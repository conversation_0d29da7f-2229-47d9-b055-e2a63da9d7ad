import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/services/bookmark_service.dart';

/// 书签管理页面
class BookmarksPage extends StatefulWidget {
  const BookmarksPage({super.key});

  @override
  State<BookmarksPage> createState() => _BookmarksPageState();
}

class _BookmarksPageState extends State<BookmarksPage> {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  List<BookmarkItem> _bookmarks = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBookmarks();
  }

  /// 加载所有书签
  Future<void> _loadBookmarks() async {
    try {
      final bookmarks = await BookmarkService.instance.loadBookmarks();

      setState(() {
        _bookmarks = bookmarks;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading bookmarks: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 删除书签（带动画）
  Future<void> _deleteBookmark(BookmarkItem bookmark) async {
    try {
      // 找到要删除的项目索引
      final index = _bookmarks.indexWhere((item) => item.url == bookmark.url);
      if (index == -1) return;

      // 从本地列表中移除
      final removedItem = _bookmarks.removeAt(index);

      // 执行动画移除
      _listKey.currentState?.removeItem(
        index,
        (context, animation) => SlideTransition(
          position: animation.drive(
            Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).chain(CurveTween(curve: Curves.easeIn)),
          ),
          child: FadeTransition(
            opacity: animation,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: _buildBookmarkItem(removedItem),
            ),
          ),
        ),
        duration: const Duration(milliseconds: 300),
      );

      // 从持久化存储中删除
      await BookmarkService.instance.deleteBookmark(bookmark.url);

      ToastWidget.show(AppLocalizations.of(context)!.bookmarkDeleted);
      debugPrint('书签已删除: ${bookmark.title}');
    } catch (e) {
      debugPrint('删除书签失败: $e');
      // 如果删除失败，重新加载书签
      _loadBookmarks();
    }
  }

  /// 导航到URL
  void _navigateToUrl(String url) {
    debugPrint('从书签返回URL给浏览器: $url');
    // 将选中的URL返回给调用者（通常是浏览器页面）
    Navigator.of(context).pop(url);
  }

  /// 构建单个书签项目
  Widget _buildBookmarkItem(BookmarkItem bookmark) {
    final themeColors = ThemeManager.currentTheme;

    return GestureDetector(
      onTap: () => _navigateToUrl(bookmark.url),
      child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: themeColors.mineButtonBackgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // 网站图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: themeColors.backgroundColor,
            ),
            child: ClipOval(
              child: BookmarkService.instance.buildBookmarkIcon(
                bookmark.title,
                bookmark.url,
                bookmark.favicon,
                40,
                faviconCached: bookmark.faviconCached,
              ),
            ),
          ),
          const SizedBox(width: 16),

          // 网站信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 网站标题
                Text(
                  bookmark.title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: themeColors.textColor,
                  ),
                ),
                const SizedBox(height: 2),
                // 网站URL
                Text(
                  bookmark.url,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 11,
                    color: themeColors.textColor.withValues(alpha: 0.5),
                  ),
                ),
                const SizedBox(height: 4),
                // 添加时间
                Text(
                  _formatTimestamp(context, bookmark.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: themeColors.textColor.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),

          // 删除按钮
          IconButton(
            icon: Icon(
              Icons.delete_outline,
              color: themeColors.textColor.withValues(alpha: 0.6),
              size: 20,
            ),
            onPressed: () => _deleteBookmark(bookmark),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ),
      ),
    );
  }



  /// 格式化时间戳
  String _formatTimestamp(BuildContext context, int timestamp) {
    final now = DateTime.now();
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return AppLocalizations.of(context)!.justNow;
    } else if (difference.inHours < 1) {
      return AppLocalizations.of(context)!.minutesAgo(difference.inMinutes);
    } else if (difference.inDays < 1) {
      return AppLocalizations.of(context)!.hoursAgo(difference.inHours);
    } else {
      return AppLocalizations.of(context)!.daysAgo(difference.inDays);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: ThemeManager(),
      child: Consumer<ThemeManager>(
        builder: (context, themeManager, child) {
          final themeColors = ThemeManager.currentTheme;

          return Scaffold(
            backgroundColor: themeColors.backgroundColor,
            appBar: AppBar(
              backgroundColor: themeColors.backgroundColor,
              elevation: 0,
              leading: IconButton(
                icon: Icon(Icons.arrow_back_ios, color: themeColors.textColor),
                onPressed: () => Navigator.of(context).pop(),
              ),
              title: Text(
                AppLocalizations.of(context)!.allBookmarks,
                style: TextStyle(
                  color: themeColors.textColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            body: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _bookmarks.isEmpty
                    ? Center(
                        child: Text(
                          AppLocalizations.of(context)!.noBookmarks,
                          style: TextStyle(
                            fontSize: 16,
                            color: themeColors.textColor.withValues(alpha: 0.6),
                          ),
                        ),
                      )
                    : Padding(
                        padding: const EdgeInsets.all(16),
                        child: AnimatedList(
                          key: _listKey,
                          initialItemCount: _bookmarks.length,
                          itemBuilder: (context, index, animation) {
                            if (index >= _bookmarks.length) {
                              return const SizedBox.shrink();
                            }
                            return SlideTransition(
                              position: animation.drive(
                                Tween<Offset>(
                                  begin: const Offset(1.0, 0.0),
                                  end: Offset.zero,
                                ).chain(CurveTween(curve: Curves.easeOut)),
                              ),
                              child: FadeTransition(
                                opacity: animation,
                                child: Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  child: _buildBookmarkItem(_bookmarks[index]),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
          );
        },
      ),
    );
  }
}


