import 'package:flutter/foundation.dart';
import 'package:imtrans/services/appsflyer_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// AppsFlyer Diagnostics for Conversion Data Issues
/// 
/// Helps diagnose why conversion data (conversions.appsflyersdk) is not appearing
class AppsFlyerDiagnostics {
  
  /// Run comprehensive diagnostics for conversion data issues
  static Future<void> runConversionDataDiagnostics() async {
    debugPrint('🔍 === AppsFlyer Conversion Data Diagnostics ===');
    
    // 1. Check if Apps<PERSON>lyer is initialized
    await _checkInitialization();
    
    // 2. Check if this is first app launch
    await _checkFirstLaunch();
    
    // 3. Check AppsFlyer UID
    await _checkAppsFlyerUID();
    
    // 4. Check configuration
    await _checkConfiguration();
    
    // 5. Provide troubleshooting steps
    _provideTroubleshootingSteps();
    
    debugPrint('🔍 === Diagnostics Complete ===');
  }
  
  /// Check AppsFlyer initialization status
  static Future<void> _checkInitialization() async {
    debugPrint('📋 1. Checking AppsFlyer Initialization...');
    
    if (AppsFlyerService.isInitialized) {
      debugPrint('   ✅ AppsFlyer is initialized');
    } else {
      debugPrint('   ❌ AppsFlyer is NOT initialized');
      debugPrint('   💡 Solution: Ensure AppsFlyerService.initialize() is called in main.dart');
      return;
    }
  }
  
  /// Check if this is the first app launch
  static Future<void> _checkFirstLaunch() async {
    debugPrint('📋 2. Checking First Launch Status...');
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = !prefs.containsKey('appsflyer_first_launch_done');
      
      if (isFirstLaunch) {
        debugPrint('   ✅ This appears to be FIRST app launch');
        debugPrint('   🔍 conversions.appsflyersdk should appear in logs');
        
        // Mark first launch as done
        await prefs.setBool('appsflyer_first_launch_done', true);
      } else {
        debugPrint('   ⚠️  This is NOT the first app launch');
        debugPrint('   📝 Conversion data only appears on FIRST launch');
        debugPrint('   💡 To test: Uninstall app completely and reinstall');
      }
    } catch (e) {
      debugPrint('   ❌ Error checking first launch: $e');
    }
  }
  
  /// Check AppsFlyer UID
  static Future<void> _checkAppsFlyerUID() async {
    debugPrint('📋 3. Checking AppsFlyer UID...');
    
    try {
      final uid = await AppsFlyerService.getAppsFlyerUID();
      if (uid != null && uid.isNotEmpty) {
        debugPrint('   ✅ AppsFlyer UID: $uid');
        debugPrint('   🔍 conversions.appsflyersdk - Install UID: $uid');
      } else {
        debugPrint('   ❌ AppsFlyer UID is null or empty');
        debugPrint('   💡 This indicates SDK initialization issues');
      }
    } catch (e) {
      debugPrint('   ❌ Error getting AppsFlyer UID: $e');
    }
  }
  
  /// Check AppsFlyer configuration
  static Future<void> _checkConfiguration() async {
    debugPrint('📋 4. Checking Configuration...');
    
    // Note: We can't access private constants directly, so we'll check indirectly
    if (AppsFlyerService.isInitialized) {
      debugPrint('   ✅ SDK initialized successfully (dev key and app ID are valid)');
    } else {
      debugPrint('   ❌ SDK not initialized (check dev key and app ID)');
    }
    
    debugPrint('   📝 Ensure the following are configured:');
    debugPrint('      - Dev Key in AppsFlyerService');
    debugPrint('      - iOS App ID (numeric only, no "id" prefix)');
    debugPrint('      - Android manifest permissions');
    debugPrint('      - iOS Info.plist configuration');
  }
  
  /// Provide troubleshooting steps
  static void _provideTroubleshootingSteps() {
    debugPrint('📋 5. Troubleshooting Steps:');
    debugPrint('');
    debugPrint('   🔍 Why conversion data might not appear:');
    debugPrint('   1. NOT first app launch (most common reason)');
    debugPrint('   2. SDK not properly initialized');
    debugPrint('   3. Invalid dev key or app ID');
    debugPrint('   4. Network connectivity issues');
    debugPrint('   5. App installed from development build (not store)');
    debugPrint('');
    debugPrint('   💡 Solutions:');
    debugPrint('   1. Completely uninstall app and reinstall');
    debugPrint('   2. Check dev key and app ID configuration');
    debugPrint('   3. Ensure network connectivity');
    debugPrint('   4. Test with release build');
    debugPrint('   5. Check AppsFlyer dashboard for events');
    debugPrint('');
    debugPrint('   🔍 What to look for in logs:');
    debugPrint('   - "conversions.appsflyersdk" messages');
    debugPrint('   - "AppsFlyer Install Conversion Data Received"');
    debugPrint('   - AppsFlyer UID values');
  }
  
  /// Reset first launch flag for testing
  static Future<void> resetFirstLaunchFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('appsflyer_first_launch_done');
      debugPrint('✅ First launch flag reset - next app start will be treated as first launch');
    } catch (e) {
      debugPrint('❌ Error resetting first launch flag: $e');
    }
  }
  
  /// Force trigger conversion data check
  static Future<void> forceTriggerConversionData() async {
    debugPrint('🔄 Force triggering conversion data check...');
    
    if (!AppsFlyerService.isInitialized) {
      debugPrint('❌ AppsFlyer not initialized');
      return;
    }
    
    // Get UID to trigger any pending callbacks
    await AppsFlyerService.getAppsFlyerUID();
    
    // Log a test event to ensure SDK is working
    await AppsFlyerService.logEvent('diagnostic_test', {
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
      'test_type': 'conversion_data_check',
    });
    
    debugPrint('✅ Conversion data check triggered');
    debugPrint('🔍 Watch logs for "conversions.appsflyersdk" messages');
  }
  
  /// Check if app was installed organically or through campaign
  static Future<void> checkInstallSource() async {
    debugPrint('📋 Checking Install Source...');
    
    // This information would come from conversion data callback
    // For now, we can only check if we have an AppsFlyer UID
    final uid = await AppsFlyerService.getAppsFlyerUID();
    
    if (uid != null) {
      debugPrint('   ✅ App has AppsFlyer UID: $uid');
      debugPrint('   📝 Install source data should appear in conversion callback');
    } else {
      debugPrint('   ❌ No AppsFlyer UID available');
    }
  }
  
  /// Print detailed diagnostic report
  static Future<void> printDetailedReport() async {
    debugPrint('');
    debugPrint('🔍 ========================================');
    debugPrint('🔍 APPSFLYER CONVERSION DATA DIAGNOSTIC REPORT');
    debugPrint('🔍 ========================================');
    debugPrint('');
    
    await runConversionDataDiagnostics();
    
    debugPrint('');
    debugPrint('🔍 ========================================');
    debugPrint('🔍 END OF DIAGNOSTIC REPORT');
    debugPrint('🔍 ========================================');
    debugPrint('');
  }
}
