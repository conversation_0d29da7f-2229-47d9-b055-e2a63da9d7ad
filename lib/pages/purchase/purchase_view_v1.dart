import 'package:flutter/material.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:imtrans/pages/settings/privacy.dart';
import 'package:imtrans/pages/settings/ua.dart';
// import '../../services/account.dart';
import '../../services/iap_purchases.dart';
import '../../util/util.dart';

class PurchaseViewV1 extends StatefulWidget {
  /// 获取该视图的背景颜色
  static Color getBackgroundColor() {
    return const Color(0xffcdee2d);
  }
  final List products;
  final String? userUuid;
  final Function(String?) onBuyFailed;
  final Function() onBuySucceed;
  
  const PurchaseViewV1({
    super.key,
    required this.products,
    required this.userUuid,
    required this.onBuyFailed,
    required this.onBuySucceed,
  });
  
  @override
  State<PurchaseViewV1> createState() => _PurchaseViewV1State();
}

class _PurchaseViewV1State extends State<PurchaseViewV1> {
  var _index = 0;
  bool _isLoading = false;
  
  Widget createPayItem(width, index) {
    if(index >= widget.products.length) {
      return Container();
    }
    var item = widget.products[index];
    var isMonth = item.title.toLowerCase().contains("month");
    String name = isMonth ? "Monthly" : "Annual";
    String price = item.price;
    String currencySymbol = item.currencySymbol;
    String pricePerDay = currencySymbol + (item.rawPrice / 365).toStringAsFixed(2);
    
    String discountPrice = price;
    if (item is AppStoreProductDetails) {
      final skProduct = item.skProduct;
      // 检查是否有促销价格
      if (skProduct.introductoryPrice != null) {
        discountPrice = skProduct.introductoryPrice!.price;
      } else if (skProduct.discounts.isNotEmpty) {
        // 如果有折扣，使用第一个折扣价格
        discountPrice = skProduct.discounts.first.price;
      }
    }
    if (!discountPrice.startsWith(currencySymbol)) {
      discountPrice = '$currencySymbol$discountPrice';
    }

    Color bColor = index == _index ? const Color(0x26cdee2d) : const Color(0xfff6f6f6);
    Border border = index == _index 
      ? Border.all(color: const Color(0xffc4d961), width: 1) 
      : Border.all(color: const Color(0xfff6f6f6), width: 1);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _index = index;
        });
      },
      child: Container(
        width: width,
        height: 90,
        clipBehavior: Clip.hardEdge,
        padding: const EdgeInsets.symmetric(horizontal: 17),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: bColor,
            border: border
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: SizedBox(
                height: 70,
                child: Material(
                  color: Colors.transparent,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(name, 
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: Color(0xff1b1c1e)
                        ),
                      ),
                      const Spacer(flex: 1),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text(discountPrice, style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xff1b1c1e)
                                ),
                              ),
                              const SizedBox(width: 10),
                              if (price != discountPrice) Text(price,
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xffb2b6c2),
                                  decoration: TextDecoration.lineThrough,
                                  decorationColor: Color(0xffb2b6c2),
                                ),
                              ),
                            ],
                          ),
                          if (!isMonth) Text(
                            "Only $pricePerDay per day",
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Color(0xffb2b6c2)
                            ),
                          ),
                          if (isMonth) Container(
                            width: 94,
                            height: 32,
                            margin: const EdgeInsets.only(left: 10),
                            child: const Image(
                              image: AssetImage("images/pay/viptipbtn.png"),
                              fit: BoxFit.contain,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(flex: 5),
                      if (isMonth) Text(
                        "First month $discountPrice then $price",
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0x881b1c1e)
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget createPayButton(width) {
    String buttonImage = "images/pay/vippay1.png";
    // Color color = Colors.black;

    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          GestureDetector(
            onTap: _isLoading ? null : () async {
              if (_isLoading) return;
              
              setState(() {
                _isLoading = true;
              });

              try {
                if (widget.userUuid == null) {
                  throw Exception('Account error');
                }

                if (widget.products.isEmpty || _index >= widget.products.length) {
                  throw Exception('No products available');
                }
              
                final selectedProductId = widget.products[_index].id;
                await IAPPurchases().buyProduct(
                  selectedProductId,
                  uuid: widget.userUuid!,
                  onSuccess: () {
                    setState(() {
                      _isLoading = false;
                    });
                    widget.onBuySucceed();
                  },
                  onError: (error) {
                    setState(() {
                      _isLoading = false;
                    });
                    widget.onBuyFailed(error);
                  },
                  onCancel: () {
                    setState(() {
                      _isLoading = false;
                    });
                    // 用户取消购买，不显示错误提示，只重置loading状态
                  },
                );
              } catch (e) {
                setState(() {
                  _isLoading = false;
                });
                widget.onBuyFailed(e.toString());
              }
            },
            child: Container(
              width: width,
              height: 56,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(buttonImage),
                  fit: BoxFit.fitWidth,
                )
              ),
              child: !_isLoading ? const Text(
                "Continue",
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.bold
                ),
              ) : Container(),
            ),
          ),
          if (_isLoading)
            Positioned.fill(
              child: Container(
                color: Colors.black26,
                child: const Center(
                  child: SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget createTip(width) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: width,
        alignment: Alignment.center,
        child: const Text("Automatically renew, cancel at any time", style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: Color(0xff939393)
        ),),
      ),
    );
  }

  Widget createTop(width) {
    double imageW = width;
    double topH = 300;
    double bt = 40;
    Alignment align = Alignment.bottomCenter;
    
    return Container(
      width: width,
      height: topH,
      alignment: align,
      padding: EdgeInsets.only(bottom: bt),
      child: Image(image: const AssetImage("images/pay/bg01.png"), width: imageW, fit: BoxFit.fitWidth,),
    );
  }

  Widget createPPUA() {
    return Material(
      color: Colors.transparent,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            onPressed: () {
              Util.navigatorTransparentPush(const Privacy());
            },
            child: const Text("Privacy Policy", style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Color(0xff4589ff)
            ),),
          ),
          Container(
              width: 1,
              height: 14,
              color: const Color(0xff4589ff)
          ),
          TextButton(
            onPressed: () {
              Util.navigatorTransparentPush(const UA());
            },
            child: const Text("User Agreement", style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Color(0xff4589ff)
            ),),
          )
        ],
      ),
    );
  }

  Widget createCenter(width, height) {
    double centerH = height - 300;
    double offsetTop = 35;

    return Container(
      width: width,
      constraints: BoxConstraints(
        minHeight: centerH
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(50)),
        boxShadow: [
          BoxShadow(
            color: Colors.white,
            blurRadius: 10
          )
        ]
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: offsetTop,),
          // 使用循环创建支付项目
          ...widget.products.asMap().entries.map((entry) {
            int idx = entry.key;
            return Column(
              children: [
                createPayItem(width * 0.82, idx),
                SizedBox(height: idx < widget.products.length - 1 ? 19 : 33),
              ],
            );
          }),
          createPayButton(width * 0.82),
          const SizedBox(height: 10,),
          createTip(width * 0.82),
          const SizedBox(height: 10,),
          createPPUA(),
          const SizedBox(height: 10,),
        ],
      )
    );
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;
    
    return Stack(
      children: [
        Positioned(
          top: 0,
          height: height,
          child: SingleChildScrollView(
            child: Column(
              children: [
                createTop(width),
                createCenter(width, height),
              ],
            ),
          ),
        ),
      ],
    );
  }
}