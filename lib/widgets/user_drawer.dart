import 'package:flutter/material.dart';
import 'package:imtrans/models/product_model.dart';
import 'package:imtrans/services/account.dart';
import 'package:imtrans/util/dialog_manager.dart';
import 'package:imtrans/util/localizations_extension.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/util.dart';
import 'package:imtrans/util/loading_manager.dart';
import 'package:imtrans/pages/settings/feedback.dart';
import 'package:imtrans/pages/settings/theme.dart';
import 'package:imtrans/pages/settings/browser_settings.dart';
import 'package:imtrans/pages/signin.dart';
import 'package:imtrans/pages/purchase/vip.dart';
import 'package:imtrans/controllers/product_controller.dart';
import 'package:imtrans/controllers/draft_controller.dart';
import 'package:imtrans/pages/settings/language.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/widgets/common/image_widget.dart';

class UserDrawer extends StatefulWidget {
  const UserDrawer({super.key});

  @override
  State<UserDrawer> createState() => _UserDrawerState();
}

class _UserDrawerState extends State<UserDrawer> {
  Future<Account> _accountFuture = Account.instance;
  final ProductController _productController = ProductController.instance;
  // 主题监听器变量
  late VoidCallback _themeListener;
  String _proBannerBgNo = '01';

  @override
  void initState() {
    super.initState();
    _refreshAccountInfo();
    _themeListener = () {
      if (mounted) {
        setState(() {}); 
      }
    };
    // 主题变化监听器
    ThemeManager().addListener(_themeListener);
    _proBannerBgNo = ((DateTime.now().day % 3) + 2).toString().padLeft(2, '0');
  }

  @override
  void dispose() {
    ThemeManager().removeListener(_themeListener); 
    super.dispose();
  }

  // 刷新账户信息
  Future<void> _refreshAccountInfo() async {
    try {
      Account account = await Account.instance;
      await account.reloadInfo();
      _accountFuture = Account.instance;
    } catch (e) {
      debugPrint('Error refreshing account info: $e');
    }
  }

  // 登录方法
  Future<void> _signIn() async {
    Util.navigatorPush(SignIn(onSelected: (String signinType) async {
      if (!mounted) return;
      
      setState(() {
        _accountFuture = Account.instance;
      });
      
      try {
        await _productController.refresh();
      } catch (e) {
        debugPrint('Error refreshing products: $e');
      }
    }));
  }

  // 注销方法
  Future<void> _signOut() async {
    // Navigator.pop(context); // 关闭抽屉
    try {
      Account account = await _accountFuture;
      // 使用 LoadingManager 显示 loading
      LoadingManager.instance.show(context);
      await account.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
    } finally {
      LoadingManager.instance.hide(context);
      if (mounted) {
        setState(() {
          _accountFuture = Account.instance;
        });
      }
      
      await _productController.refresh();
    }
  }

  // Pro banner的功能说明
  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 3,
            height: 8,
            margin: const EdgeInsets.only(top: 6),
            decoration: const BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            constraints: const BoxConstraints(maxWidth: 200), 
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xff1b1c1e),
                fontFamily: "Poppins-medium",
                height: 13/8, // 18px line height for 10px font
              ),
              overflow: TextOverflow.visible, // 允许文本溢出
              softWrap: false, // 禁止自动换行
            ),
          ),
        ],
      ),
    );
  }
    

  // 删除账户方法
  Future<void> _deleteAccount() async {
    // Navigator.pop(context); // 关闭抽屉
    final BuildContext currentContext = context;
    final l10n = AppLocalizations.of(currentContext)!;
    
    DialogManager.instance.showDialog(
      title: l10n.deleteAccount,
      message: l10n.deleteAccountWarning,
      onConfirm: () async {
        LoadingManager.instance.show(currentContext);
        
        try {
          // 清空草稿
          DraftController draftController = DraftController();
          await draftController.initialized; 
          await draftController.clearAllDrafts();
          
          // 清空产品
          ProductModel productModel = ProductModel();
          await productModel.clearCache();
          
          // 清空账号
          Account account = await _accountFuture;
          await account.delete();
          
          // 重置账户状态
          if (mounted) {
            setState(() {
              _accountFuture = Account.instance;
            });
            
            await _productController.refresh();
            LoadingManager.instance.hide(currentContext);
            
            DialogManager.instance.showConfirmDialog(
              title: l10n.done,
              message: l10n.deleteDataSuccess,
              onConfirm: () async {
                if (mounted) {
                  // 删除后返回主屏幕
                  Navigator.of(currentContext).pushNamedAndRemoveUntil('/', (route) => false);
                }
              }
            );
          } else {
            LoadingManager.instance.hide(currentContext);
          }
        } catch (e) {
          debugPrint('Error deleting account: $e');
          // 确保在组件仍然挂载时才关闭loading
          if (mounted) {
            LoadingManager.instance.hide(currentContext);
          }
        }
      },
      confirmText: l10n.confirm,
      onCancel: () {
        // 取消操作
      },
      cancelText: l10n.cancel
    );
  }
  
  // 创建菜单项的公共方法
  Widget _buildMenuItem({
    required dynamic icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    Widget? trailing,
    Color? textColor,
  }) {
    final defaultTextStyle = TextStyle(
      color: ThemeManager.currentTheme.textColor,
      fontSize: 15,
      fontWeight: FontWeight.w500,
      fontFamily: "Poppins-medium",
      letterSpacing: 0,
    );
    final style = textColor != null ? defaultTextStyle.copyWith(color: textColor) : defaultTextStyle;
    
    return Container(
      height: 70, // 设置整体高度
      child: ListTile(
        contentPadding: const EdgeInsets.only(left: 8, right: 22, top: 0, bottom: 0), 
        // minLeadingWidth: 36, // 图标的最小宽度
        horizontalTitleGap: 2, // 文本与图标之间的间距
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: icon is IconData ? BoxDecoration(
            shape: BoxShape.circle,
            color: iconColor,
          ) : null,
          child: icon is IconData 
              ? Icon(icon, color: Colors.white, size: 20)
              : Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    color: iconColor,
                    shape: BoxShape.circle,
                  ),
                  child: icon,
                ),
        ),
        title: Text(title, style: style),
        trailing: trailing ?? SizedBox(width: 2), //Image.asset('images/icons/icon_next.png', width: 16, height: 16)"",
        onTap: onTap,
      ),
    );
  }

  Widget _buildGetProButton(String text, {double? width}) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Container(
        width: width == null ? 80 : width,
        height: 35,
        margin: const EdgeInsets.only(top: 0), 
        child: ElevatedButton(
          onPressed: () {
            Util.navigatorPush(const Vip());
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xff1b1c1e),
            foregroundColor: const Color(0xFFFdfdfd),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(60),
            ),
            padding: EdgeInsets.zero,
          ),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: Color(0xFFFdfdfd),
              fontFamily: "inter",
              letterSpacing: 0, // 0.5% letter spacing
              height: 20/12, // 20px line height for 12px font
            ),
          ),
        ),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Drawer(
      backgroundColor: ThemeManager.currentTheme.drawerBackgroundColor,
      child: FutureBuilder<Account>(
        future: _accountFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          final account = snapshot.data!;
          return Column(
            children: [
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    // 用户信息头部
                    Container(
                      padding: const EdgeInsets.only(top: 66, bottom: 10),
                      
                      child: Row(
                        children: [
                          const SizedBox(width: 20),
                          // 应用图标和名称
                          Row(
                            children: [
                              Image.asset('images/logo.png', width: 40, height: 40),
                              const SizedBox(width: 10),
                              RichText(
                                textAlign: TextAlign.left,
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'Imtrans',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: ThemeManager.currentTheme.textColor,
                                        height: 1.2, // 修改行高为1.0以实现垂直居中
                                        letterSpacing: 0, 
                                        fontFamily: "Poppins-medium",
                                      ),
                                    ),
                                    // TextSpan(
                                    //   text: l10n.appSubtitle,
                                    //   style: TextStyle(
                                    //     fontSize: 12,
                                    //     color: Colors.grey,
                                    //     height: 1.2,
                                    //   ),
                                    // ),
                                  ],
                                ),
                              ),
                              // 如果是Pro用户，显示Pro图标
                              if (account.isVip)
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: ImageWidget.proIcon(),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    // PRO信息卡片 
                    if (!account.isVip)
                      GestureDetector(
                        onTap: () {
                          if (!account.isVip) {
                            // Navigator.pop(context);
                            Util.navigatorPush(const Vip());
                          }
                        },
                        // child: ClipRRect(
                          // borderRadius: BorderRadius.circular(8),
                        child: Container(
                          width: 280,
                          height: 176,
                          // margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              // 随机选择一张背景图片01-04
                              image: AssetImage('images/pay/banner_bg${_proBannerBgNo}.png'),
                              fit: BoxFit.fill,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: .1),
                                blurRadius: 100,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // PRO标题
                              Container(
                                height: 40, // 设置固定高度为30px
                                padding: const EdgeInsets.only(left: 10, right: 10, top: 0, bottom: 4),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween, // 两端对齐
                                  children: [
                                    Text(
                                      'PRO',
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.w900,
                                        color: const Color(0xFF1b1c1e),
                                        fontFamily: "Poppins-BoldItalic",
                                        // letterSpacing: 0.005, // 0.5% letter spacing
                                        // height: 30/16, // 设置行高为30px
                                      ),
                                    ),
                                    // 标题右侧的pro按钮
                                    if (AppLocalizations.of(context)!.isLongText(Localizations.localeOf(context).languageCode))
                                      _buildGetProButton(l10n.getPro, width: 120), 
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              // 功能列表
                              Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        _buildFeatureItem(l10n.unlockFileUpload),
                                        _buildFeatureItem(l10n.highQualityTranslation),
                                        _buildFeatureItem(l10n.adFreeExperience),
                                        SizedBox(height: 16)
                                      ],
                                    ),
                                  ),
                                  // 购买按钮
                                  if (!AppLocalizations.of(context)!.isLongText(Localizations.localeOf(context).languageCode))
                                    
                                    Padding(
                                      padding: const EdgeInsets.only(top: 30.0),
                                      child: _buildGetProButton(l10n.getPro),
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      )
                    else 
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                        child: Image.asset('images/pay/banner_pro02.png'),
                      ),

                    const SizedBox(height: 8),
                    // 菜单项目
                    _buildMenuItem(
                      icon: const Icon(Icons.language_rounded, color: Colors.white, size: 20),
                      iconColor: Color(0xffFBB11B),
                      title: l10n.language,
                      onTap: () {
                        // Navigator.pop(context); // 关闭抽屉
                        Util.navigatorPush(const AppLanguagePage());
                      },
                    ),
                    
                    _buildMenuItem(
                      icon: Image.asset(ThemeManager.getImagePath('icon_darkmode')),
                      title: l10n.darkMode,
                      onTap: () {
                        // Navigator.pop(context); // 关闭抽屉
                        Util.navigatorPush(const ThemePage());
                      },
                    ),

                    _buildMenuItem(
                      icon: Image.asset(ThemeManager.getImagePath('icon_contact')),
                      title: l10n.contactUs,
                      onTap: () {
                        // Navigator.pop(context);
                        Util.navigatorPush(const FeedbackPage());
                      },
                      // trailing: Icon(Icons.chevron_right, size: 20, color: Colors.grey.shade400),
                    ),
                    
                    _buildMenuItem(
                      icon: const Icon(Icons.public, color: Colors.white, size: 20),
                      iconColor: Color(0xff3498DB),
                      title: l10n.browserSettings,
                      onTap: () {
                        Util.navigatorPush(const BrowserSettingsPage());
                      },
                    ),
                    
                    // 删除数据按钮
                    if (account.isSignedIn())
                      _buildMenuItem(
                        icon: Image.asset("images/icons/icon_delete_account.png"),
                        trailing: SizedBox(width: 2),
                        title: l10n.deleteData,
                        onTap: _deleteAccount,
                      ),
                  ],
                ),
              ),
              
              // 底部登录按钮
              if (!account.isSignedIn())
                Column(
                  children: [
                    SizedBox(height: 10), // 减小顶部间距
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8), // 增大水平方向的内边距
                      child: ElevatedButton(
                        onPressed: _signIn,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ThemeManager.currentTheme.logoColor,
                          foregroundColor: Colors.black,
                          minimumSize: Size(double.infinity, 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          l10n.signIn,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 80), 
                  ],
                ),
              // 已登录状态显示登出按钮
              if (account.isSignedIn())
                Column(
                  children: [
                    SizedBox(height: 10), // 减小顶部间距
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8), // 增大水平方向的内边距
                      child: OutlinedButton(
                        onPressed: _signOut,
                        style: OutlinedButton.styleFrom(
                          backgroundColor: ThemeManager.currentTheme.signOutButtonBgColor,
                          foregroundColor: Color(0xff1b1c1e),
                          minimumSize: Size(double.infinity, 50),
                          side: BorderSide(color: Color(0xff1b1c1e).withValues(alpha: .1), width: 2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(
                          l10n.signOut,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 80), // 底部留一些空间
                  ],
                ),
            ],
          );
        },
      ),
    );
  }
}