import 'dart:io';
import 'package:flutter/material.dart';
import '../util/theme_manager.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

// 图片相关的通用widgets
@deprecated
class ProductWidget{
  // 构建占位图像的辅助方法
  static Widget placeholderImage(double width, double height) {
    // Calculate a reasonable icon size
    double iconSize = 24.0;
    if (width.isFinite && height.isFinite) {
      iconSize = (width < height ? width : height) * 0.3;
    }

    return Container(
      width: width.isFinite ? width : double.infinity,
      height: height.isFinite ? height : double.infinity,
      color: ThemeManager.currentTheme.borderAreaBgColor.withValues(alpha: .3),
      child: Center(
        child: Icon(
          Icons.image_outlined,
          size: iconSize,
          color: ThemeManager.currentTheme.textColor.withValues(alpha: .5),
        ),
      ),
    );
  }

  static Widget loadImage(BuildContext context, String imagePath, {double width = 40, double height = 40}) {
    return Container(
      width: width,
      height: height,
      child: Image.file(
        File(imagePath),
        width: width,
        height: height,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Error loading image: $error');
          return Container(
            width: width,
            height: height,
            color: Colors.grey[200],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image_rounded,
                  size: 32,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 8),
                Text(
                  AppLocalizations.of(context)!.imageNotFound,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  static Widget proIcon(){
    return Image.asset(
      'images/icons/icon_pro.png',
      height: 16,
      fit: BoxFit.contain,
    ); 
  }
}