// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart';
// import 'package:imtrans/util/global.dart';
// import 'package:imtrans/controllers/product_controller.dart';
// import 'package:imtrans/util/theme_manager.dart';
// import 'package:imtrans/util/util.dart';
// import 'package:imtrans/pages/purchase/vip.dart';
// import 'package:imtrans/services/account.dart';
// import 'package:imtrans/pages/browser/browser.dart';
// import 'package:imtrans/widgets/common/common_widgets.dart';

// class MediaPicker {
//   static Future<List<XFile>> chooseImages(ImageSource source) async {
//     final ImagePicker picker = ImagePicker();
//     try {
//       if (source == ImageSource.gallery) {
//         return await picker.pickMultiImage(
//           limit: 10,
//           requestFullMetadata: false
//         );
//       } else if (source == ImageSource.camera) {
//         final image = await picker.pickImage(source: source);
//         return image != null ? [image] : [];
//       }
//       return [];
//     } catch (e) {
//       debugPrint('Error picking images: $e');
//       return [];
//     }
//   }

//   static Future<void> showSelector({
//     required BuildContext context,
//     required Function(dynamic selection) onSelected,
//   }) async {
//     showModalBottomSheet(
//       context: context,
//       backgroundColor: Colors.transparent,
//       builder: (BuildContext context) {
//         return _MediaPicker(
//           onSelected: (source) async {
//             Navigator.of(context).pop();
//             if (source.runtimeType.toString() == "ImageSource") {
//               final images = await chooseImages(source as ImageSource);
//               onSelected(images);
//             } else {
//               FilePickerResult? result = await FilePicker.platform.pickFiles(
//                 type: FileType.custom,
//                 allowedExtensions: ['zip', 'jpg', 'jpeg', 'png', 'webp'],
//               );

//               if (result != null) {
//                 String filePath = result.files.single.path!;
//                 onSelected(filePath);
//               }
//             }
//           },
//         );
//       },
//     );
//   }
// }

// class _MediaPicker extends StatefulWidget {
//   final Function onSelected;
//   const _MediaPicker({required this.onSelected});

//   @override
//   State<_MediaPicker> createState() => _MediaPickerState();
// }

// class _MediaPickerState extends State<_MediaPicker> {
//   int? _remainingCount;

//   @override
//   void initState() {
//     super.initState();
//     _loadRemainingCount();
//   }

//   Future<void> _loadRemainingCount() async {
//     int count = 1;
//     final Account account = await Account.instance;
//     if (!account.isVip) {
//       count = await ProductController().getQuota();
//     }
//     if (mounted) {
//       setState(() {
//         _remainingCount = count;
//       });
//     }
//   }

//   Widget createItem(BuildContext context, String title) {
//     late String str;
//     if (title == "Browser") {
//       str = Global().image_camera;
//     } else if (title == "Album") {
//       str = Global().image_album;
//     } else if (title == "File") {
//       str = Global().image_zip;
//     }
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         Stack(
//           children: [
//             ButtonWidget.createImageButton(str, 60, 60, onPressed: () async {
//               final account = await Account.instance;
//               if (title == "File" && !account.isVip) {
//                 Navigator.of(context).pop();
//                 Util.navigatorPush(const Vip());
//                 return;
//               }

//               if (title == "Browser") {
//                 Navigator.of(context).pop();
//                 // Util.navigatorPush(const BrowserPage());
//                 return;
//               }
//               else {
//                 widget.onSelected(title == "File" 
//                   ? "File" : (title == "Album" ? ImageSource.gallery : ImageSource.camera)
//                 );
//               }
//             }),
//             if (title == "File")
//               Positioned(
//                 right: 0,
//                 top: 0,
//                 child: Container(
//                   padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
//                   decoration: BoxDecoration(
//                     color: Colors.amber,
//                     borderRadius: BorderRadius.circular(4),
//                   ),
//                   child: const Text(
//                     'VIP',
//                     style: TextStyle(
//                       color: Colors.white,
//                       fontSize: 10,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         ),
//         Container(
//           height: 32,
//           alignment: Alignment.center,
//           child: Text(
//             title,
//             style: TextStyle(
//               fontSize: 14,
//               fontWeight: FontWeight.w500,
//               color: ThemeManager.currentTheme.selectSourceColor
//             ),
//           ),
//         )
//       ],
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     var size = MediaQuery.of(context).size;
//     return Container(
//       width: size.width,
//       height: size.height * 0.25,
//       decoration: BoxDecoration(
//         color: ThemeManager.currentTheme.bottomBarBackgroundColor,
//         borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
//         boxShadow: [
//           BoxShadow(
//             blurRadius: 10,
//             color: ThemeManager.currentTheme.shadowColor,
//           )
//         ]
//       ),
//       child: Column(
//         children: [
//           FutureBuilder<Account>(
//             future: Account.instance,
//             builder: (context, snapshot) {
//               if (snapshot.hasData && snapshot.data!.isVip) {
//                 return Padding(
//                   padding: const EdgeInsets.only(top: 26.0),
//                   child: Text(
//                     'Use the File button to process ZIP files',
//                     style: TextStyle(
//                       fontSize: 14,
//                       // color: Global().selectSourceColor.withAlpha(180),
//                       fontWeight: FontWeight.w400,
//                     ),
//                   ),
//                 );
//               }
//               return Padding(
//                 padding: const EdgeInsets.only(top: 26.0),
//                 child: Column(
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Text(
//                           'Images remaining today: ',
//                           style: TextStyle(
//                             fontSize: 14,
//                             color: ThemeManager.currentTheme.selectSourceColor,
//                             fontWeight: FontWeight.w500,
//                           ),
//                         ),
//                         _remainingCount != null
//                             ? Text(
//                                 '$_remainingCount',
//                                 style: TextStyle(
//                                   fontSize: 14,
//                                   color: ThemeManager.currentTheme.selectSourceColor,
//                                   fontWeight: FontWeight.w500,
//                                 ),
//                               )
//                             : SizedBox(
//                                 width: 10,
//                                 height: 10,
//                                 child: CircularProgressIndicator(
//                                   strokeWidth: 1,
//                                   valueColor: AlwaysStoppedAnimation<Color>(
//                                     ThemeManager.currentTheme.selectSourceColor,
//                                   ),
//                                 ),
//                               ),
//                       ],
//                     ),
//                     const SizedBox(height: 4),
//                     GestureDetector(
//                       onTap: () {
//                         Navigator.of(context).pop();
//                         Util.navigatorPush(const Vip());
//                       },
//                       child: Text(
//                         'Become VIP for unlimited access & ZIP file support',
//                         style: TextStyle(
//                           fontSize: 12,
//                           color: ThemeManager.currentTheme.selectSourceColor.withAlpha(150),
//                           fontWeight: FontWeight.w400,
//                           decoration: TextDecoration.underline,
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             },
//           ),
//           Expanded(
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: [
//                 createItem(context, "Browser"),
//                 createItem(context, "Album"),
//                 createItem(context, "File"),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// } 