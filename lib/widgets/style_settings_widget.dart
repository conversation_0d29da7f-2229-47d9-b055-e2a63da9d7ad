import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/text_overlay_settings.dart';
import 'package:imtrans/widgets/font_settings_widget.dart';

/// 综合样式设置组件
/// 
/// 包含所有文本样式相关的设置：字体、颜色、描边、阴影等
class StyleSettingsWidget extends StatelessWidget {
  final TextOverlaySettings settings;
  final VoidCallback? onChanged;

  const StyleSettingsWidget({
    Key? key,
    required this.settings,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          // Tab bar with reset button
          Row(
            children: [
              // Constrained TabBar to make tabs even narrower
              Flexible(
                flex: 3, // Takes 2/3 of available space (reduced from 3/4)
                child: TabBar(
                  indicator: _RoundedUnderlineTabIndicator(
                    borderSide: BorderSide(
                      color: ThemeManager.currentTheme.logoColor, // 绿色下划线
                      width: 4.0, // Exactly 4px height
                    ),
                    borderRadius: const BorderRadius.all(Radius.circular(10)), // 10px 圆角
                    insets: const EdgeInsets.symmetric(horizontal: 16.0),
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  dividerColor: Colors.transparent,
                  labelColor: ThemeManager.currentTheme.textColor, // 选中时字体颜色加深
                  unselectedLabelColor: ThemeManager.currentTheme.textColor.withValues(alpha: 0.5), // 未选中字体颜色稍浅
                  labelStyle: const TextStyle(
                    fontSize: 14,
                    fontFamily: 'Poppins-Medium', // 未选中时使用 SemiBold
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontSize: 14,
                    fontFamily: 'Poppins-SemiBold', // 选中时
                  ),
                  tabs: const [
                    Tab(text: 'Font'),
                    Tab(text: 'Stroke'),
                    Tab(text: 'Shadow'),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // Larger Reset button
              GestureDetector(
                onTap: () {
                  settings.resetToDefaults();
                  onChanged?.call();
                },
                child: Container(
                  width: 80, // Increased from 40
                  height: 39, // Increased from 40
                  child: Icon(
                    Icons.refresh,
                    color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                    size: 24, // Increased from 20
                  ),
                ),
              ),
            ],
          ),
          // Horizontal divider - seamlessly connected to TabBar indicator
          Container(
            margin: const EdgeInsets.only(bottom: 8),
            height: 2,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              children: [
                _buildFontTab(),
                _buildStrokeTab(),
                _buildShadowTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 字体设置标签页
  Widget _buildFontTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Font size slider
          StyleSliderWidget(
            value: settings.fontSize,
            min: 0.5,
            max: 2.0,
            divisions: 15,
            onChanged: (value) {
              settings.setFontSize(value);
              onChanged?.call();
            },
            displayValue: '${(settings.fontSize * 100).round()}',
            isFontSizeSlider: true,
          ),
          const SizedBox(height: 20),
          
          // Font family and weight settings
          FontSettingsWidget(
            settings: settings,
            onChanged: onChanged,
          ),
          const SizedBox(height: 30),

          // Font color picker
          StyleColorPicker(
            currentColor: settings.textColor,
            onColorChanged: (color) {
              settings.setTextColor(color);
              onChanged?.call();
            },
          ),
        ],
      ),
    );
  }

  /// 描边设置标签页
  Widget _buildStrokeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stroke width slider
          StyleSliderWidget(
            value: settings.strokeWidth,
            min: 0.0,
            max: 10.0,
            divisions: 20,
            onChanged: (value) {
              settings.setStrokeWidth(value);
              onChanged?.call();
            },
            displayValue: settings.strokeWidth.toStringAsFixed(1),
            trackLabel: 'Width',
          ),
          const SizedBox(height: 70),

          // Stroke color picker
          StyleColorPicker(
            currentColor: settings.strokeColor,
            onColorChanged: (color) {
              settings.setStrokeColor(color);
              onChanged?.call();
            },
          ),
        ],
      ),
    );
  }

  /// 阴影设置标签页
  Widget _buildShadowTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Shadow opacity
          StyleSliderWidget(
            value: settings.shadowOpacity,
            min: 0.0,
            max: 1.0,
            divisions: 20,
            onChanged: (value) {
              settings.setShadowSettings(opacity: value);
              onChanged?.call();
            },
            displayValue: '${(settings.shadowOpacity * 100).round()}%',
            trackLabel: 'Opacity',
          ),
          const SizedBox(height: 20),

          // Shadow blur radius
          StyleSliderWidget(
            value: settings.shadowBlurRadius,
            min: 0.0,
            max: 10.0,
            divisions: 20,
            onChanged: (value) {
              settings.setShadowSettings(blurRadius: value);
              onChanged?.call();
            },
            displayValue: settings.shadowBlurRadius.toStringAsFixed(1),
            trackLabel: 'Blur',
          ),
          const SizedBox(height: 20),

          // Shadow offset X
          StyleSliderWidget(
            value: settings.shadowOffset.dx,
            min: -5.0,
            max: 5.0,
            divisions: 20,
            onChanged: (value) {
              settings.setShadowSettings(offsetX: value);
              onChanged?.call();
            },
            displayValue: settings.shadowOffset.dx.toStringAsFixed(1),
            trackLabel: 'Offset X',
          ),
          const SizedBox(height: 20),

          // Shadow offset Y
          StyleSliderWidget(
            value: settings.shadowOffset.dy,
            min: -5.0,
            max: 5.0,
            divisions: 20,
            onChanged: (value) {
              settings.setShadowSettings(offsetY: value);
              onChanged?.call();
            },
            displayValue: settings.shadowOffset.dy.toStringAsFixed(1),
            trackLabel: 'Offset Y',
          ),
        ],
      ),
    );
  }
}

/// 样式滑块组件
class StyleSliderWidget extends StatelessWidget {
  final double value;
  final double min;
  final double max;
  final int divisions;
  final Function(double) onChanged;
  final String displayValue;
  final String? trackLabel;
  final bool showLeftLabel;
  final bool isFontSizeSlider;

  const StyleSliderWidget({
    Key? key,
    required this.value,
    required this.min,
    required this.max,
    required this.divisions,
    required this.onChanged,
    required this.displayValue,
    this.trackLabel,
    this.showLeftLabel = false,
    this.isFontSizeSlider = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Left label (only show if showLeftLabel is true)
        if (showLeftLabel) ...[
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                trackLabel ?? '',
                style: TextStyle(
                  color: ThemeManager.currentTheme.textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
        ],

        // Slider with track label or font size indicators
        Expanded(
          child: Stack(
            children: [
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: ThemeManager.currentTheme.slideBarActiveColor,
                  inactiveTrackColor: ThemeManager.currentTheme.slideBarInactiveColor,
                  thumbColor: ThemeManager.currentTheme.textColor,
                  overlayColor: Colors.transparent, // Remove overlay transparency
                  trackHeight: 32,
                  thumbShape: StyleSliderThumbShape(displayValue),
                  overlayShape: SliderComponentShape.noOverlay,
                ),
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  divisions: divisions,
                  onChanged: onChanged,
                ),
              ),
              // Track label positioned on the right side (for non-font-size sliders)
              if (trackLabel != null && !isFontSizeSlider)
                Positioned(
                  right: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      trackLabel!,
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              // Font size indicators inside track (for font size slider)
              if (isFontSizeSlider) ...[
                // Small "A" on the left
                Positioned(
                  left: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      'A',
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                // Large "A" on the right
                Positioned(
                  right: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      'A',
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}

/// 样式颜色选择器组件
class StyleColorPicker extends StatelessWidget {
  final Color currentColor;
  final Function(Color) onColorChanged;

  const StyleColorPicker({
    Key? key,
    required this.currentColor,
    required this.onColorChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colors = [
      Colors.black,
      Colors.white,
      Colors.red,
      Colors.blue,
      const Color(0xFF6366F1), // Purple
      Colors.yellow,
      Colors.orange,
      const Color(0xFF10B981), // Green
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: colors.map((color) {
        final isSelected = _colorsEqual(currentColor, color);
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected
                  ? ThemeManager.currentTheme.logoColor
                  : Colors.grey.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: isSelected && !ThemeManager.isDarkMode ? [
                BoxShadow(
                  color: ThemeManager.currentTheme.logoColor,
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 0),
                ),
              ] : null,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Compare two colors for equality using ARGB values
  bool _colorsEqual(Color color1, Color color2) {
    return color1.a == color2.a &&
           color1.r == color2.r &&
           color1.g == color2.g &&
           color1.b == color2.b;
  }
}

/// 自定义样式滑块拇指形状，在拇指中心显示数值
class StyleSliderThumbShape extends SliderComponentShape {
  final String displayValue;

  const StyleSliderThumbShape(this.displayValue);

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return const Size(32, 32);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // Draw thumb circle with enhanced shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.25)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    canvas.drawCircle(center + const Offset(0, 2), 16, shadowPaint);

    // Draw thumb circle
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, 16, paint);

    // Draw border
    final borderPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawCircle(center, 16, borderPaint);

    // Draw text
    final textSpan = TextSpan(
      text: displayValue,
      style: const TextStyle(
        color: Colors.black,
        fontSize: 10,
        fontWeight: FontWeight.w600,
      ),
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: textDirection,
    );

    textPainter.layout();

    final textOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );

    textPainter.paint(canvas, textOffset);
  }
}

/// Custom rounded underline tab indicator
class _RoundedUnderlineTabIndicator extends Decoration {
  final BorderSide borderSide;
  final BorderRadius borderRadius;
  final EdgeInsetsGeometry insets;

  const _RoundedUnderlineTabIndicator({
    required this.borderSide,
    required this.borderRadius,
    this.insets = EdgeInsets.zero,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _RoundedUnderlinePainter(this, onChanged);
  }
}

class _RoundedUnderlinePainter extends BoxPainter {
  final _RoundedUnderlineTabIndicator decoration;

  _RoundedUnderlinePainter(this.decoration, VoidCallback? onChanged)
      : super(onChanged);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    assert(configuration.size != null);
    final Rect rect = offset & configuration.size!;
    final TextDirection textDirection = configuration.textDirection!;
    final Rect indicator = decoration.insets.resolve(textDirection).deflateRect(rect);

    // Create filled paint with identical fill and border color
    final Paint paint = Paint()
      ..color = decoration.borderSide.color
      ..style = PaintingStyle.fill;

    final Path path = Path();
    final double indicatorHeight = decoration.borderSide.width;
    final Rect indicatorRect = Rect.fromLTWH(
      indicator.left,
      indicator.bottom - indicatorHeight,
      indicator.width,
      indicatorHeight, // Exact height, positioned to touch divider
    );

    path.addRRect(RRect.fromRectAndCorners(
      indicatorRect,
      topLeft: decoration.borderRadius.topLeft,
      topRight: decoration.borderRadius.topRight,
      bottomLeft: decoration.borderRadius.bottomLeft,
      bottomRight: decoration.borderRadius.bottomRight,
    ));

    canvas.drawPath(path, paint);
  }
}
