<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-8934235169213681~7420870570</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>workmanager.background.task</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Imtrans</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>AI Manga</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.307763371993-nvcmmp9kfln2gagbh3s3qu7i8m1kvvqu</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>cc.littlegrass.mangaai.appsflyer</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>appsflyeraimanga</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>GIDClientID</key>
	<string>307763371993-nvcmmp9kfln2gagbh3s3qu7i8m1kvvqu.apps.googleusercontent.com</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<!-- AppsFlyer Configuration -->
	<key>AppsFlyerDevKey</key>
	<string>m8RpWhxetPzmsCdGy4aMh6</string>
	<key>AppsFlyerAppID</key>
	<string>id6639613717</string>
	<!-- iOS 14.5+ App Tracking Transparency -->
	<key>NSUserTrackingUsageDescription</key>
	<string>This app uses tracking to provide personalized content and ads, and to measure advertising effectiveness.</string>
	<key>LSApplicationCategoryType</key>
	<array>
		<string></string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>13.0.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Need to access the camera to take photos and perform text translation on the images</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Need to access the photo library and select images for translation</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
	</array>
	<key>UIDeviceFamily</key>
	<array>
		<string>1</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
