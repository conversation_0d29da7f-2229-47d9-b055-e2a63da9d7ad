import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart' show ThemeManager;
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/util.dart';
import '../../models/product_model.dart';

class FolderCatalog extends StatefulWidget {
  final dynamic dart;
  final List<ProductImage> list;
  final int currentIndex;
  final String productName;

  const FolderCatalog(
      {required this.dart,
      required this.list,
      required this.currentIndex,
      required this.productName,
      super.key});

  @override
  State<FolderCatalog> createState() => _FolderCatalogState();
}

class _FolderCatalogState extends State<FolderCatalog> with TickerProviderStateMixin {
  late final AnimationController _inController;
  final _scrollController = ScrollController();
  late Animation<Offset> _animation;
  int _page = 1;
  final int _pageNum = 21;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        if (mounted) {
          nextPage();
        }
      }
    });
    _inController = AnimationController(vsync: this, duration: const Duration(milliseconds: 200));
    _animation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: const Offset(0, 0),
    ).animate(_inController);

    _inController.forward();
  }

  @override
  void dispose() {
    Util.popNull(widget);
    _scrollController.dispose();
    _inController.dispose();
    super.dispose();
  }

  nextPage() {
    setState(() {
      _page++;
    });
  }

  void closeView() {
    _inController.reverse().then((value) {
      widget.dart.showCatalog(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;

    return Material(
      color: Colors.transparent,
      child: SizedBox(
        width: size.width,
        height: size.height,
        child: GestureDetector(
          onTapUp: (e) {
            closeView();
          },
          child: Container(
            width: size.width,
            height: size.height,
            color:  ThemeManager.currentTheme.blackOverlayColor,
            alignment: Alignment.bottomCenter,
            child: SlideTransition(
              position: _animation,
              child: Container(
                width: size.width,
                height: size.height * 0.67,
                decoration: BoxDecoration(
                    color: ThemeManager.currentTheme.bottomBarBackgroundColor,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(30)),
                    boxShadow: [
                      BoxShadow(blurRadius: 20, color: ThemeManager.currentTheme.shadowColor)
                    ]),
                child: Column(
                  children: [
                    Container(
                      height: 60,
                      alignment: Alignment.center,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: size.width * 0.8,
                            alignment: Alignment.centerLeft,
                            padding: const EdgeInsets.only(left: 20, right: 20),
                            child: Text(
                              widget.productName,
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ThemeManager.currentTheme.textColor,
                                  fontWeight: FontWeight.w400),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: size.width * 0.2,
                            padding: const EdgeInsets.only(right: 10),
                            alignment: Alignment.centerRight,
                            child: IconButton(
                                onPressed: () {
                                  closeView();
                                },
                                icon: Icon(
                                  Icons.close,
                                  size: 26,
                                  color: ThemeManager.currentTheme.textColor,
                                )),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: size.width,
                      height: size.height * 0.65 - 50,
                      alignment: Alignment.topLeft,
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              spacing: 15,
                              runSpacing: 10,
                              children: createListView(size),
                            ),
                            const SizedBox(height: 10)
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  static createImage(ProductImage item, imageWidth, imageHeight) {
    BoxFit fit = BoxFit.cover;
    var image = CachedNetworkImage(
      imageUrl: item.url!,
      width: imageWidth,
      height: imageHeight,
      fit: fit,
    );
    return SizedBox(
      width: imageWidth,
      height: imageHeight,
      child: image,
    );
  }

  createItemDetail(ProductImage item, double w, double h) {
    // int read = item.read;
    return Container(
      constraints: BoxConstraints(
        minHeight: h,
      ),
      clipBehavior: Clip.hardEdge,
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(8))
      ),
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: w,
          alignment: Alignment.topLeft,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
      ),
    );
  }

  createListView(size) {
    double imageWidth = (size.width - 70) / 3;
    double imageHeight = imageWidth * 1.44;
    List<Widget> widgets = [];

    int end = _page * _pageNum;
    if (end > widget.list.length) {
      end = widget.list.length;
    }

    List<ProductImage> list = widget.list.sublist(0, end);
    for (var i = 0; i < list.length; i++) {
      ProductImage productImage = list[i];
      var image = Container(
        width: imageWidth,
        height: imageHeight,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
            color:  ThemeManager.currentTheme.itemBorderColor,
            borderRadius: BorderRadius.circular(8),
            border: widget.currentIndex == i
                ? Border.all(color: ThemeManager.currentTheme.logoColor, width: 2)
                : Border.all(width: 0),
            boxShadow: [
              BoxShadow(
                color:  ThemeManager.currentTheme.itemBorderColor,
                blurRadius: 4,
                offset: const Offset(0, 4),
              ),
        ]),
        alignment: Alignment.topCenter,
        child: Stack(
          alignment: Alignment.center,
          clipBehavior: Clip.none,
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: createImage(productImage, imageWidth, imageHeight),
            ),
            createItemDetail(
                productImage,
                imageWidth + (widget.currentIndex == i ? 0 : 4),
                imageHeight / 2),
          ],
        ),
      );
      Widget con = GestureDetector(
        onTapUp: (e) {
          closeView();
          widget.dart.selectIndex(i);
        },
        child: image,
      );
      widgets.add(con);
    }

    widgets.add(const SizedBox(
      height: 10,
    ));
    return widgets;
  }
}
