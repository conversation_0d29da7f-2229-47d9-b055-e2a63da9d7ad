import 'dart:io';
import 'package:archive/archive.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import '../services/account.dart';
import '../models/draft_model.dart';
import 'package:path/path.dart' as path;
import '../controllers/product_controller.dart';
import '../services/event_log.dart';
import 'dart:async';

/// 草稿箱控制器
class DraftController {
  static final DraftController _instance = DraftController._internal();
  factory DraftController() => _instance;
  
  DraftController._internal() {
    _initFuture = _initialize();
  }

  /// 草稿数据模型
  final DraftModel _model = DraftModel();
  Future<void>? _initFuture;

  List<Draft> get list => _model.items;
  String get storagePath => _model.storagePath;

  /// 获取当前草稿数量
  int get draftCount => list.length;

  /// 提供一个 getter 用于监听更新
  Stream<void> get onDraftUpdated => _draftStreamController.stream;
    /// 获取初始化 Future
  Future<void> get initialized => _initFuture ?? Future.value();

  /// 添加一个 StreamController
  final _draftStreamController = StreamController<void>.broadcast();

  /// 添加草稿数量的 StreamController
  final _draftCountController = StreamController<int>.broadcast();

  /// 提供一个 getter 用于监听草稿数量
  Stream<int> get onDraftCountUpdated => _draftCountController.stream;

  /// 初始化草稿箱
  /// 从账户获取uuid并调用模型的初始化
  Future<void> _initialize() async {
    final account = await Account.instance;
    await _model.initialize(suffix: account.instanceId);
  }
  
  /// 在控制器销毁时关闭 stream
  void dispose() {
    _draftStreamController.close();
    _draftCountController.close();
  }

  /// 开始翻译处理
  /// [targetLang] 目标语言
  /// [indices] 要处理的草稿索引列表,如果为null则处理所有草稿
  Future<void> beginProcess(String targetLang, [List<int>? indices, Function? onQuotaExceeded]) async {
    await _model.load();
    debugPrint('beginProcess');
    ProductController productController = ProductController();

    // 确定要处理的草稿索引列表
    final List<int> draftIndices = indices ?? List.generate(_model.items.length, (i) => i);
    
    // 将所有要处理的草稿状态设置为pending
    for (int index in draftIndices) {
      _model.items[index].updateStatus(Draft.STATUS_PENDING);
    }
    _draftStreamController.add(null);
    
    // 从前向后遍历，从上面开始处理
    // 使用倒序删除计数器来跟踪已删除的项目数量
    int deletedCount = 0;
    for (int i = 0; i < draftIndices.length; i++) {
      // 调整索引以考虑已删除的项目
      int modelIndex = draftIndices[i] - deletedCount;
      if (modelIndex >= _model.items.length) {
        continue; // 跳过无效索引
      }
      Draft draft = _model.items[modelIndex];
      
      // 在处理每个草稿前检查剩余免费次数或者VIP状态
      if (!await checkPermition()) {
        // 恢复所有草稿的状态为正常
        for (int idx in draftIndices) {
          if (idx < _model.items.length && _model.items[idx].processingStatus == Draft.STATUS_PENDING) {
            _model.items[idx].updateStatus(Draft.STATUS_NONE);
          }
        }
        _draftStreamController.add(null);
        if (onQuotaExceeded != null) {
          onQuotaExceeded();
        }
        return; // Stop the translation process
      }
      
      draft.updateStatus(Draft.STATUS_PROCESSING);
      _draftStreamController.add(null);

      // 记录翻译开始事件
      await EventLogService.logTranslateStart(targetLang: targetLang);

      // 读取图片
      List<Uint8List> imageDatas = [];
      List<String> imageNames = [];
      for (String imageFile in draft.imageFiles) {
        final file = File('${storagePath}/$imageFile');
        final imageData = await file.readAsBytes();
        imageDatas.add(imageData);
        imageNames.add(path.basename(imageFile));
      }
      _draftStreamController.add(null);

      try { 
        String productName = draft.name != null ? path.basenameWithoutExtension(draft.name!) : "";
        String? productId = await productController.create(imageDatas, targetLang, name: productName, imageNames: imageNames);
        if (productId != null) {
          draft.updateStatus(Draft.STATUS_TRANSLATING);
          _draftStreamController.add(null);
          // 取得翻译结果
          final product = await productController.waitForTranslation(productId);
          if (product != null) {
            debugPrint(' ------ translation finished ------');
            
            // 设置状态为完成
            draft.updateStatus(Draft.STATUS_COMPLETED);
            _draftStreamController.add(null);
            
            // 立即删除完成的草稿
            await _cleanupDraftResources(draft);
            _model.removeAt(modelIndex);
            await _model.save();
            deletedCount++; // 增加删除计数
            
            _draftCountController.add(draftCount);
            _draftStreamController.add(null); // 通知UI更新草稿列表
            
            // 通知 Product 视图更新
            Future.microtask(() {
              productController.notifyProductsUpdated();
              debugPrint('Product update notification sent after draft processing');
            });

            // 记录翻译成功事件
            await EventLogService.logImageTranslation(
              productId: productId,
              targetLang: targetLang,
              success: true,
            );
          }
          else {
            draft.updateStatus(Draft.STATUS_FAILED);
            _draftStreamController.add(null);
            ToastWidget.show('Translation timeout');

            // 记录翻译失败事件（超时）
            await EventLogService.logImageTranslation(
              productId: '',
              targetLang: targetLang,
              success: false,
              errorMessage: 'Translation timeout',
            );
          }
        } else {
          draft.updateStatus(Draft.STATUS_FAILED);
          _draftStreamController.add(null);
          
          // 记录翻译失败事件（创建产品失败）
          await EventLogService.logImageTranslation(
            productId: '',
            targetLang: targetLang,
            success: false,
            errorMessage: 'Create product failed',
          );
        }
      } catch (e) {
        debugPrint('Error creating product: $e');
        
        // 记录翻译失败事件（异常）
        await EventLogService.logImageTranslation(
          productId: '',
          targetLang: targetLang,
          success: false,
          errorMessage: 'Error creating product: $e',
        );
        ToastWidget.show('Failed to create product: $e');
        draft.updateStatus(Draft.STATUS_FAILED);
        _draftStreamController.add(null);
      }
    }
  }


  Future<bool> checkPermition() async {
    ProductController productController = ProductController();
    final remainingCount = await productController.getQuota();
    final account = await Account.instance;
    return account.isVip || remainingCount > 0;
  }

  /// 删除指定草稿
  /// [index] 草稿在列表中的索引
  /// 会同时清理相关的资源文件
  Future<void> deleteDraft(int index) async {
    debugPrint('Deleting draft at index: $index');
    final draft = _model.items[index];
    await _cleanupDraftResources(draft);
    _model.removeAt(index);
    debugPrint('after delete draft: ${_model.items}');
    await _model.save();
    debugPrint('Draft deleted and model saved.');
    _draftStreamController.add(null);
    _draftCountController.add(draftCount);
  }

  /// 从草稿箱存储目录删除文件
  /// [fileName] 要删除的文件名
  Future<void> _deleteFile(String fileName) async {
    try {
      debugPrint('Attempting to delete file: $fileName');
      final file = File('${storagePath}/$fileName');
      if (await file.exists()) {
        await file.delete();
        debugPrint('File deleted: $fileName');
      } else {
        debugPrint('File not found: $fileName');
      }
    } catch (e) {
      debugPrint('Error deleting file: $e');
    }
  }

  /// 清理草稿相关的所有资源文件
  /// 包括封面图片和其他图片文件
  Future<void> _cleanupDraftResources(Draft draft) async {
    debugPrint('Cleaning up resources for draft: ${draft.name}');
    // 清理封面图片
    if (draft.coverFile != null) {
      await _deleteFile(draft.coverFile!);
    }

    // 清理其他图片
    for (String path in draft.imageFiles) {
      await _deleteFile(path);
    }
  }

  /// 统一处理选择的文件或图片
  /// [selection] 可以是:
  /// - List<XFile>: 摄像头/相册选择的图片
  /// - String: 文件路径
  /// - List<Uint8List>: 图片二进制数据
  Future<void> handleSelection(dynamic selection) async {
    // debugPrint('Handling selection: $selection');
    await _model.load();
    if (selection is List<XFile>) {
      await _handleImageSelection(selection);
    } else if (selection is String) {
      await _handleFileSelection(selection);
    } else if (selection is List<Uint8List>) {
      await _handleImageBytes(selection);
    }
    await _model.save();
    _draftStreamController.add(null);
    _draftCountController.add(draftCount);
  }

  /// 私有方法：处理图像二进制数据
  Future<void> _handleImageBytes(List<Uint8List> images) async {
    for (var imageData in images) {
      // 生成唯一的图片名称
      final String imageName = 'img_${DateTime.now().microsecondsSinceEpoch}.jpg';
      final String targetPath = '${_model.storagePath}/$imageName';
      
      // 保存图片数据到文件
      await File(targetPath).writeAsBytes(imageData);
      
      // 创建封面
      String coverFile = await _model.createCoverImage(targetPath);

      // 创建新的草稿
      final draft = Draft(
        name: imageName,
        coverFile: coverFile,
        imageFiles: [imageName],
      );
      _model.add(draft);
      debugPrint('Image bytes processed and draft added: $imageName');
    }
  }

  /// 私有方法：处理图像选择
  /// 将选择的图片移动到草稿目录，并保存到草稿箱
  Future<void> _handleImageSelection(List<XFile> images) async {
    for (var image in images) {
      // 移动图片到草稿目录
      final String draftPath = _model.storagePath;
      final String imageName = 'img_${DateTime.now().microsecondsSinceEpoch}.jpg';
      final String targetPath = '$draftPath/$imageName';
      await image.saveTo(targetPath);
      // 创建封面
      String coverFile = await _model.createCoverImage(targetPath);

      final draft = Draft(
        name: imageName,
        coverFile: coverFile,
        imageFiles: [imageName],
        );
      _model.add(draft);
      debugPrint('Image processed and draft added: $imageName');
    }
  }

  /// 私有方法：处理文件选择
  Future<void> _handleFileSelection(String filePath) async {
    debugPrint('Handling file selection: $filePath');
    final file = File(filePath);
    if (await file.exists()) {
      Draft draft = Draft();
      draft.name = path.basename(filePath);

      // 生成随机文件名避免重复
      final String randomName = '${DateTime.now().millisecondsSinceEpoch}_${path.basename(filePath)}';
      final String targetPath = "${_model.storagePath}/$randomName";
      await file.copy(targetPath);
      
      // 使用新的文件路径读取内容
      final copiedFile = File(targetPath);
      final archive = await ZipDecoder().decodeBytes(await copiedFile.readAsBytes());
      
      // 获取所有文件并按文件名排序
      var archiveFiles = archive.files.toList()
        ..sort((a, b) => a.name.compareTo(b.name));
      
      for (var file in archiveFiles) {
        final String fileName = file.name; // 注意:这个解析出来包含了压缩包里的路径
        final String targetPath = '${_model.storagePath}/$fileName';

        // 只处理可接受的图片文件
        if (file.isFile && Draft.acceptedFileTypes.contains(fileName.toLowerCase().split('.').last)) {
          // Skip hidden files starting with . and __ 
          if (path.basename(fileName).startsWith('.') || fileName.startsWith('__')) {
            debugPrint('Unsupported file type: $fileName');
            continue;
          }
          final data = file.content as List<int>;
          await File(targetPath).create(recursive: true);
          await File(targetPath).writeAsBytes(data);
          
          if (draft.coverFile == null) {
            // 使用第一张制作封面
            String coverFile = await _model.createCoverImage(targetPath);
            draft.coverFile = path.join(path.dirname(fileName), coverFile);
            draft.imageFiles = [fileName];
          } else {
            draft.imageFiles.add(fileName);
          }
          
        } else {
          debugPrint('Unsupported file type: $fileName');
        }
      }
      _model.add(draft);
      debugPrint('File selection handled and draft added.');
    }
  }

  /// 清空所有草稿
  /// 会删除所有草稿数据及其关联的资源文件
  Future<void> clearAllDrafts() async {
    debugPrint('Clearing all drafts');
    try {
      // 清理所有草稿的资源文件
      for (Draft draft in List.from(_model.items)) {
        await _cleanupDraftResources(draft);
      }
      
      // 清空草稿列表
      _model.items.clear();
      await _model.save();
      
      // 通知监听者更新
      // _draftStreamController.add(null);
      // _draftCountController.add(0);
      
      debugPrint('All drafts cleared successfully');
    } catch (e) {
      debugPrint('Error clearing drafts: $e');
      rethrow; // 向上层抛出异常，让调用者处理
    }
  }
}
