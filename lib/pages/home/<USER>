import 'dart:async';
import 'package:flutter/material.dart';
// import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/util/dialog_manager.dart';
import 'package:imtrans/widgets/common/image_widget.dart';
import '../../controllers/draft_controller.dart';
import '../../models/draft_model.dart';
import '../../models/product_model.dart';
import '../../util/theme_manager.dart';
import '../../util/util.dart';
import '../../widgets/breakall.dart';
import '../../util/language_manager.dart';
import '../../controllers/product_controller.dart';
import '../../services/account.dart';
import '../purchase/vip.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import '../../util/localizations_extension.dart';
import '../../widgets/wheel_picker.dart';

class DraftView extends StatefulWidget {
  final dynamic cameraDart;
  const DraftView({this.cameraDart, super.key});

  @override
  State<DraftView> createState() => _DraftViewState();
}

class _DraftViewState extends State<DraftView> {
  final Map<String, String> _languages = Product.targetLanguages;
  String _currentLang = LanguageManager.currentLanguage;
  bool _isSelectMode = false;
  Set<int> _selectedIndices = {};
  bool _isAllSelected = false;
  late final DraftController _draftController;
  bool _isTranslating = false;
  StreamSubscription? _draftSubscription;
  late VoidCallback _themeListener;
  // RewardedAd? _rewardedAd;
  // 记录本次进入页面是否已经提示过错误，避免重复弹toast
  bool _errorShown = false;

  @override
  void initState() {
    super.initState();
    _draftController = DraftController();
    // Controller.draftView = this;
    // 添加初始化完成后的状态更新
    _draftController.initialized.then((_) {
      if (mounted) {
        setState(() {});
      }
    });
    
    _draftSubscription = _draftController.onDraftUpdated.listen((_) {
      debugPrint('Draft updated notification received');
      if (mounted) {
        setState(() {
          // 检查是否所有翻译任务都已完成，如果是则重置翻译状态
          if (_isTranslating && !_draftController.list.any((draft) => draft.isProcessing)) {
            _isTranslating = false;
            debugPrint('All translation tasks completed, resetting _isTranslating to false');
          }
        });
      }
    });
    // 添加主题变化监听
    _themeListener = () {
      if (mounted) {
        setState(() {});
      }
    };
    ThemeManager().addListener(_themeListener);

    // _loadRewardedAd(); // 加载广告
  }

  @override
  void dispose() {
    // _rewardedAd?.dispose();
    _draftSubscription?.cancel();
    Util.popNull(widget);
    cancelAllSelect(isBack: true);
    ThemeManager().removeListener(_themeListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 页面每次build时检查是否有失败任务需要提示
    if (!_errorShown) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkForFailedDrafts();
      });
    }
    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: FutureBuilder(
        future: _draftController.initialized,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.loadingFailed(snapshot.error.toString()),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {  });
                    },
                    child: Text(AppLocalizations.of(context)!.retry),
                  ),
                ],
              ),
            );
          }
          return createContainer();
        },
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      toolbarHeight: 60,
      title: Container(
        height: 60,
        width: double.infinity,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 左侧按钮 - 根据模式显示返回或取消
            Positioned(
              left: 20,
              top: 0,
              bottom: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    if (_isSelectMode) {
                      // 退出选择模式
                      cancelAllSelect();
                    } else {
                      // 返回上一页
                      Util.closeView(widget);
                    }
                  },
                  child: _isSelectMode 
                  ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(50), 
                      border: Border.all(color: ThemeManager.currentTheme.textColor, width: 1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor,
                        fontSize: 12,
                      ),
                    )
                  )
                  : Image.asset(
                      ThemeManager.getImagePath('btn_back'),
                      width: 40,
                      height: 40,
                    ),
                ),
              ),
            ),
            // 居中标题 - 选择模式下显示已选数量
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _isSelectMode 
                        ? AppLocalizations.of(context)!.selectedCount(_selectedIndices.length)
                        : AppLocalizations.of(context)!.drafts,
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (!_isSelectMode && _draftController.list.isNotEmpty)
                      SizedBox(height: 4),
                      Text(
                        '${_draftController.list.length}',
                        style: TextStyle(
                          color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // 右侧按钮区域
            Positioned(
              right: 20,
              top: 0,
              bottom: 0,
              child: _draftController.list.isNotEmpty ? 
                Center(
                  child: Row(  // 将单个按钮改为 Row 布局
                    children: [
                      // 删除按钮 - 仅在选择模式下显示
                      if (_isSelectMode)
                        GestureDetector(
                          onTap: _selectedIndices.isNotEmpty ? () {
                            DialogManager.instance.showDeleteDialog(
                              message: AppLocalizations.of(context)!.deleteConfirmation(
                                _selectedIndices.length,
                                _selectedIndices.length > 1 ? 's' : ''
                              ),
                              onConfirm: () {
                                deleteSelect();
                              }
                            );
                          } : null,
                          child: Container(
                            margin: EdgeInsets.only(right: 20),
                            child: Image.asset(
                              ThemeManager.getImagePath('icon_delete'),
                              width: 24,
                              height: 24,
                              color: _selectedIndices.isNotEmpty ? null : Colors.grey,
                            ),
                          ),
                        ),
                      // 全选按钮
                      GestureDetector(
                        onTap: () {
                          if (_isSelectMode) {
                            // 全选/取消全选
                            setState(() {
                              if (_isAllSelected) {
                                _selectedIndices = {};
                                // 取消所有草稿的选中状态
                                for (var draft in _draftController.list) {
                                  if (!draft.isProcessing) {
                                    draft.setSelected(false);
                                  }
                                }
                              } else {
                                // 选中所有非处理中的草稿
                                for (int i = 0; i < _draftController.list.length; i++) {
                                  if (!_draftController.list[i].isProcessing) {
                                    _selectedIndices.add(i);
                                    _draftController.list[i].setSelected(true);
                                  }
                                }
                              }
                              _isAllSelected = !_isAllSelected;
                            });
                          } else {
                            // 进入选择模式
                            setState(() {
                              _isSelectMode = true;
                              _selectedIndices = {};
                              _isAllSelected = false;
                            });
                          }
                        },
                        child: _isSelectMode
                          ? Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: const Color(0xFF1B1C1E),
                                  width: 1,
                                ),
                                color: _isAllSelected ? ThemeManager.currentTheme.selectedItemColor : Colors.white,
                              ),
                              child: _isAllSelected
                                ? const Icon(Icons.check, color: const Color(0xFF1B1C1E), size: 16)
                                : null,
                            )
                          : Image.asset(
                              ThemeManager.getImagePath('btn_select'),
                              width: 40,
                              height: 40,
                            ),
                      ),
                    ],
                  ),
                )
              : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  _buildBottomBar() {
    // var width = MediaQuery.of(context).size.width;
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        padding: const EdgeInsets.only(top: 4, bottom: 0, left: 8, right: 8),
        decoration: BoxDecoration(
          color: ThemeManager.currentTheme.bottomBarBackgroundColor,
        ),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // 语言选择下拉菜单
              Container(
                width: 180,
                child: OutlinedButton(
                  onPressed: () {
                    // 临时存储选择的语言，以便在确认时应用
                    String tempSelectedLang = _currentLang;
                    showModalBottomSheet(
                      context: context,
                      backgroundColor: ThemeManager.currentTheme.backgroundColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                      ),

                      // 使用通用滚轮选择器组件
                      builder: (context) => WheelPicker(
                        selectedValue: tempSelectedLang,
                        options: Map.fromEntries(
                          _languages.keys.map(
                            (key) {
                              final languageName = AppLocalizations.of(context)!.getLanguageName(key);
                              return MapEntry(
                                key, 
                                languageName
                              );
                            },
                          ),
                        ),
                        onValueChanged: (newLang) {
                          tempSelectedLang = newLang;
                        },
                        onCancel: () { Navigator.pop(context); },
                        onConfirm: () {
                          Navigator.pop(context);
                          setState(() {
                            _currentLang = tempSelectedLang;
                          });
                        },
                      ),
                    );
                  },
                  style: OutlinedButton.styleFrom(
                    backgroundColor: ThemeManager.currentTheme.borderAreaBgColor,
                    foregroundColor: ThemeManager.currentTheme.textColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: BorderSide(color: ThemeManager.currentTheme.textColor, width: 1),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(width: 14),
                      Expanded(  
                        child: Text(
                          AppLocalizations.of(context)!.translateTo(_currentLang),
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500
                          ),
                        ),
                      ),
                      Row(  
                        children: [
                          Container(
                            width: 1,
                            height: 16,
                            color: Color(0xffd9d9d9),
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                          ),
                          const Icon(Icons.keyboard_arrow_up, size: 20),
                          const SizedBox(width: 8),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              // 翻译按钮
              Container(
                width: 155,
                padding: const EdgeInsets.symmetric(horizontal: 0,vertical: 8),
                // height: 38,
                child: OutlinedButton(
                  onPressed: (_isTranslating || _draftController.list.any((draft) => draft.isProcessing) || 
                      (_isSelectMode && !_draftController.list.any((draft) => draft.selected))) ? null : () async {
                    try {
                      setState(() {
                        _isTranslating = true;
                      });
                    
                      // 根据选择状态决定要处理的草稿索引
                      List<int>? indicesToProcess;
                      if (_isSelectMode) {
                        indicesToProcess = [];
                        for (int i = 0; i < _draftController.list.length; i++) {
                          if (_draftController.list[i].selected) {
                            indicesToProcess.add(i);
                          }
                        }
                        if (indicesToProcess.isEmpty) {
                          ToastWidget.show(AppLocalizations.of(context)!.nothingSelected);
                          return;
                        }
                        // 开始翻译前取消选择状态
                        cancelAllSelect();
                      }
                      // 直接启动翻译任务而不阻塞当前页面，保证离开页面后任务仍可继续执行
                      await _draftController.beginProcess(_currentLang, indicesToProcess, _onQuotaExceeded);
                    } catch (e) {
                      // 捕获翻译过程中的异常并提示，同时保证状态被正确复位
                      debugPrint('Error during translation: ${e.toString()}');
                      if (mounted) {
                        ToastWidget.showError(AppLocalizations.of(context)!.operationFailed(e.toString()));
                      }
                    } finally {
                      // 无论成功还是失败，都要重置翻译状态
                      if (mounted) {
                        setState(() {
                          _isTranslating = false;
                        });
                      }
                    }
                  },
                  style: OutlinedButton.styleFrom(
                    backgroundColor: ThemeManager.currentTheme.logoColor, 
                    foregroundColor: const Color(0xff1b1c1e),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: const BorderSide(color: Colors.black, width: 1), 
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isTranslating || _draftController.list.any((draft) => draft.isProcessing))
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.black54,
                          ),
                        )
                      else
                        Text(
                          _isSelectMode ? AppLocalizations.of(context)!.translate : AppLocalizations.of(context)!.translateAll,
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onQuotaExceeded() {
    DialogManager.instance.showDialog(
      title: AppLocalizations.of(context)!.dailyLimitReached,
      message: AppLocalizations.of(context)!.quotaResetMessage,
      onConfirm: () {
        Util.navigatorPush(const Vip());
      },
      confirmText: AppLocalizations.of(context)!.watchAd,
      cancelText: AppLocalizations.of(context)!.tryTomorrowButton,
      onCancel: (){},
    );
  }

  cancelAllSelect({bool? isBack}) {
    if (isBack == null) {
      setState(() {
        _isSelectMode = false;
        _selectedIndices = {};
        _isAllSelected = false;
        // 清除所有草稿的选中状态
        for (var draft in _draftController.list) {
          draft.setSelected(false);
        }
      });
    }
  }

  Future<void> deleteSelect() async {
    try {
      List<int> indexesToDelete = [];
      for (int i = 0; i < _draftController.list.length; i++) {
        if (_draftController.list[i].selected) {
          indexesToDelete.add(i);
        }
      }
      // 从后往前删除以保持索引有效
      for (int i = indexesToDelete.length - 1; i >= 0; i--) {
        await _draftController.deleteDraft(indexesToDelete[i]);
      }
    } catch (e) {
      debugPrint('Error deleting drafts: $e');
    }
    finally {
      setState(() {  
        _selectedIndices = {};
        _isAllSelected = false;
        // 确保所有草稿的选中状态被重置
        for (var draft in _draftController.list) {
          draft.setSelected(false);
        }
      });
    }
  }

  Widget _buildImageName(Draft item) {
    String name = Util.getImageName(item.name ?? "");
    return Container(
      padding: EdgeInsets.only(left: 4, right: 4, top: 3, bottom: 4),
      constraints: const BoxConstraints(
        minHeight: 20
      ),
      alignment: Alignment.centerLeft,
      child: BreakAllText(
        name,
        style: TextStyle(
          fontSize: 12,
          color: ThemeManager.currentTheme.textColor,
          fontWeight: FontWeight.w400,
        ),
        maxLines: 2,
      ),
    );
  }

  createCover(Draft item, double w) {
    bool isFolder = item.imageFiles.length > 1;
    String imagePath = item.coverFile != null ? _draftController.storagePath + "/" + item.coverFile! : "";
    if (isFolder) {
      double imageWidth = w;  // 使用传入的宽度
      double imageHeight = 142;
      return Container(
        alignment: Alignment.center,
        width: w,
        height: 160,
        child: Column(
          children: [
            Expanded(
              child: Container(
                width: imageWidth,
                clipBehavior: Clip.hardEdge,
                alignment: Alignment.center,
                margin: const EdgeInsets.only(bottom: 5,left: 5,right: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Stack(
                  fit: StackFit.expand,  // 确保Stack填充整个容器
                  children: [
                    ImageWidget.loadImage(context, imagePath, width: imageWidth, height: imageHeight),
                    if (item.isProcessing || item.processingStatus == Draft.STATUS_FAILED) createLoading(item),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        width: w,
        height: w * 1.33,  // 保持合适的宽高比
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Stack(
          fit: StackFit.expand, 
          children: [
            ImageWidget.loadImage(context, imagePath, width: w, height: w * 1.33),
            if (item.isProcessing || item.processingStatus == Draft.STATUS_FAILED) createLoading(item),
          ],
        ),
      );
    }
  }

  IconData _getStatusIcon(item) {
    switch (item.processingStatus) {
      case Draft.STATUS_PENDING:
        return Icons.pending_actions;
      case Draft.STATUS_PROCESSING:
        return Icons.settings;
      case Draft.STATUS_TRANSLATING:
        return Icons.translate;
      case Draft.STATUS_COMPLETED:
        return Icons.check_circle_outline;
      case Draft.STATUS_FAILED:
        return Icons.error_outline;
      default:
        return Icons.pending_actions;
    }
  }
  
  createLoading(Draft item) {
    return Container(
      alignment: Alignment.center,
      color: Colors.black45,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 32,
            height: 32,
            child: item.processingStatus == Draft.STATUS_COMPLETED || 
                    item.processingStatus == Draft.STATUS_FAILED || 
                    item.processingStatus == Draft.STATUS_PENDING
              ? Icon(
                  _getStatusIcon(item),
                  size: 32,
                  color: Colors.white,
                )
              : Stack(
                  alignment: Alignment.center,
                  children: [
                    const CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    Icon(
                      _getStatusIcon(item),
                      size: 16,
                      color: Colors.white,
                    ),
                  ],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            Draft.getStatusText(item.processingStatus),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.none,
            ),
          ),
        ],
      ),
    );
  }

  _buildSelectionIcon(item){
    return Positioned(
      right: 8,
      top: 8,
      child: Container(
        width: 16,
        height: 16,
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: item.selected ? ThemeManager.currentTheme.selectedItemColor : Colors.white,
          shape: BoxShape.circle,
          border: Border.all(
            color: const Color(0xFF1B1C1E),
            width: 1,
          ),
        ),
        child: item.selected
          ? const Icon(
              Icons.check,
              color: Color(0xFF1B1C1E),
              size: 10,
            )
          : null,
      ),
    );
  }

  createSingleImage(double width, int index, Draft item, {Function? onPressed}) {
      double w = (width - 40) / 3; // 三列布局的宽度计算
      bool isFolder = item.imageFiles.length > 1;
      
      return Container(
        width: w,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: ThemeManager.currentTheme.mineButtonBackgroundColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: .04),
              offset: const Offset(0, 4),
              blurRadius: 10,
            ),
          ],
        ),
        alignment: Alignment.topCenter,
        child: GestureDetector(
          onTapUp: (e) {
            if (!_isSelectMode) {
              if (onPressed != null) {
                onPressed();
              }
              // Util.navigatorPush(ShowImage(list: list, currentIndex: index));
            } else {
              if (!item.isProcessing) {
                setState(() {
                  item.setSelected(!item.selected);
                  // 同步更新 _selectedIndices 集合
                  if (item.selected) {
                    _selectedIndices.add(index);
                  } else {
                    _selectedIndices.remove(index);
                  }
                  // 更新全选状态
                  _isAllSelected = _selectedIndices.length == _draftController.list.where((d) => !d.isProcessing).length;
                });
              }
            }
          },
          child: Stack(
            fit: StackFit.expand,  // 确保Stack填充整个容器
            children: [
              Container(
                width: w,
                height: 180,
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: isFolder
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildImageName(item),
                        Expanded(
                          child: createCover(item, w),
                        ),
                      ],
                    )
                  : createCover(item, w),
            ),
            // 如果是选择模式，显示选择图标
            if (_isSelectMode && !item.isProcessing) _buildSelectionIcon(item),
          ],
        ),
      ),
    );
  }

  createContainer() {
    var width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      body: Stack(
        children: [
          // 主要内容区域
          if (_draftController.list.isNotEmpty)
            Container(
              // width: size.width,
              padding: EdgeInsets.only(left: 11, right: 11, bottom: 80),
              child: Container(
                // width: width,
                child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                    ),
                    itemCount: _draftController.list.length,
                    itemBuilder: (context, index) {
                      var item = _draftController.list[index];
                      return createSingleImage(width, index, item);
                    },
                  )
              ),
            )
          else
            Container(
              // width: size.width,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 80), // 使用 Padding 替代 Transform.translate
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset("images/draft_nodata.png", width: 80),
                      SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context)!.allJobsDone,
                        style: TextStyle(
                          color: ThemeManager.currentTheme.textColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w700
                        ),
                      ),
                       Image.asset(
                          'images/nodata_line.png',
                          width: 200,
                          height: 14,
                          fit: BoxFit.fill,
                        ),
                    ],
                  ),
                ),
              ),
            ),
          
          // Pro升级和剩余次数提示
          FutureBuilder<Widget>(
            future: _buildUsagePrompt(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return snapshot.data!;
              }
              return Container();
            },
          ),
          
          // 底部工具栏 - 只在有数据时显示
          if (_draftController.list.isNotEmpty) _buildBottomBar(),
        ],
      ),
    );
  }

  /// 构建使用次数提示
  /// 如果用户不是VIP，则显示使用次数提示
  /// 如果用户是VIP，则不显示
  Future<Widget> _buildUsagePrompt() async {
    Account account = await Account.instance;
    if (!account.isVip) {
      final remainingUses = await ProductController.instance.getQuota();
      return Positioned(
        left: 0,
        right: 0,
        bottom: 120,
        child: Container(
          height: 64,
          margin: const EdgeInsets.symmetric(horizontal: 12),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: ThemeManager.currentTheme.backgroundColor.withValues(alpha: .9),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.white,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: .1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.usesLeftToday(remainingUses),
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor,
                        fontSize: 15,
                        fontWeight: FontWeight.w500
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      AppLocalizations.of(context)!.upgradeToPro,
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: .6),
                        fontSize: 10,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8), // 添加间距
              OutlinedButton(
                onPressed: () {
                  Util.navigatorPush(Vip());
                },
                style: OutlinedButton.styleFrom(
                  backgroundColor: const Color(0xFF1B1C1E),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  AppLocalizations.of(context)!.getPro,
                  style: const TextStyle(
                    fontSize: 14,
                    fontFamily: "Inter",
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.005
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Container();
  }

  /// 检查是否存在失败的翻译任务，若有则给出Toast提示并重置提示标记
  void _checkForFailedDrafts() {
    if (!mounted) return;
    final hasFailed = _draftController.list.any((d) => d.processingStatus == Draft.STATUS_FAILED);
    if (hasFailed) {
      _errorShown = true;
      // ToastWidget.show(AppLocalizations.of(context)!.operationFailed(""));
      // 同时确保界面刷新，去掉潜在的 loading 状态
      setState(() {});
    }
  }
}
