import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:imtrans/services/account.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/util/loading_manager.dart';
import 'package:imtrans/util/theme_manager.dart';
import '../util/util.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

/// 登录页
class SignIn extends StatefulWidget {
  final Function onSelected;
  const SignIn({required this.onSelected,super.key});

  @override
  State<SignIn> createState() => _SignInState();
}

class _SignInState extends State<SignIn> with SingleTickerProviderStateMixin {

  Size _winSize=const Size(0,0);
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  late final AnimationController _inController=AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200)
  )..addStatusListener((status) {
    if (status == AnimationStatus.dismissed) {
      Util.closeView(widget,showAni: false);
    }
  });

  @override
  void initState() {
    Future.delayed(const Duration(milliseconds: 10),(){
      _inController.forward();
    });
    super.initState();
  }

  @override
  dispose(){
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Widget _buildSocialLoginButton(String title, Color backgroundColor, Widget icon) {
    return Container(
      width: _winSize.width * 0.9,
      height: 48,
      margin: const EdgeInsets.only(bottom: 26),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      child: TextButton(
        onPressed: () async {
          // 使用_handleSignIn方法处理登录逻辑
          if (title == "Google") {
            await _handleSignIn(Account.signinTypeGoogle);
          } else if (title == "Apple") {
            await _handleSignIn(Account.signinTypeApple);
          }
          else if (title == "Email") {
            await _handleSignIn(Account.signinTypeAuth0);
          }
        },
        style: TextButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            const SizedBox(width: 10),
            Text(
              "$title",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xff1b1c1e),
                letterSpacing: -0.5,
                
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 添加处理登录的方法
  Future<void> _handleSignIn(String signinType) async {
    try {
      Account account = await Account.instance;
      
      if (!mounted) return;
      // 使用较长的超时时间，因为登录可能需要更长时间
      LoadingManager.instance.show(context, timeoutSeconds: 100);
      
      if (await account.signIn(type: signinType)) {
        if (!mounted) return;
        // 先回调通知登录成功
        widget.onSelected(signinType);
        // 延迟关闭页面，确保回调处理完成
        await Future.delayed(const Duration(milliseconds: 100));
        if (!mounted) return;
        _inController.reverse();
      } else {
        debugPrint("登录取消");
        // Util.showToast("Login Failed");
      }
    } catch (e) {
      debugPrint("登录错误: $e");
      ToastWidget.showError("Login Error");
    } finally {
      // await Future.delayed(const Duration(milliseconds: 300));
      // if (mounted) {
        LoadingManager.instance.hide(context);
      // }
    }
  }

  @override
  Widget build(BuildContext context) {
    _winSize = MediaQuery.of(context).size;
    return Material(
      color: Colors.transparent,
      child: Container(
        width: _winSize.width,
        height: _winSize.height,
        color: ThemeManager.currentTheme.backgroundColor,
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0), // 从右侧开始
            end: const Offset(0, 0),     // 滑动到中心位置
          ).animate(_inController),
          child: Stack(
            children: [
              // 背景图片
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Image.asset(
                  'images/login.png',
                  fit: BoxFit.contain,
                  alignment: Alignment.topCenter,
                ),
              ),
              // 关闭按钮
              Positioned(
                right: 20,
                top: 60,
                child: IconButton(
                  icon: Image.asset(ThemeManager.getImagePath('btn_close'), width: 40, height: 40),
                  onPressed: () {
                    _inController.reverse(); // 这里会触发反向动画，使页面向右滑出
                  },
                ),
              ),
              // 主内容
              Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Logo
                      Container(
                        width: 80,
                        // height: 100,
                        margin: const EdgeInsets.only(bottom: 40, top: 0),
                        child: Center(
                          child: Image.asset('images/logo.png'),
                        ),
                      ),
                      // 登录文本
                      Container(
                        // margin: const EdgeInsets.only(bottom: 0),
                        child: Text(
                          AppLocalizations.of(context)!.loginTitle,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 30,
                            fontWeight: FontWeight.bold,
                            color: ThemeManager.currentTheme.textColor,
                            letterSpacing: -0.5,
                            height: 1.2,
                          ),
                        ),
                      ),
                      // Container(
                      //   // margin: const EdgeInsets.only(bottom: 30),
                      //   child: Text(
                      //     "Account",
                      //     style: TextStyle(
                      //       fontSize: 30,
                      //       fontWeight: FontWeight.bold,
                      //       color: ThemeManager.currentTheme.labelBlack,
                      //       letterSpacing: -0.5,
                      //     ),
                      //   ),
                      // ),
                      const SizedBox(height: 180),
                      // 邮箱输入框
                      // _buildInputField(_emailController, "Email"),
                      // 密码输入框
                      // _buildInputField(_passwordController, "Password", isPassword: true),
                      // 忘记密码
                      // _buildForgotPassword(),
                      // 登录按钮
                      // _buildLoginButton(),
                      // 没有账号文本
                      // _buildNoAccountText(),

                      // Google登录按钮
                      _buildSocialLoginButton(
                        "Google",
                        Color(0xffeeeeee),
                        SvgPicture.asset('images/icons/icon_google.svg', width: 22, height: 22),
                      ),
                      // Apple登录按钮 - 只在iOS上显示
                      if (Platform.isIOS)
                        _buildSocialLoginButton(
                          "Apple",
                          Color(0xffeeeeee),
                          Icon(Icons.apple, size: 28, color: Color(0xff1b1c1e)),
                        ),
                      // Auth0登录按钮
                      _buildSocialLoginButton(
                        "Email",
                        Color(0xffeeeeee),
                        Icon(Icons.email, size: 24, color: Color(0xff4589ff)),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
