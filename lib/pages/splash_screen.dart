import 'package:flutter/material.dart';
import 'package:imtrans/util/config.dart';
import 'package:imtrans/util/theme_manager.dart';
import '../services/account.dart';
import 'package:imtrans/util/util.dart';
import 'package:imtrans/widgets/splash_animation.dart';
import 'index.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  int _aniIndex = 0;
  Account? _account;
  bool _showGuide = true;

  @override
  void initState() {
    super.initState();
    init();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    Util.setMainContext(context);
    // DialogManager.instance.setMainContext(context);
  }

  void init() async {
    WidgetsFlutterBinding.ensureInitialized();
    await ThemeManager.initSystemThemeColor();
    await Util.initPath();

    _account = await Account.instance;
    if (_account!.signinToken == null) {
      await _account!.signIn();
    }
    
    // 这里控制是否显示引导页 
    // _showGuide = await GuideState.shouldShowGuide("${_account!.info["uid"]}");
    _showGuide = false;
    
    // await ProductList.instance().initData();
    // await Util.getFreeLimit(_account!);

    if (mounted) {
      setState(() {
        _aniIndex = 1;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Global().initStatusBarHeight(context);
    return SplashAnimation(
      images: Config.getAssertList(_aniIndex),
      onAnimationFinish: () {
        Util.setMainContext(context);
        // DialogManager.instance.setMainContext(context);
        if (_showGuide) {
          // Util.navigatorReplace(const Guide());
        } else {
          // Util.navigatorReplace(MainView());
          Util.navigatorReplace(IndexPage());
        }
      },
      endIndex: 14,
    );
  }

  @override
  void dispose() {
    Util.popNull(widget);
    super.dispose();
  }
}
