import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';

/// 包含折扣价格的产品详情
class ProductWithDiscount {
  final String id;
  final String title;
  final String description;
  final String price;
  final double rawPrice;
  final String currencySymbol;
  final String discountPrice;
  final ProductDetails originalProduct; // 保留原始产品对象以便需要时使用

  ProductWithDiscount({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.rawPrice,
    required this.currencySymbol,
    required this.discountPrice,
    required this.originalProduct,
  });

  // 从ProductDetails创建ProductWithDiscount的工厂构造函数
  factory ProductWithDiscount.fromProduct(ProductDetails product, String discountPrice) {
    return ProductWithDiscount(
      id: product.id,
      title: product.title,
      description: product.description,
      price: product.price,
      rawPrice: product.id == "PROTRIAL2025003" ? 0 : double.parse(product.rawPrice.toStringAsFixed(2)),
      // rawPrice: discountPrice == "0" ? 0 : (discountPrice.isNotEmpty ? double.tryParse(discountPrice.replaceAll(product.currencySymbol, "")) ?? product.rawPrice : product.rawPrice),
      currencySymbol: product.currencySymbol,
      discountPrice: "${product.currencySymbol}${discountPrice.replaceAll(RegExp(r'[^0-9.]'), '')}",
      originalProduct: product,
    );
  }
}

/// 处理跨平台的内购购买流程
class IAPPurchases {
  static final IAPPurchases _instance = IAPPurchases._internal();
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  Function()? _onSuccess;
  Function(String)? _onError;
  Function()? _onCancel;

  // 添加标记来追踪恢复状态 避免多次调用回调方法
  bool _isRestoring = false;

  // 平台特定的商品ID
  static const Map<String, Set<String>> _platformProductIds = {
    'ios': {
      "PROTRIAL2025003",
      "PROWEEK2025002",
      "PROANNUAL2025004",
    },
    'android': {
      "protrial_2025_003",
      "proweek_2025_002",
      "proannual_2025_004",
    },
  };

  // 获取当前平台的商品ID
  static Set<String> get _productIds {
    if (Platform.isIOS) {
      return _platformProductIds['ios']!;
    } else if (Platform.isAndroid) {
      return _platformProductIds['android']!;
    } else {
      throw UnsupportedError('Unsupported platform for in-app purchases');
    }
  }

  // 商品名称映射
  static const Map<String, String> productNames = {
    // iOS 商品ID
    "PROTRIAL2025003": "Free Trial",
    "PROWEEK2025002": "Weekly",
    "PROANNUAL2025004": "Annual",
    // Android 商品ID
    "protrial_2025_003": "Free Trial",
    "proweek_2025_002": "Weekly",
    "proannual_2025_004": "Annual",
  };

  // 获取商品名称的静态方法
  static String getProductName(String productId) {
    return productNames[productId] ?? "Subscription";
  }

  factory IAPPurchases() {
    return _instance;
  }

  IAPPurchases._internal() {
    final Stream<List<PurchaseDetails>> purchaseUpdated = _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen(
      _onPurchaseUpdate,
      onDone: _onDone,
      onError: _onError,
    );
    
    // 应用启动时清理未完成的交易
    clearTransactions();
  }

  /// 检查商店可用性并获取商品列表
  ///
  /// 该方法执行以下步骤：
  /// 1. 检查应用商店是否可用
  /// 2. 查询指定商品ID的详细信息
  /// 3. 记录未找到的商品ID
  ///
  /// 参数：
  /// - [productIds]: 需要查询的商品ID集合
  ///
  /// 返回值：
  /// - 返回 [List<ProductDetails>] 包含查询到的商品详情列表
  /// - 如果商店不可用或发生错误，返回空列表
  ///
  /// 异常处理：
  /// - 捕获所有异常并打印错误信息
  /// - 发生异常时返回空列表
  Future<List<ProductWithDiscount>> fetchProducts() async {
    try {
      // 1. 检查商店是否可用
      final bool isAvailable = await _inAppPurchase.isAvailable();
      if (!isAvailable) {
        debugPrint('App Store is not available');
        return [];
      }

      // 2. 使用内部定义的商品ID查询商品详情
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);

      // 3. 检查是否有未找到的商品
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('Product IDs not found: ${response.notFoundIDs}');
      }

      List<ProductWithDiscount> productsWithDiscount = [];

      for (var product in response.productDetails) {
        debugPrint('完整的ProductDetails对象: ${product.toString()}');
        String discountPrice = calculateDiscountPrice(product);
        // String currencySymbol = product.currencySymbol;
        // debugPrint('Product found: ID: ${product.id}, Title: ${product.title}, Price: ${product.rawPrice}, Discount: ${discountPrice}');
        productsWithDiscount.add(ProductWithDiscount.fromProduct(product, discountPrice));
        
        // productsWithDiscount.rawPrice = product.rawPrice;
        // if (!discountPrice.startsWith(currencySymbol)) {
        //   discountPrice = '$currencySymbol$discountPrice';
        // }
      }
      // 打印商品列表信息
      for (var product in productsWithDiscount) {
        debugPrint('''
        商品详情:
        - ID: ${product.id}
        - 标题: ${product.title}
        - 描述: ${product.description}
        - 原价: ${product.price}
        - 折扣价: ${product.discountPrice}
        - 货币符号: ${product.currencySymbol}
        - 原始价格: ${product.rawPrice}
        ''');
      }

      // 4. 按价格从低到高排序
      productsWithDiscount.sort((a, b) => a.rawPrice.compareTo(b.rawPrice));

      // return response.productDetails;
      return productsWithDiscount;
      // return makeMokeProducts();
    } catch (e) {
      debugPrint('Error fetching product list: $e');
      return [];
    }
  }

  // 创建假的ProductDetails用于调试
  List<ProductDetails> makeMokeProducts() {
    List<ProductDetails> mockProducts = [];
    
    // 为每个产品ID创建一个模拟产品
    var i = 1;
    for (String id in _productIds) {
      mockProducts.add(
        ProductDetails(
          id: id,
          title: i % 2 == 1 ? 'Monthly $id' : 'Weekly $id',
          description: '这是一个用于调试的模拟商品',
          price: i % 2 == 1 ? '\$19.99' : '\$4.99',
          rawPrice: i % 2 == 1 ? 19.99 : 4.99,
          currencyCode: 'USD',
        )
      );
      i++;
    }
    
    debugPrint('返回模拟商品数据用于调试:');
    for (var product in mockProducts) {
      debugPrint('Mock Product: ID: ${product.id}, Title: ${product.title}, Price: ${product.price}');
    }
    
    return mockProducts;
  }

  // 计算折扣价格的方法
  String calculateDiscountPrice(var item) {
    String price = item.price;
    String discountPrice = price;
    if (item is AppStoreProductDetails) {
      final skProduct = item.skProduct;
      // 检查是否有促销价格
      if (skProduct.introductoryPrice != null) {
        discountPrice = skProduct.introductoryPrice!.price;
      } else if (skProduct.discounts.isNotEmpty) {
        // 如果有折扣，使用第一个折扣价格
        discountPrice = skProduct.discounts.first.price;
      }
    }
    return discountPrice;
  }

  /// 检查是否有待处理的交易
  Future<bool> _hasPendingTransactions() async {
    if (Platform.isIOS) {
      try {
        final SKPaymentQueueWrapper queue = SKPaymentQueueWrapper();
        final transactions = await queue.transactions();

        if (transactions.isNotEmpty) {
          debugPrint('Found ${transactions.length} transactions in queue');
          for (var transaction in transactions) {
            debugPrint('''
            Transaction in queue:
            - ID: ${transaction.transactionIdentifier}
            - State: ${transaction.transactionState}
            - Product ID: ${transaction.payment.productIdentifier}
            - Time: ${transaction.transactionTimeStamp}
            ''');
          }

          // 检查是否有任何交易正在进行中
          return transactions.any((t) =>
            t.transactionState == SKPaymentTransactionStateWrapper.purchasing ||
            t.transactionState == SKPaymentTransactionStateWrapper.deferred
          );
        }
      } catch (e) {
        debugPrint('Error checking pending transactions: $e');
      }
    } else if (Platform.isAndroid) {
      // Android平台的Google Play Billing不需要手动检查待处理交易
      // Google Play Billing会自动处理待处理的交易
      debugPrint('Android platform: Google Play Billing handles pending transactions automatically');
      return false;
    }
    return false;
  }

  /// 发起商品购买流程
  ///
  /// 该方法执行以下步骤：
  /// 1. 验证商店是否可用
  /// 2. 清理未完成的交易
  /// 3. 查询并验证商品信息
  /// 4. 发起非消耗型商品的购买请求
  ///
  /// 参数：
  /// - [productId]: 要购买的商品ID
  /// - [uuid]: 用户唯一标识符
  /// - [onSuccess]: 购买成功时的回调函数
  /// - [onError]: 购买失败时的回调函数，接收错误信息字符串作为参数
  ///
  /// 错误处理：
  /// - 商店不可用时触发错误回调
  /// - 商品未找到时触发错误回调
  /// - 购买过程中发生异常时触发错误回调
  ///
  /// 注意：
  /// - 实际购买结果会通过 [_onPurchaseUpdate] 方法异步处理
  /// - 这里仅发起购买请求，不处理购买结果
  Future<void> buyProduct(String productId,
      {required String uuid,
      required Function() onSuccess,
      required Function(String) onError,
      Function()? onCancel}) async {
    
    // 检查是否有待处理的交易
    if (await _hasPendingTransactions()) {
      
      onError('A purchase is already in progress. Please wait for it to complete.');
      return;
    }
    
    _onSuccess = onSuccess;
    _onError = onError;
    _onCancel = onCancel;
    
    try {
      // 1. 检查商店是否可用
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        onError('App Store is not available');
        return;
      }

      // 清理未完成的交易
      await clearTransactions();

      // 2. 获取商品详情
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails({productId});

      if (response.notFoundIDs.isNotEmpty) {
        onError('Product not found');
        return;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: response.productDetails.first,
        applicationUserName: uuid,
      );
      debugPrint('''
      Buy product called with params:
      - Product ID: ${purchaseParam.productDetails.id}
      - Product Title: ${purchaseParam.productDetails.title}
      - Product Description: ${purchaseParam.productDetails.description}
      - Product Price: ${purchaseParam.productDetails.price}
      - Product Currency: ${purchaseParam.productDetails.currencyCode}
      - User ID: ${purchaseParam.applicationUserName}
      ''');
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    } catch (e) {
      debugPrint('Purchase failed: $e');
      
      // 检查是否是用户取消
      // Check if it's user cancellation on different platforms
      if (e is SKError && e.code == 2) {
        // iOS平台用户取消 (SKError code 2)
        debugPrint('Purchase cancelled by user at method level (iOS)');
        _onCancel?.call();
        return; // 用户主动取消，不弹出错误提示
      }
      
      // 检查是否包含用户取消的错误信息
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('user') && errorString.contains('cancel')) {
        debugPrint('Purchase cancelled by user at method level (detected from error message)');
        _onCancel?.call();
        return; // 用户主动取消，不弹出错误提示
      }
      
      _onError?.call(e.toString());
    }
  }

  /// 处理购买状态更新的回调方法
  ///
  /// 该方法负责处理所有购买状态的变化，包括：
  /// - 待处理（pending）
  /// - 错误（error）
  /// - 已购买（purchased）
  /// - 已恢复（restored）
  /// - 已取消（canceled）
  ///
  /// 参数：
  /// - [purchaseDetails]: 包含购买详情的列表，每个元素代表一个购买交易
  ///
  /// 处理流程：
  /// 1. 对于每个购买交易，根据其状态进行相应处理
  /// 2. 对于已购买或已恢复的交易：
  ///    - 验证购买的有效性
  ///    - 完成交易流程
  ///    - 触发相应的回调
  ///
  /// 注意：
  /// - 这是一个异步回调方法，会在购买状态发生变化时被自动调用
  /// - 所有购买状态的变化都会触发此方法
  // 用于跟踪已处理的交易ID，防止重复处理
  final Set<String> _processedTransactions = {};
  
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetails) async {
    if (purchaseDetails.isEmpty) return;

    try {
      for (var purchase in purchaseDetails) {
        final String transactionId = purchase.purchaseID ?? 'unknown';
        
        // 检查是否已经处理过该交易，避免重复处理
        if (purchase.status == PurchaseStatus.purchased && 
            _processedTransactions.contains(transactionId)) {
          debugPrint('Skipping already processed transaction: $transactionId');
          continue;
        }
        
        debugPrint('Purchase status: ${purchase.status}');

        switch (purchase.status) {
          case PurchaseStatus.pending:
            debugPrint('Purchase pending...');
            break;

          case PurchaseStatus.error:
            final errorMessage = purchase.error?.message ?? 'Unknown error';
            debugPrint('Purchase error: $errorMessage');
            debugPrint('Error details: ${purchase.error?.details}');
            
            // 检查是否是用户取消引起的错误
            // Check if the error is caused by user cancellation
            final errorString = errorMessage.toLowerCase();
            if (errorString.contains('user') && errorString.contains('cancel')) {
              debugPrint('Purchase error caused by user cancellation, not showing error dialog');
              await _inAppPurchase.completePurchase(purchase);
              _onCancel?.call();
              return; // 用户主动取消，不弹出错误提示
            }
            
            await _inAppPurchase.completePurchase(purchase);
            _onError?.call('Purchase failed: $errorMessage');
            break;

          case PurchaseStatus.purchased:
          case PurchaseStatus.restored:
            if (purchase.status == PurchaseStatus.restored && _isRestoring) {
              if (await _verifyPurchase(purchase)) {
                debugPrint('Restore verification successful');
              }
            } else if (purchase.status == PurchaseStatus.purchased) {
              // 标记交易为已处理
              _processedTransactions.add(transactionId);
              
              if (await _verifyPurchase(purchase)) {
                debugPrint('Purchase verification successful');
                _onSuccess?.call();
              } else {
                _onError?.call('Purchase verification failed');
              }
            }
            await _inAppPurchase.completePurchase(purchase);
            break;

          case PurchaseStatus.canceled:
            await _inAppPurchase.completePurchase(purchase);
            debugPrint('Purchase cancelled by user');
            // 用户主动取消购买，调用取消回调来重置UI状态
            // User actively cancelled purchase, call cancel callback to reset UI state
            _onCancel?.call();
            break;
        }
      }

      // 所有恢复的购买都处理完成后，只调用一次回调
      if (_isRestoring) {
        _isRestoring = false;
        _onSuccess?.call();
      }

    } catch (e) {
      _isRestoring = false;
      debugPrint('Error during purchase process: $e');
      _onError?.call('Error during purchase process: ${e.toString()}');
    }
  }

  /// 验证购买交易的有效性
  ///
  /// 该方法执行以下验证：
  /// 1. 检查验证数据是否存在
  /// 2. 验证交易状态的合法性
  /// 3. 验证商品ID的有效性
  /// 4. 验证收据数据
  /// 5. 对于iOS设备，获取并记录详细的交易信息
  ///
  /// 参数:
  /// - [purchaseDetails]: 购买详情对象，包含交易信息
  ///
  /// 返回:
  /// - 如果验证通过返回true，否则返回false
  ///
  /// 注意：
  /// - 该方法主要用于本地基础验证
  /// - 完整的验证流程应该包含服务器端验证
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    if (purchaseDetails.verificationData.localVerificationData.isEmpty) {
      debugPrint('Purchase verification data is empty');
      return false;
    }

    try {
      // 检查交易状态
      if (purchaseDetails.status != PurchaseStatus.purchased && purchaseDetails.status != PurchaseStatus.restored) {
        throw 'Invalid purchase status: ${purchaseDetails.status}';
      }

      // 检查是否有productID
      if (purchaseDetails.productID.isEmpty) {
        throw 'Product ID is empty';
      }

      // 验证 local receipt
      final localReceipt = purchaseDetails.verificationData.localVerificationData;
      if (localReceipt.isEmpty) {
        throw 'Receipt verification failed';
      }

      return true;
    } catch (e) {
      debugPrint('Error during purchase verification: $e');
      return false;
    }
  }

  void _onDone() {
    debugPrint('Purchase process completed');
    _subscription.cancel();
    _isRestoring = false;
    _onSuccess = null;
    _onError = null;
    _onCancel = null;
  }

  /// 恢复之前的购买记录
  ///
  /// 该方法用于：
  /// - 恢复用户之前在该设备或其他设备上的购买
  /// - 适用于非消耗型商品的恢复
  ///
  /// 参数：
  /// - [uuid]: 用户唯一标识符
  /// - [onSuccess]: 恢复成功的回调
  /// - [onError]: 恢复失败的回调
  ///
  /// 错误处理：
  /// - 捕获并处理恢复过程中的所有异常
  /// - 通过onError回调返回错误信息
  Future<void> restorePurchases({required String uuid, required Function() onSuccess, required Function(String) onError}) async {
    debugPrint('Restore purchases called');
    try {
      _isRestoring = true; // 开始恢复时设置标记
      _onSuccess = onSuccess;
      _onError = onError;
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      _isRestoring = false; // 发生错误时重置标记
      debugPrint('Restore purchases failed: $e');
      // 检查是否是用户取消 主动取消不弹出错误提示
      if (e is SKError && e.code == 2) {
        return;
      }
      // 输出错误的详细信息
      if (e is SKError) {
        debugPrint('SKError code: ${e.code}');
        debugPrint('SKError domain: ${e.domain}');
        debugPrint('SKError userInfo: ${e.userInfo}');
      }
      onError(e.toString());
    }
  }

  /// 清理未完成的交易
  ///
  /// 该方法用于：
  /// - 清理队列中的未完成交易
  /// - 防止交易堆积导致的问题
  ///
  /// 注意：
  /// - iOS平台上执行实际清理操作
  /// - Android平台由Google Play Billing自动处理
  /// - 建议在发起新购买前调用此方法
  Future<void> clearTransactions() async {
    if (Platform.isIOS) {
      try {
        final SKPaymentQueueWrapper queue = SKPaymentQueueWrapper();
        final transactions = await queue.transactions();

        if (transactions.isEmpty) {
          // debugPrint('No transactions to clear');
          return;
        }

        debugPrint('Clearing ${transactions.length} transactions');
        for (var transaction in transactions) {
          // 只完成非purchasing状态的交易，避免中断正在进行的购买
          if (transaction.transactionState != SKPaymentTransactionStateWrapper.purchasing) {
            await queue.finishTransaction(transaction);
            debugPrint('Finished transaction: ${transaction.transactionIdentifier} (State: ${transaction.transactionState})');
          } else {
            debugPrint('Skipped purchasing transaction: ${transaction.transactionIdentifier}');
          }
        }
      } catch (e) {
        debugPrint('Error clearing transactions: $e');
      }
    } else if (Platform.isAndroid) {
      // Android平台的Google Play Billing自动处理交易清理
      debugPrint('Android platform: Transaction clearing handled by Google Play Billing');
    }
  }

  void dispose() {
    _subscription.cancel();
    _onSuccess = null;
    _onError = null;
    _onCancel = null;
    _isRestoring = false;
  }
}
