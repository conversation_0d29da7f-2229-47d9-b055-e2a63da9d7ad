import 'package:flutter/material.dart';
// import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'base_extractor.dart';
import 'fanfox_extractor.dart';
import 'generic_extractor.dart';
import 'mangadex_extractor.dart';
import 'mangahere_extractor.dart';

/// 图片提取器工厂类
/// 负责根据URL创建合适的图片提取器实例
class ExtractorFactory {
  // 私有构造函数，防止直接实例化
  ExtractorFactory._();
  
  // 网站提取器映射配置
  static final Map<String, bool Function(String)> _siteMatchers = {
    'fanfox': (url) => url.contains('fanfox') || url.contains('mangafox'),
    'mangadex': (url) => url.contains('mangadex.org'),
    'a': (url) => url.contains('mangahere'),
  };
  
  /// 创建图片提取器实例
  /// 根据URL自动选择合适的提取器
  static IImageExtractor createExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) {
    
    // 检查是否匹配特定网站
    if (_siteMatchers['fanfox']?.call(currentUrl) ?? false) {
      debugPrint("使用fanfox解析器");
      return FanfoxExtractor(
        controller: controller,
        currentUrl: currentUrl,
      );
    }
    
    if (_siteMatchers['mangadex']?.call(currentUrl) ?? false) {
      debugPrint("使用mangadex解析器");
      return MangaDexExtractor(
        controller: controller,
        currentUrl: currentUrl,
      );
    }
    // debugPrint('是否匹配mangahere:');
    // debugPrint(_siteMatchers['site']?.call(currentUrl).toString());
    if (_siteMatchers['a']?.call(currentUrl) ?? false) {
      debugPrint("使用mangahere解析器");
      return MangaHereExtractor(
        controller: controller,
        currentUrl: currentUrl,
      );
    }
    
    // 默认使用通用提取器
    debugPrint("使用通用解析器");
    return GenericExtractor(
      controller: controller,
      currentUrl: currentUrl,
    );
  }
  
  /// 注册新的网站匹配器
  static void registerSiteMatcher(String siteKey, bool Function(String) matcher) {
    _siteMatchers[siteKey] = matcher;
  }
}