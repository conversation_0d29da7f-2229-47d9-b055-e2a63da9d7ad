import '../models/product_model.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import '../services/server_request.dart';

class ProductController {
  static final ProductController _instance = ProductController._internal();
  factory ProductController() => _instance;
  
  bool _isInitialized = false;
  static const int defaultPageSize = 20;
  static const List<double> defaultHeights = [130, 150, 180, 200];
  final ProductModel _model = ProductModel();
  List<Product> _products = [];
  bool _isLoading = false;
  // bool _isInitializing = false;

  // Getters
  static ProductController get instance => _instance;
  List<Product> get products => _products;
  bool get isLoading => _isLoading;
  ProductController._internal();

    // 已读状态更新的流控制器
  final _readStatusController = StreamController<String>.broadcast();
  Stream<String> get onReadStatusUpdated => _readStatusController.stream;

  // 产品列表更新的流控制器
  final _productStreamController = StreamController<void>.broadcast();
  Stream<void> get onProductsUpdated => _productStreamController.stream;

  void notifyProductsUpdated() {
    debugPrint('ProductController: Sending product update notification');
    if (!_productStreamController.isClosed) {
      _productStreamController.add(null);
    } else {
      debugPrint('ProductController: Stream controller is closed!');
    }
  }

  /// 初始化产品控制器
  /// @param waitForTranslations 是否等待翻译完成
  /// 当手动刷新时设为true，自动刷新时设为false
  Future<void> initialize({bool waitForTranslations = false}) async {
    // 如果已经初始化完成，直接返回
    if (_isInitialized) return;

    try {
      final cachedProducts = await _model.loadFromCache();
      if (cachedProducts.isNotEmpty) {
        _products = cachedProducts;
      }
      else {
        _products = await _model.loadFromNetwork(page: 1, pageSize: defaultPageSize);
        if (waitForTranslations) {
          // 等待翻译加载完成
          await _loadProductsTranslations(_products, saveToCache: true);
        } else {
          // 后台加载翻译
          _loadProductsTranslations(_products, saveToCache: true);
        }
      }
    } catch (e) {
      debugPrint('Error initializing ProductController: $e');
      rethrow;
    } finally {
      _isInitialized = true;
    }
  }

  /// 异步加载产品翻译结果
  /// @param saveToCache 是否在加载完成后保存到缓存
  Future<void> _loadProductsTranslations(List<Product> products, {bool saveToCache = false}) async {
    try {
      // 创建一个临时列表来存储需要更新的产品
      final List<Product> productsToUpdate = List<Product>.from(products);
      // 创建一个临时映射来存储产品ID到更新后的产品的映射
      final Map<String?, Product> updatedProductsMap = {};
      
      for (var product in productsToUpdate) {
        if (product.productId == null) {
          continue;
        }
        
        try {
          final productInfo = await _model.query(product.productId!);
          if (productInfo != null) {
            // 将更新后的产品存储在映射中
            updatedProductsMap[product.productId] = productInfo;
          }
        } catch (e) {
          // 处理特定的ProductModelException异常
          if (e is ProductModelException) {
            debugPrint('ProductModelException: ${e.message} (code: ${e.code})');
            // 可以根据不同的错误代码进行不同的处理
            if (e.code == Product.RETURN_CODE_NEEDVIP) {
              debugPrint('VIP required for this operation');
            }
          } else {
            debugPrint('Error loading translations for product ${product.productId}: $e');
          }
          // 继续处理下一个产品，而不是完全中断
          continue;
        }
      }

      // 一次性更新_products列表中的产品
      if (updatedProductsMap.isNotEmpty) {
        // 创建一个新的列表来避免并发修改
        final List<Product> newProducts = [];
        
        for (var product in _products) {
          if (product.productId != null && updatedProductsMap.containsKey(product.productId)) {
            // 使用更新后的产品
            newProducts.add(updatedProductsMap[product.productId]!);
          } else {
            // 保留原始产品
            newProducts.add(product);
          }
        }
        
        // 替换整个列表
        _products = newProducts;
      }

      // 保存到缓存
      if (saveToCache) {
        try {
          await _model.saveToCache(_products);
          debugPrint('Products saved to cache after translations loaded');
        } catch (e) {
          debugPrint('Error saving products to cache: $e');
        }
      }
    } catch (e) {
      debugPrint('Error in _loadProductsTranslations: $e');
      rethrow;
    }
  }

  Future<List<Product>?> list({int page = 1, int pageSize = defaultPageSize, bool forceRefresh = false}) async {
    debugPrint('ProductController.list 被调用: page=$page, pageSize=$pageSize, forceRefresh=$forceRefresh');
    
    // 如果已初始化且不是强制刷新，直接返回缓存数据
    if (page == 1 && !forceRefresh) {
      // 确保有数据才返回缓存
      if (_products.isNotEmpty) {
        debugPrint('ProductController.list 返回缓存数据: ${_products.length} 条');
        return _products;
      }
    }

    try {
      debugPrint('ProductController.list 开始加载网络数据');
      _isLoading = true;
      final products = await _model.loadFromNetwork(page: page, pageSize: pageSize);
      debugPrint('ProductController.list 网络数据加载完成: ${products.length} 条');
      
      if (products.isNotEmpty) {
        if (page == 1) {
          _products = products;
        } else {
          debugPrint('ProductController.list 追加产品列表: 从 ${_products.length} 增加 ${products.length} 条');
          _products.addAll(products);
        }
      }
      
      debugPrint('ProductController.list 保存数据到缓存');
      _model.saveToCache(products);
      return products;
    } 
    catch (e) {
      debugPrint('ProductController.list 加载产品出错: $e');
      return null;
    } 
    finally {
      _isLoading = false;
      debugPrint('ProductController.list 完成: _isLoading = $_isLoading');
    }
  }

  // Future<List<Product>> _loadFromNetwork({required int page, required int pageSize}) async {
  //   final response = await _model.list(page: page, pageSize: pageSize);
  //   if (response != null && response["code"] == Product.RETURN_CODE_SUCCESS) {
  //     final List<dynamic> productsData = response["list"] ?? [];
  //     final products = productsData.map((data) => Product.fromMap(data)).toList();
      
  //     // 加载元数据（已读状态等）
  //     await _loadProductMetadata(products);

  //     // 只在加载第一页时保存到缓存
  //     if (page == 1) {
  //       await _model.saveToCache(products);
  //     }

  //     return products;
  //   }
  //   return [];
  // }

  /// 创建新的产品
  /// 
  /// [imageDatas] 要上传的图片数据列表（Uint8List格式）
  /// [targetLang] 目标翻译语言
  /// [name] 可选的产品名称
  /// [imageNames] 可选的图片名称列表
  /// 
  /// 创建成功返回产品ID，失败返回null
  Future<String?> create(List<Uint8List> imageDatas, String targetLang, {String? name, List<String>? imageNames}) async {
    try {
      final newProduct = Product();

      // 1. 创建并上传封面
      debugPrint("create cover image");
      final coverData = await _model.createCoverImage(imageDatas[0]);
      if (coverData == null) {
        throw Exception("Failed to create cover image");
      }
      final coverUrl = await _model.uploadImage(coverData);
      if (coverUrl == null) {
        throw Exception("Failed to upload cover image");
      }

      // 2. 上传图片
      debugPrint("upload image");
      for (var i = 0; i < imageDatas.length; i++) {
        final imageUrl = await _model.uploadImage(imageDatas[i]);
        if (imageUrl == null) {
          throw Exception("Failed to upload image ${i + 1}");
        }
        ProductImage newProductImage = ProductImage();
        newProductImage.setUrl(imageUrl);
        newProductImage.setName(imageNames?[i]);
        debugPrint("Created new ProductImage: ${newProductImage.name} with URL: ${imageUrl}");
        newProduct.addImage(newProductImage);
      }

      // 3. 创建产品
      debugPrint("create product");
      newProduct.setName(name);
      newProduct.setTargetLang(Product.targetLanguages[targetLang]);
      newProduct.setCoverUrl(coverUrl);

      final productId = await _model.create(newProduct);
      if (productId == null) {
        throw Exception("Failed to create product");
      }

      // 创建成功后通知刷新
      // _notifyRefreshNeeded();
      
      return productId;
    } catch (e) {
      if (e is ProductModelException && e.code == Product.RETURN_CODE_NEEDVIP) {
        throw("Free limit exceeded. Please purchase VIP");
      }
      else {
        debugPrint('Error creating product: $e');
        throw Exception("An error occurred. Please try again.");
      }
    }
  }

  /// 等待翻译完成
  Future<Product?> waitForTranslation(String productId) async {
    try {
      bool isFirstQuery = true;
      int attempts = 0;
      const maxAttempts = 20;
      
      while (attempts < maxAttempts) {
        attempts++;
        
        await Future.delayed(
          Duration(seconds: isFirstQuery ? 2 : 1)
        );
        isFirstQuery = false;

        try {
          final product = await _model.query(productId);
          if (product == null) continue;
          int imageCount = product.images.length;
          for (var image in product.images) {
            if (image.translateStatus == Product.STATUS_TRANSLATED) {
              imageCount--;
            }
          }
          if (imageCount == 0) {
            product.setStatus(Product.STATUS_TRANSLATED);
            // 保存翻译结果
            await _model.saveImagesToCache(product);
            return product;
          }
        } catch (e) {
          // 处理特定的ProductModelException异常
          if (e is ProductModelException) {
            debugPrint('ProductModelException in waitForTranslation: ${e.message} (code: ${e.code})');
            if (e.code == Product.RETURN_CODE_NEEDVIP) {
              debugPrint('VIP required for this operation');
              return null; // 需要VIP时直接返回null
            }
          } else {
            debugPrint('Error querying product in waitForTranslation: $e');
          }
          // 继续尝试下一次查询
          continue;
        }
      }
      throw Exception("Translation timed out");
    } catch (e) {
      debugPrint('Error waiting for translation: $e');
      return null;
    }
  }

  Future<Product?> query(String productId) async {
    try {
      return await _model.query(productId);
    } catch (e) {
      // 处理特定的ProductModelException异常
      if (e is ProductModelException) {
        debugPrint('ProductModelException in query: ${e.message} (code: ${e.code})');
        if (e.code == Product.RETURN_CODE_NEEDVIP) {
          debugPrint('VIP required for this operation');
        }
      } else {
        debugPrint('Error querying product: $e');
      }
      return null;
    }
  }

  /// 将产品标记为已读
  Future<bool> markAsRead(String productId) async {
    try {
      await _model.setRead(productId, true);
       _readStatusController.add(productId);
      return true;
    } catch (e) {
      debugPrint('Error marking product as read: $e');
      return false;
    }
  }

  Future<bool> rename(String productId, String newName) async {
    try {
      final success = await _model.update({
        "product_id": productId,
        "product_name": newName,
      });
      return success;
    } catch (e) {
      debugPrint('Error renaming product: $e');
      return false;
    }
  }

  /// 删除产品
  /// @param productId 要删除的产品ID
  /// @return 删除是否成功
  Future<bool> delete(String productId) async {
    try {
      final success = await _model.delete(productId);

      // 2. 清除本地已读状态
      try {
        final prefs = await SharedPreferences.getInstance();
        final key = 'product_read_$productId';
        await prefs.remove(key);
      } catch (e) {
        debugPrint('Error clearing read status: $e');
        // 继续执行，不影响删除结果
      }

      if (success) {
        notifyProductsUpdated();
      }

      return success;
    } catch (e) {
      debugPrint('Error deleting product: $e');
      return false;
    }
  }

  /// 批量删除产品
  /// @param productIds 要删除的产品ID列表
  /// @return 删除成功的产品ID列表
  Future<List<String>> batchDelete(List<String> productIds) async {
    final List<String> successIds = [];
    
    for (final id in productIds) {
      if (await delete(id)) {
        successIds.add(id);
      }
    }
    return successIds;
  }

  static String generateImageName(String imageName) {
    DateTime specificTime = DateTime(2060, 3, 1, 12, 0, 0);
    int specificTimestamp = specificTime.microsecondsSinceEpoch;
    
    int currentTimeStamp = DateTime.now().microsecondsSinceEpoch;

    var name = "LG${specificTimestamp - currentTimeStamp}.${imageName.substring(imageName.lastIndexOf(".") + 1)}";
    return name;
  }

  /// 获取产品图片信息
  /// @param product 产品对象
  /// @return 包含图片高度、是否本地图片、图片URL的Map对象
  /// 返回的Map包含以下字段:
  /// - height: 图片高度
  /// - isLocal: 是否为本地缓存图片
  /// - imageUrl: 图片URL,优先使用本地缓存路径,否则使用远程URL
  Map<String, dynamic> getImageInfo(Product product) {
    final productHeight = getProductHeight(product); 
    // debugPrint('Default height for product ${product.productId}: $productHeight');
    
    return {
      'height': productHeight,
      'isLocal': product.localCoverPath != null,
      'imageUrl': product.localCoverPath ?? product.coverUrl ?? "",
    };
  }

  // 根据产品ID信息确定性地选择一个默认高度
  double getProductHeight(Product product) {
    // 使用产品ID作为种子
    String seed = product.productId.toString();

    // 使用字符串的字符ASCII码之和作为种子
    int sum = seed.codeUnits.fold(0, (prev, curr) => prev + curr);

    // 使用种子取模选择默认高度
    final index = sum % defaultHeights.length;
    return defaultHeights[index];
  }

  Product? getProductAt(int index) {
    if (index < 0 || index >= _model.products.length) {
      debugPrint('Invalid product index access: $index (length: ${_model.products.length})');
      return null;
    }
    return _model.products[index];
  }

  Future<void> clean() async {
    _products.clear();
    _isInitialized = false;
    _isLoading = false;
    // _isInitializing = false;
    await _model.clearCache();
    notifyProductsUpdated();
  }

  Future<void> refresh({bool waitForTranslations = false}) async {
    try {
      _isLoading = true;
      _isInitialized = false;
      await _model.clearCache();
      await initialize(waitForTranslations: waitForTranslations);
      notifyProductsUpdated();
    } finally {
      _isLoading = false;
    }
  }

  /// 获取今日剩余可用图片数量
  /// 返回剩余数量，如果发生错误返回-1
  Future<int> getQuota() async {
    try {
      final response = await ServerRequest.getData(ServerRequest.freeLimit, {
        'scene': 'image_translate'
      });

      if (response['code'] == 0) {
        final maxCount = response['data']['limit_total'] as int;
        final usedCount = response['data']['used'] as int;
        return maxCount - usedCount; 
      } else {
        debugPrint('Error getting remaining image count: ${response['message']}');
        return -1;
      }
    } catch (e) {
      debugPrint('Error getting remaining image count: $e');
      return -1;
    }
  }

  /// 增加配额
  /// @param scene 业务场景，目前支持 image_translate
  /// @param limitAdd 增加的使用次数
  /// @param limitDate 可选的日期参数，格式：2025-04-25，默认为当前日期
  /// @return 是否增加成功
  Future<bool> increaseQuota(int amount, {String? limitDate}) async {
    try {
      final data = {
        'scene': "image_translate",
        'limit_add': amount,
      };
      
      if (limitDate != null) {
        data['limit_date'] = limitDate;
      } else {
        // 生成当前日期，格式：2025-04-25
        final now = DateTime.now();
        data['limit_date'] = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      }

      final response = await ServerRequest.postData(ServerRequest.increaseQuota, data);
      return response['code'] == 0;
    } catch (e) {
      debugPrint('Error increasing quota: $e');
      return false;
    }
  }
}
