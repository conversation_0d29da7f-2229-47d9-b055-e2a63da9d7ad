import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/util/dialog_manager.dart';
import 'package:imtrans/util/theme_manager.dart';
import '../../controllers/product_controller.dart';
import '../../models/product_model.dart';
import '../../util/util.dart';
import '../../widgets/breakall.dart';
import '../../widgets/common/image_widget.dart';
import '../../services/image_save_service.dart';
import 'folder_viewer.dart';
import 'image_viewer.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';

// 查看模式枚举
enum ViewMode {
  list,      // 列表模式
  smallGrid, // 小图模式  
  largeGrid, // 大图模式（当前的瀑布流）
}

class ProductListPage extends StatefulWidget {
  const ProductListPage({Key? key}) : super(key: key);

  @override
  State<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends State<ProductListPage> {
  final ProductController _controller = ProductController();
  final ScrollController _scrollController = ScrollController();
  StreamSubscription? _productSubscription;
  
  double _width = 0.0;
  
  List<Product> _products = [];
  bool _hasMore = true;
  int _page = 1;
  bool _isLoading = false;
  
  // 选择状态相关变量
  bool _isSelectMode = false;
  Set<int> _selectedIndices = {};
  bool _isAllSelected = false;
  
  // 查看模式相关变量
  ViewMode _currentViewMode = ViewMode.largeGrid;
  // bool _isDeleting = false;

  @override
  void initState() {
    super.initState();
    _setupScrollListener();
    _loadPage(1);

    // 产品更新监听
    _productSubscription = _controller.onProductsUpdated.listen((_) {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
        
        _loadPage(1, forceRefresh: true).then((_) {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        });
      }
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // 当滚动到距离底部200像素时就开始加载下一页，提前加载提高用户体验
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
        if (mounted && _hasMore && !_isLoading) {
          debugPrint('开始加载下一页: $_page');
          _loadNextPage();
        }
      }
    });
  }

  @override
  void dispose() {
    _productSubscription?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  // 数据加载方法
  Future<void> _loadPage(int page, {bool forceRefresh = false}) async {
    debugPrint('_loadPage 被调用: page=$page, forceRefresh=$forceRefresh, isLoading=$_isLoading');
    if (!mounted || _isLoading) {
      debugPrint('_loadPage 提前返回: mounted=$mounted, isLoading=$_isLoading');
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    debugPrint('开始请求数据: page=$page');
    final products = await _controller.list(
      page: page,
      pageSize: 20,
      forceRefresh: forceRefresh
    );

    debugPrint('数据请求结果: ${products != null ? '成功，获取 ${products.length} 条数据' : '失败'}');
    if (mounted) {
      setState(() {
        if (products == null || products.isEmpty) {
          // 如果返回空数据，表示没有更多数据了
          _hasMore = false;
          debugPrint('没有更多数据，停止翻页: hasMore=$_hasMore');
        } else {
          if (page == 1) {
            _products = products;
          } else {
            _products.addAll(products);
          }
          // 更新页码，只有当获取到数据时才更新
          _page = page + 1;
          // 保持hasMore为true，允许继续尝试加载
          _hasMore = true;
        }
        _isLoading = false;
        debugPrint('更新状态: hasMore=$_hasMore, nextPage=$_page, isLoading=$_isLoading');
      });
    }
  }

  Future<void> _loadNextPage() async {
    debugPrint('_loadNextPage 被调用: 当前页码=$_page');
    await _loadPage(_page);
  }

  // 获取当前查看模式对应的SVG图标路径
  String _getViewModeSvgPath() {
    switch (_currentViewMode) {
      case ViewMode.list:
        return ThemeManager.getImagePath('icon_view_list');
      case ViewMode.smallGrid:
        return ThemeManager.getImagePath('icon_view_sm');
      case ViewMode.largeGrid:
        return ThemeManager.getImagePath('icon_view_l');
    }
  }

  // 切换查看模式（按照大图-小图-列表的顺序循环）
  void _switchViewMode() {
    setState(() {
      switch (_currentViewMode) {
        case ViewMode.largeGrid:
          _currentViewMode = ViewMode.smallGrid;
          break;
        case ViewMode.smallGrid:
          _currentViewMode = ViewMode.list;
          break;
        case ViewMode.list:
          _currentViewMode = ViewMode.largeGrid;
          break;
      }
    });
  }



  // 构建AppBar
  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      toolbarHeight: 60,
      title: Container(
        height: 60,
        width: double.infinity,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 左侧按钮 - 根据模式显示返回或取消
            Positioned(
              left: 20,
              top: 0,
              bottom: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    if (_isSelectMode) {
                      // 退出选择模式
                      setState(() {
                        _isSelectMode = false;
                        _selectedIndices = {};
                        _isAllSelected = false;
                      });
                    } else {
                      // 返回上一页
                      Navigator.of(context).pop();
                    }
                  },
                  child: _isSelectMode 
                  ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: .2), 
                      border: Border.all(color: ThemeManager.currentTheme.textColor, width: 1),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor,
                        fontSize: 12,
                      ),
                    )
                  )
                  : Image.asset(
                      ThemeManager.getImagePath('btn_back'),
                      width: 40,
                      height: 40,
                    ),
                ),
              ),
            ),
            // 居中标题 - 选择模式下显示已选数量
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              child: Center(
                child: Text(
                  _isSelectMode 
                    ? AppLocalizations.of(context)!.selectedCount(_selectedIndices.length)
                    : AppLocalizations.of(context)!.allFiles,
                  style: TextStyle(
                    color: ThemeManager.currentTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // 右侧按钮区域 - 查看模式按钮和选择按钮
            Positioned(
              right: 20,
              top: 0,
              bottom: 0,
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 查看模式切换按钮 - 只有在非选择模式下显示
                    if (!_isSelectMode) ...[
                      GestureDetector(
                        onTap: () => _switchViewMode(),
                        child: Container(
                          width: 40,
                          height: 40,
                          child: SvgPicture.asset(
                            _getViewModeSvgPath(),
                            width: 40,
                            height: 40,
                            fit: BoxFit.contain,
                            // colorFilter: ColorFilter.mode(
                            //   ThemeManager.currentTheme.textColor,
                            //   BlendMode.srcIn,
                            // ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                    // 选择按钮
                    GestureDetector(
                      onTap: () {
                        if (_isSelectMode) {
                          // 全选/取消全选
                          setState(() {
                            if (_isAllSelected) {
                              _selectedIndices = {};
                            } else {
                              _selectedIndices = Set.from(List.generate(_products.length, (i) => i));
                            }
                            _isAllSelected = !_isAllSelected;
                          });
                        } else {
                          // 进入选择模式
                          setState(() {
                            _isSelectMode = true;
                            _selectedIndices = {};
                            _isAllSelected = false;
                          });
                        }
                      },
                      child: _isSelectMode
                        ? Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: const Color(0xFF1B1C1E),
                                width: 1,
                              ),
                              color: _isAllSelected ? ThemeManager.currentTheme.selectedItemColor : Colors.white,
                            ),
                            child: _isAllSelected
                              ? const Icon(Icons.check, color: const Color(0xFF1B1C1E), size: 16)
                              : null,
                          )
                        : Image.asset(
                            ThemeManager.getImagePath('btn_select'),
                            width: 40,
                            height: 40,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建文件夹视图
  Widget _buildFolderView(Product product, int index) {
    double w = (_width - 8) / 2;
    double h = 220;
    double imageWidth = 160;
    double imageHeight = 220;  
    
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: _selectedIndices.contains(index) ? 0.5 : 1.0,
      child: GestureDetector(
        onTap: () {
          if (_isSelectMode) {
            // 选择模式下点击切换选中状态
            setState(() {
              if (_selectedIndices.contains(index)) {
                _selectedIndices.remove(index);
              } else {
                _selectedIndices.add(index);
              }
              _isAllSelected = _selectedIndices.length == _products.length;
            });
          } else {
            // 非选择模式下打开文件夹
            try {
              Util.navigatorPush(FolderViewer(
                product: product,
                currentIndex: 0,  
              ));
            } catch (e) {
              ToastWidget.show("Failed to open folder: $e");
            }
          }
        },
        child: Container(
          width: w,
          height: h,
          // margin: const EdgeInsets.only(left: 6, right: 6, bottom: 6),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            boxShadow: ThemeManager.isDarkMode ? null : [
              BoxShadow(
                color: ThemeManager.currentTheme.itemBorderColor,
                blurRadius: 4,
                offset: const Offset(0, 4),
              ),
            ]
          ),
          alignment: Alignment.topCenter,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: w,
                padding: const EdgeInsets.only(left: 10, right: 10),
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  color: ThemeManager.currentTheme.mineButtonBackgroundColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  children: [
                    _buildFolderDetail(product, w, h/2),
                    Expanded(
                      child: Container(
                        alignment: Alignment.center,
                        width: w,
                        height: imageHeight,
                        child: Container(
                          width: imageWidth,
                          height: imageHeight,
                          clipBehavior: Clip.hardEdge,
                          alignment: Alignment.center,
                          margin: const EdgeInsets.only(bottom: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: CachedNetworkImage(
                            fadeInDuration: Duration.zero,
                            imageUrl: product.coverUrl ?? product.images[0].url ?? "",
                            width: imageWidth,
                            height: imageHeight,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => ImageWidget.placeholder(w, imageHeight),
                            errorWidget: (context, url, obj) => ImageWidget.placeholder(w, imageHeight),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 选择模式下显示选择指示器
              if (_isSelectMode)
                Positioned(
                  top: 10,
                  right: 10,
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: const Color(0xFF1B1C1E),
                        width: 1,
                      ),
                      color: _selectedIndices.contains(index) ? ThemeManager.currentTheme.selectedItemColor : Colors.white,
                    ),
                    child: _selectedIndices.contains(index)
                      ? const Icon(Icons.check, color: const Color(0xFF1B1C1E), size: 12)
                      : null,
                  ),
                ),
            ],
          )
        ),
      ),
    );
  }

  // 构建图片视图
  Widget _buildImageView(Product product, int index) {
    final imageInfo = _controller.getImageInfo(product);
    double w = (_width) / 2;
    double containerHeight = imageInfo['height'].toDouble();

    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: _selectedIndices.contains(index) ? 0.5 : 1.0,
      child: GestureDetector(
        onTap: () {
          if (_isSelectMode) {
            // 选择模式下点击切换选中状态
            setState(() {
              if (_selectedIndices.contains(index)) {
                _selectedIndices.remove(index);
              } else {
                _selectedIndices.add(index);
              }
              _isAllSelected = _selectedIndices.length == _products.length;
            });
          } else {
            // 非选择模式下查看图片
            Util.navigatorPush(ImageViewer(
              currentIndex: index,
              productController: _controller,
            ));
          }
        },
        child: Container(
          width: w,
          height: containerHeight,
          // margin: const EdgeInsets.only(left: 0, right: 0, bottom: 6),
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            boxShadow: ThemeManager.isDarkMode ? null : [
              BoxShadow(
                color:ThemeManager.currentTheme.itemBorderColor,
                blurRadius: 4,
                offset: const Offset(0, 4),
              ),
            ]
          ),
          alignment: Alignment.topCenter,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: w,
                height: containerHeight,
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  color: ThemeManager.currentTheme.mineButtonBackgroundColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: CachedNetworkImage(
                  fadeInDuration: Duration.zero,
                  imageUrl: imageInfo['imageUrl'],
                  width: w,
                  height: containerHeight,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => ImageWidget.placeholder(w, containerHeight),
                  errorWidget: (context, url, obj) => ImageWidget.placeholder(w, containerHeight),
                ),
              ),
              Positioned(
                top: 0,
                child: _buildImageDetail(product, w, containerHeight)
              ),
              
              // 选择模式下显示选择指示器
              if (_isSelectMode)
                Positioned(
                  top: 10,
                  right: 10,
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: const Color(0xFF1B1C1E),
                        width: 1,
                      ),
                      color: _selectedIndices.contains(index) ? ThemeManager.currentTheme.selectedItemColor : Colors.white,
                    ),
                    child: _selectedIndices.contains(index)
                      ? const Icon(Icons.check, color: const Color(0xFF1B1C1E), size: 12)
                      : null,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建图片详情
  Widget _buildImageDetail(Product product, double w, double h) {
    return Container(
      constraints: BoxConstraints(minHeight: h),
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16))
      ),
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: w,
          height: h,
          alignment: Alignment.topLeft,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16)
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 6),
              // 移除未读指示器
              const SizedBox(height: 0),
            ],
          ),
        ),
      ),
    );
  }

  // 构建文件夹详情
  Widget _buildFolderDetail(Product product, double w, double h) {
    return Container(
      width: w,
      padding: const EdgeInsets.only(top: 8, left: 6, right: 6, bottom: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16)
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              return Text.rich(
                TextSpan(
                  children: [
                    BreakAllTextSpan(
                      product.name ?? "",
                      TextStyle(
                        fontSize: 14,
                        color: ThemeManager.currentTheme.textColor,
                        height: 1.2,
                      )
                    ),
                  ]
                ),
                softWrap: true,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              );
            }
          ),
          Text(
            product.images.length > 1 
              ? AppLocalizations.of(context)!.multipleImages(product.images.length)
              : AppLocalizations.of(context)!.singleImage,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  // 构建底部操作栏
  Widget _buildBottomActionBar() {
    // 判断是否只选中了一个文件夹
    bool isSingleFolderSelected = _selectedIndices.length == 1 && 
        _products[_selectedIndices.first].images.length > 1;
    
    // 是否有选中项
    bool hasSelection = _selectedIndices.isNotEmpty;
    
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: ThemeManager.currentTheme.bottomBarBackgroundColor,
        // boxShadow: [
        //   BoxShadow(
        //     color: ThemeManager.currentTheme.bottomBarBackgroundColor,
        //     // color: ThemeManager.currentTheme.bottomBarShadowBackgroundColor,
        //     blurRadius: 4,
        //     offset: const Offset(0, -1),
        //   ),
        // ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 重命名按钮 - 只有选中单个文件夹时可用
          _buildActionButton(
            icon: ThemeManager.getImagePath('icon_edit'),
            label: AppLocalizations.of(context)!.rename,
            onTap: isSingleFolderSelected ? () => _handleRename() : null,
            isEnabled: isSingleFolderSelected,
          ),
          // 下载按钮
          _buildActionButton(
            icon: ThemeManager.getImagePath('icon_download'),
            label: AppLocalizations.of(context)!.download,
            onTap: hasSelection ? () => _handleDownload() : null,
            isEnabled: hasSelection,
          ),
          // 删除按钮
          _buildActionButton(
            icon: ThemeManager.getImagePath('icon_delete'),
            label: AppLocalizations.of(context)!.delete,
            onTap: hasSelection ? () => _handleDelete() : null,
            isEnabled: hasSelection,
          ),
        ],
      ),
    );
  }
  
  // 构建操作按钮
  Widget _buildActionButton({required String icon, required String label, required VoidCallback? onTap, required bool isEnabled}) {
    final color = isEnabled ? ThemeManager.currentTheme.textColor : Colors.grey.shade400;
    
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            icon,
            width: 26,  
            height: 26,
            color: color,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
  
  // 处理重命名操作
  void _handleRename() async {
    if (_selectedIndices.length != 1) return;
    
    final index = _selectedIndices.first;
    final product = _products[index];
    
    // await Util.showDialog(
    DialogManager.instance.showInputDialog(
      title: AppLocalizations.of(context)!.rename,
      message: "",
      onConfirm: (String text) async {
        if (text.isNotEmpty && text != product.name) {
          final success = await _controller.rename(product.productId!, text);
          if (success) {
            // 只更新选中文件夹的名称
            setState(() {
              _products[index].setName(text);
            });
          } else {
            ToastWidget.showError(AppLocalizations.of(context)!.failed);
          }
        }
      },
      confirmText: AppLocalizations.of(context)!.rename,
      initialInputText: product.name,
    );
  }
  
  // 处理下载操作
  void _handleDownload() async {
    if (_selectedIndices.isEmpty) return;
    
    // 获取选中的产品
    final selectedProducts = _selectedIndices.map((i) => _products[i]).toList();
    
    // 使用新的图片保存服务，统一保存原图
    try {
      await ImageSaveService.instance.saveMultipleProductImages(
        selectedProducts,
        saveType: ImageSaveType.original,
        context: context,
      );
      
      // 保存完成后退出选择模式
      setState(() {
        _isSelectMode = false;
        _selectedIndices = {};
        _isAllSelected = false;
      });
    } catch (e) {
      debugPrint('批量下载失败: $e');
      ToastWidget.showError(AppLocalizations.of(context)!.errorDownloading(e.toString()));
    }
  }
  
  // 处理删除操作
  void _handleDelete() async {
    if (_selectedIndices.isEmpty) return;
    
    DialogManager.instance.showDeleteDialog(
      message: AppLocalizations.of(context)!.deleteConfirmation(
        _selectedIndices.length,
        _selectedIndices.length > 1 ? 's' : '',
      ),
      onConfirm: () async {
        // 获取选中的产品ID和索引
        final selectedProducts = _selectedIndices
            .map((i) => _products[i])
            .where((product) => product.productId != null)
            .toList();
        
        final productIds = selectedProducts
            .map((product) => product.productId!)
            .toList();

        if (productIds.isEmpty) return;

        // 创建一个Map来存储要删除项目的透明度
        final Map<String, double> itemOpacities = {};
        for (var product in selectedProducts) {
          if (product.productId != null) {
            itemOpacities[product.productId!] = 0.5;
          }
        }

        // 更新状态以显示半透明效果
        setState(() {
          _products = _products.map((product) {
            if (product.productId != null && itemOpacities.containsKey(product.productId)) {
              return product;
            }
            return product;
          }).toList();
        });

        // 延迟一小段时间后完全移除
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            setState(() {
              _products.removeWhere((product) => 
                product.productId != null && 
                productIds.contains(product.productId));
              _isSelectMode = false;
              _selectedIndices = {};
              _isAllSelected = false;
            });
          }
        });

        // 在后台执行实际的删除操作
        final successIds = await _controller.batchDelete(productIds);
        
        if (successIds.length != productIds.length) {
          // 如果有删除失败的项目，刷新列表
          _loadPage(1, forceRefresh: true);
          ToastWidget.showError('Delete failed, please try again later');
        }
      }
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    _width = size.width;

    return Scaffold(
      backgroundColor:ThemeManager.currentTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _products.isEmpty && !_isLoading
        ? Center(
            child: Text(
              AppLocalizations.of(context)!.noFilesFound,
              style: TextStyle(color: Colors.grey.shade600),
            ),
          )
        : Stack(
            children: [
              RefreshIndicator(
                onRefresh: () => _loadPage(1, forceRefresh: true),
                color: ThemeManager.currentTheme.textColor,
                backgroundColor: ThemeManager.currentTheme.backgroundColor,
                child: _buildContentByViewMode(),
              ),
              
              // 全局加载指示器
              if (_isLoading && _products.isEmpty)
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: ThemeManager.currentTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: .3),
                          // spreadRadius: 1,
                          blurRadius: 5,
                        ),
                      ],
                    ),
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      color: ThemeManager.currentTheme.logoColor,
                      backgroundColor: ThemeManager.currentTheme.backgroundColor,
                    ),
                  ),
                ),
            ],
          ),
      
      // 选择模式下显示底部操作栏
      bottomNavigationBar: _isSelectMode ? _buildBottomActionBar() : null,
    );
  }

  Widget _buildContentByViewMode() {
    switch (_currentViewMode) {
      case ViewMode.list:
        return ListView.builder(
          controller: _scrollController,
          padding: EdgeInsets.only(
            top: 0, 
            left: 20, 
            right: 20, 
            bottom: _isSelectMode ? 80 : 20
          ),
          itemCount: _products.length + (_hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index < _products.length) {
              return _buildListItem(_products[index], index);
            } else if (_hasMore) {
              if (!_isLoading) {
                Future.microtask(() => _loadNextPage());
              }
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                alignment: Alignment.center,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: ThemeManager.currentTheme.logoColor,
                ),
              );
            } else {
              return Container();
            }
          },
        );
      case ViewMode.smallGrid:
        return GridView.builder(
          controller: _scrollController,
          padding: EdgeInsets.only(
            top: 0, 
            left: 20, 
            right: 20, 
            bottom: _isSelectMode ? 80 : 20
          ),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 0.8,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _products.length + (_hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index < _products.length) {
              return _buildSmallGridItem(_products[index], index);
            } else if (_hasMore) {
              if (!_isLoading) {
                Future.microtask(() => _loadNextPage());
              }
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                alignment: Alignment.center,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: ThemeManager.currentTheme.logoColor,
                ),
              );
            } else {
              return Container();
            }
          },
        );
      case ViewMode.largeGrid:
        return MasonryGridView.count(
          controller: _scrollController,
          padding: EdgeInsets.only(
            top: 0, 
            left: 20, 
            right: 20, 
            bottom: _isSelectMode ? 80 : 20
          ), // 选择模式下增加底部间距
          crossAxisCount: 2,
          mainAxisSpacing: 10,
          crossAxisSpacing: 10,
          itemCount: _products.length + (_hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index < _products.length) {
              return AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: 1.0,
                child: _products[index].images.length > 1 
                  ? _buildFolderView(_products[index], index)
                  : _buildImageView(_products[index], index),
              );
            } else if (_hasMore) {
              // 触发加载下一页，使用microtask确保在构建完成后执行
              if (!_isLoading) {
                Future.microtask(() => _loadNextPage());
              }
              // 显示底部加载指示器
              return Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                alignment: Alignment.center,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: ThemeManager.currentTheme.logoColor,
                ),
              );
            } else {
              return Container();
            }
          },
        );
      }
  }

  // 构建列表项（列表模式）
  Widget _buildListItem(Product product, int index) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: _selectedIndices.contains(index) ? 0.5 : 1.0,
      child: GestureDetector(
        onTap: () {
          if (_isSelectMode) {
            setState(() {
              if (_selectedIndices.contains(index)) {
                _selectedIndices.remove(index);
              } else {
                _selectedIndices.add(index);
              }
              _isAllSelected = _selectedIndices.length == _products.length;
            });
          } else {
            if (product.images.length > 1) {
              Util.navigatorPush(FolderViewer(
                product: product,
                currentIndex: 0,
              ));
            } else {
              Util.navigatorPush(ImageViewer(
                currentIndex: index,
                productController: _controller,
              ));
            }
          }
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ThemeManager.currentTheme.mineButtonBackgroundColor,
            borderRadius: BorderRadius.circular(10),
            boxShadow: ThemeManager.isDarkMode ? null : [
              BoxShadow(
                color: ThemeManager.currentTheme.itemBorderColor,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 图片预览
              Container(
                width: 60,
                height: 60,
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CachedNetworkImage(
                  fadeInDuration: Duration.zero,
                  imageUrl: product.coverUrl ?? product.images[0].url ?? "",
                  fit: BoxFit.cover,
                  placeholder: (context, url) => ImageWidget.placeholder(60, 60),
                  errorWidget: (context, url, obj) => ImageWidget.placeholder(60, 60),
                ),
              ),
              const SizedBox(width: 12),
              // 文件信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    BreakAllText(
                      product.createdAt?.toString() ?? "",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: ThemeManager.currentTheme.textColor,
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      product.images.length > 1 
                        ? AppLocalizations.of(context)!.multipleImages(product.images.length)
                        : AppLocalizations.of(context)!.singleImage,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              // 选择指示器
              if (_isSelectMode)
                Container(
                  width: 18,
                  height: 18,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFF1B1C1E),
                      width: 1,
                    ),
                    color: _selectedIndices.contains(index) 
                      ? ThemeManager.currentTheme.selectedItemColor 
                      : Colors.white,
                  ),
                  child: _selectedIndices.contains(index)
                    ? const Icon(Icons.check, color: const Color(0xFF1B1C1E), size: 12)
                    : null,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建小图网格项（小图模式）
  Widget _buildSmallGridItem(Product product, int index) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: _selectedIndices.contains(index) ? 0.5 : 1.0,
      child: GestureDetector(
        onTap: () {
          if (_isSelectMode) {
            setState(() {
              if (_selectedIndices.contains(index)) {
                _selectedIndices.remove(index);
              } else {
                _selectedIndices.add(index);
              }
              _isAllSelected = _selectedIndices.length == _products.length;
            });
          } else {
            if (product.images.length > 1) {
              Util.navigatorPush(FolderViewer(
                product: product,
                currentIndex: 0,
              ));
            } else {
              Util.navigatorPush(ImageViewer(
                currentIndex: index,
                productController: _controller,
              ));
            }
          }
        },
        child: Container(
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: ThemeManager.currentTheme.mineButtonBackgroundColor,
            borderRadius: BorderRadius.circular(10),
            boxShadow: ThemeManager.isDarkMode ? null : [
              BoxShadow(
                color: ThemeManager.currentTheme.itemBorderColor,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              Column(
                children: [
                  // 图片部分
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      child: CachedNetworkImage(
                        fadeInDuration: Duration.zero,
                        imageUrl: product.coverUrl ?? product.images[0].url ?? "",
                        fit: BoxFit.cover,
                        placeholder: (context, url) => ImageWidget.placeholder(double.infinity, double.infinity),
                        errorWidget: (context, url, obj) => ImageWidget.placeholder(double.infinity, double.infinity),
                      ),
                    ),
                  ),
                  // 文字信息部分
                  // Expanded(
                  //   flex: 1,
                  //   child: Container(
                  //     width: double.infinity,
                  //     padding: const EdgeInsets.all(6),
                  //     child: Column(
                  //       crossAxisAlignment: CrossAxisAlignment.start,
                  //       mainAxisAlignment: MainAxisAlignment.center,
                  //       children: [
                          // BreakAllText(
                          //   product.name ?? "",
                          //   style: TextStyle(
                          //     fontSize: 11,
                          //     color: ThemeManager.currentTheme.textColor,
                          //   ),
                          //   maxLines: 1,
                          // ),
                          // if (product.images.length > 1)
                          //   Text(
                          //     '${product.images.length}张',
                          //     style: TextStyle(
                          //       fontSize: 9,
                          //       color: Colors.grey.shade600,
                          //     ),
                          //   ),
                        // ],
                      // ),
                    // ),
                  // ),
                ],
              ),
              // 选择指示器
              if (_isSelectMode)
                Positioned(
                  top: 6,
                  right: 6,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: const Color(0xFF1B1C1E),
                        width: 1,
                      ),
                      color: _selectedIndices.contains(index) 
                        ? ThemeManager.currentTheme.selectedItemColor 
                        : Colors.white,
                    ),
                    child: _selectedIndices.contains(index)
                      ? const Icon(Icons.check, color: const Color(0xFF1B1C1E), size: 10)
                      : null,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
