import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:imtrans/services/account.dart';
import 'package:imtrans/services/event_log.dart';
import 'package:imtrans/services/appsflyer_service.dart';
import 'package:imtrans/util/appsflyer_diagnostics.dart';
import 'package:provider/provider.dart';
import 'package:imtrans/pages/splash_screen.dart';
import 'package:imtrans/controllers/product_controller.dart';
import 'package:imtrans/util/util.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/language_manager.dart';
import 'package:imtrans/util/app_locale.dart';
import 'package:imtrans/pages/purchase/purchase_manager.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化 Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint("Firebase Initialized");
  } catch (e) {
    debugPrint("Error initializing Firebase: $e");
  }
  
  // 初始化事件记录服务（默认开启数据收集）
  await EventLogService.initialize();

  // 初始化 AppsFlyer 归因和分析服务
  await AppsFlyerService.initialize();

  // 运行 AppsFlyer 诊断 (仅在调试模式下)
  if (kDebugMode) {
    // 延迟一秒确保初始化完成
    Future.delayed(const Duration(seconds: 1), () {
      AppsFlyerDiagnostics.printDetailedReport();
    });
  }

  Util.initPath();

  // 初始化 Firebase Analytics
  // final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  // 初始化 Account
  final account = await Account.instance;
  // await analytics.setUserId(id: account.info['uid'].toString());
  // await analytics.setUserProperty(name: 'device_id', value: account.udid);
  
  // 初始化 ProductController
  await ProductController.instance.initialize();
  
  // 初始化主题管理器
  await ThemeManager.initialize();
  
  // 初始化语言管理器
  await LanguageManager.initialize();
  
  // 初始化应用界面语言
  await AppLocale.initialize();
  
  // 初始化AB测试标识
  await PurchaseManager.initPayFlag();
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: account),
        ChangeNotifierProvider.value(value: ThemeManager()),
        ChangeNotifierProvider.value(value: LanguageManager()),
        ChangeNotifierProvider.value(value: AppLocale()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AppLocale, ThemeManager>(
      builder: (context, appLocale, themeManager, child) {
        return MaterialApp(
          themeMode: ThemeManager.themeMode,
          theme: ThemeManager.lightTheme,
          darkTheme: ThemeManager.darkTheme,
          locale: AppLocale.currentLocale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
            ],
          supportedLocales: AppLocalizations.supportedLocales,
          home: const SplashScreen()
        );
      },
    );
  }
}