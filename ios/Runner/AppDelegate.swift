import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
    let launchViewController = LaunchViewController()
    let channel = FlutterMethodChannel(name: "ios_func", binaryMessenger: controller.binaryMessenger)
    channel.setMethodCallHandler { (call, result) in
      if ("getSystemThemeColor" == call.method) {
        let userInterfaceStyle = UITraitCollection.current.userInterfaceStyle
        if(userInterfaceStyle==UIUserInterfaceStyle.light){
            result(true)
        }else{
            result(false)
        }
      }
      else {
        result(FlutterMethodNotImplemented)
      }
    }

    GeneratedPluginRegistrant.register(with: self)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
