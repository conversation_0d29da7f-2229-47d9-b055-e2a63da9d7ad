import 'package:flutter/material.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';
import 'package:google_mlkit_language_id/google_mlkit_language_id.dart';
import 'package:imtrans/util/language_manager.dart';
import 'package:imtrans/services/local_ocr_service.dart';

/// Local translation service using google_mlkit_translation package
/// Provides offline text translation with multiple language pair support
class LocalTranslationService {
  static final LocalTranslationService _instance = LocalTranslationService._internal();
  factory LocalTranslationService() => _instance;
  LocalTranslationService._internal();

  final Map<String, OnDeviceTranslator> _translators = {};
  final Map<String, bool> _downloadedLanguages = {};
  bool _isInitialized = false;

  // Language identification
  late LanguageIdentifier _languageIdentifier;

  /// Initialize the translation service
  Future<void> initialize() async {
    try {
      // Initialize language identifier
      _languageIdentifier = LanguageIdentifier(confidenceThreshold: 0.5);

      _isInitialized = true;
      debugPrint('LocalTranslationService: Initialized successfully');
    } catch (e) {
      debugPrint('LocalTranslationService: Failed to initialize - $e');
      throw TranslationException('Failed to initialize translation service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Get or create a translator for the specified language pair
  Future<OnDeviceTranslator> _getTranslator(TranslateLanguage sourceLanguage, TranslateLanguage targetLanguage) async {
    final key = '${sourceLanguage.bcpCode}_${targetLanguage.bcpCode}';
    
    if (_translators.containsKey(key)) {
      return _translators[key]!;
    }

    try {
      final translator = OnDeviceTranslator(
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
      );
      
      _translators[key] = translator;
      debugPrint('LocalTranslationService: Created translator for $key');
      return translator;
    } catch (e) {
      debugPrint('LocalTranslationService: Failed to create translator for $key - $e');
      throw TranslationException('Failed to create translator: $e');
    }
  }

  /// Check if a language model is downloaded
  Future<bool> isLanguageDownloaded(TranslateLanguage language) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    try {
      final modelManager = OnDeviceTranslatorModelManager();
      final isDownloaded = await modelManager.isModelDownloaded(language.bcpCode);
      _downloadedLanguages[language.bcpCode] = isDownloaded;
      return isDownloaded;
    } catch (e) {
      debugPrint('LocalTranslationService: Failed to check if language is downloaded - $e');
      return false;
    }
  }

  /// Download a language model
  Future<bool> downloadLanguage(TranslateLanguage language) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalTranslationService: Downloading language model for ${language.bcpCode}');
      final modelManager = OnDeviceTranslatorModelManager();
      final success = await modelManager.downloadModel(language.bcpCode);
      
      if (success) {
        _downloadedLanguages[language.bcpCode] = true;
        debugPrint('LocalTranslationService: Successfully downloaded ${language.bcpCode}');
      } else {
        debugPrint('LocalTranslationService: Failed to download ${language.bcpCode}');
      }
      
      return success;
    } catch (e) {
      debugPrint('LocalTranslationService: Error downloading language model - $e');
      throw TranslationException('Failed to download language model: $e');
    }
  }

  /// Delete a language model
  Future<bool> deleteLanguage(TranslateLanguage language) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalTranslationService: Deleting language model for ${language.bcpCode}');
      final modelManager = OnDeviceTranslatorModelManager();
      final success = await modelManager.deleteModel(language.bcpCode);
      
      if (success) {
        _downloadedLanguages[language.bcpCode] = false;
        debugPrint('LocalTranslationService: Successfully deleted ${language.bcpCode}');
      } else {
        debugPrint('LocalTranslationService: Failed to delete ${language.bcpCode}');
      }
      
      return success;
    } catch (e) {
      debugPrint('LocalTranslationService: Error deleting language model - $e');
      throw TranslationException('Failed to delete language model: $e');
    }
  }

  /// Translate text from source language to target language
  Future<String> translateText(
    String text,
    TranslateLanguage sourceLanguage,
    TranslateLanguage targetLanguage,
  ) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    if (text.trim().isEmpty) {
      return text;
    }

    try {
      debugPrint('LocalTranslationService: Translating "$text" from ${sourceLanguage.bcpCode} to ${targetLanguage.bcpCode}');
      
      // Check if both languages are downloaded
      final sourceDownloaded = await isLanguageDownloaded(sourceLanguage);
      final targetDownloaded = await isLanguageDownloaded(targetLanguage);
      
      if (!sourceDownloaded) {
        throw TranslationException('Source language model not downloaded: ${sourceLanguage.bcpCode}');
      }
      
      if (!targetDownloaded) {
        throw TranslationException('Target language model not downloaded: ${targetLanguage.bcpCode}');
      }

      final translator = await _getTranslator(sourceLanguage, targetLanguage);
      final translatedText = await translator.translateText(text);
      
      debugPrint('LocalTranslationService: Translation result: "$translatedText"');
      return translatedText;
    } catch (e) {
      debugPrint('LocalTranslationService: Translation failed - $e');
      throw TranslationException('Failed to translate text: $e');
    }
  }

  /// Translate multiple texts in batch
  Future<List<String>> translateTexts(
    List<String> texts,
    TranslateLanguage sourceLanguage,
    TranslateLanguage targetLanguage,
  ) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalTranslationService: Batch translating ${texts.length} texts');
      
      final results = <String>[];
      for (final text in texts) {
        if (text.trim().isEmpty) {
          results.add(text);
        } else {
          final translated = await translateText(text, sourceLanguage, targetLanguage);
          results.add(translated);
        }
      }
      
      debugPrint('LocalTranslationService: Batch translation completed');
      return results;
    } catch (e) {
      debugPrint('LocalTranslationService: Batch translation failed - $e');
      throw TranslationException('Failed to translate texts: $e');
    }
  }

  /// Detect the language of the given text
  Future<TranslateLanguage> detectLanguage(String text) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    if (text.trim().isEmpty) {
      return TranslateLanguage.english; // Default fallback
    }

    try {
      debugPrint('LocalTranslationService: Detecting language for text: "$text"');

      final identifiedLanguages = await _languageIdentifier.identifyPossibleLanguages(text);

      if (identifiedLanguages.isNotEmpty) {
        final topLanguage = identifiedLanguages.first;
        debugPrint('LocalTranslationService: Detected language: ${topLanguage.languageTag} (confidence: ${topLanguage.confidence})');

        // Convert language tag to TranslateLanguage
        final translateLanguage = _languageTagToTranslateLanguage(topLanguage.languageTag);
        if (translateLanguage != null) {
          return translateLanguage;
        }
      }

      debugPrint('LocalTranslationService: Could not detect language, defaulting to English');
      return TranslateLanguage.english;
    } catch (e) {
      debugPrint('LocalTranslationService: Language detection failed - $e');
      return TranslateLanguage.english; // Fallback to English
    }
  }

  /// Convert language tag to TranslateLanguage enum
  TranslateLanguage? _languageTagToTranslateLanguage(String languageTag) {
    switch (languageTag.toLowerCase()) {
      case 'en':
        return TranslateLanguage.english;
      case 'ja':
        return TranslateLanguage.japanese;
      case 'ko':
        return TranslateLanguage.korean;
      case 'zh':
      case 'zh-cn':
      case 'zh-hans':
        return TranslateLanguage.chinese;
      case 'fr':
        return TranslateLanguage.french;
      case 'de':
        return TranslateLanguage.german;
      case 'es':
        return TranslateLanguage.spanish;
      case 'it':
        return TranslateLanguage.italian;
      case 'pt':
        return TranslateLanguage.portuguese;
      case 'th':
        return TranslateLanguage.thai;
      case 'vi':
        return TranslateLanguage.vietnamese;
      case 'id':
        return TranslateLanguage.indonesian;
      case 'ms':
        return TranslateLanguage.malay;
      case 'ar':
        return TranslateLanguage.arabic;
      case 'hi':
        return TranslateLanguage.hindi;
      case 'ru':
        return TranslateLanguage.russian;
      default:
        debugPrint('LocalTranslationService: Unsupported language tag: $languageTag');
        return null;
    }
  }

  /// Get user's configured target language
  TranslateLanguage getTargetLanguage() {
    final targetLanguageCode = LanguageManager.currentLanguageCode;
    return _languageTagToTranslateLanguage(targetLanguageCode) ?? TranslateLanguage.english;
  }

  /// Ensure language models are downloaded
  Future<void> ensureLanguageModelsDownloaded(TranslateLanguage sourceLanguage, TranslateLanguage targetLanguage) async {
    try {
      // Check source language model
      final sourceDownloaded = await isLanguageDownloaded(sourceLanguage);
      if (!sourceDownloaded) {
        debugPrint("Downloading source language model: ${sourceLanguage.bcpCode}");
        final success = await downloadLanguage(sourceLanguage);
        if (!success) {
          throw Exception("Failed to download source language model: ${sourceLanguage.bcpCode}");
        }
      }

      // Check target language model
      final targetDownloaded = await isLanguageDownloaded(targetLanguage);
      if (!targetDownloaded) {
        debugPrint("Downloading target language model: ${targetLanguage.bcpCode}");
        final success = await downloadLanguage(targetLanguage);
        if (!success) {
          throw Exception("Failed to download target language model: ${targetLanguage.bcpCode}");
        }
      }

      debugPrint("Language models ready: ${sourceLanguage.bcpCode} -> ${targetLanguage.bcpCode}");
    } catch (e) {
      debugPrint("Error ensuring language models: $e");
      throw Exception("Failed to prepare language models: $e");
    }
  }

  /// Translate OCR text elements with automatic language detection
  Future<List<OcrTextElement>> translateOcrElements(List<OcrTextElement> textElements) async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    final translatedElements = <OcrTextElement>[];
    final targetLanguage = getTargetLanguage();
    debugPrint("Using target language: ${targetLanguage.bcpCode}");

    for (final element in textElements) {
      try {
        // Detect source language
        final sourceLanguage = await detectLanguage(element.text);
        debugPrint("Detected source language for '${element.text}': ${sourceLanguage.bcpCode}");

        // Skip translation if source and target languages are the same
        if (sourceLanguage == targetLanguage) {
          debugPrint("Source and target languages are the same, skipping translation");
          translatedElements.add(element);
          continue;
        }

        // Ensure language models are downloaded
        await ensureLanguageModelsDownloaded(sourceLanguage, targetLanguage);

        // Translate text
        final translatedText = await translateText(
          element.text,
          sourceLanguage,
          targetLanguage,
        );

        translatedElements.add(OcrTextElement(
          text: translatedText,
          boundingBox: element.boundingBox,
          confidence: element.confidence,
        ));
      } catch (e) {
        debugPrint("Translation failed for text '${element.text}': $e");
        // Keep original text if translation fails
        translatedElements.add(element);
      }
    }

    return translatedElements;
  }

  /// Get list of available languages
  List<TranslateLanguage> getAvailableLanguages() {
    return TranslateLanguage.values;
  }

  /// Get downloaded languages
  Future<List<TranslateLanguage>> getDownloadedLanguages() async {
    if (!_isInitialized) {
      throw TranslationException('Translation service not initialized. Call initialize() first.');
    }

    try {
      final modelManager = OnDeviceTranslatorModelManager();
      final downloadedLanguages = <TranslateLanguage>[];

      // Check each language individually since there's no getAvailableModels method
      for (final language in TranslateLanguage.values) {
        try {
          final isDownloaded = await modelManager.isModelDownloaded(language.bcpCode);
          if (isDownloaded) {
            downloadedLanguages.add(language);
            _downloadedLanguages[language.bcpCode] = true;
          } else {
            _downloadedLanguages[language.bcpCode] = false;
          }
        } catch (e) {
          debugPrint('LocalTranslationService: Error checking ${language.bcpCode}: $e');
          _downloadedLanguages[language.bcpCode] = false;
        }
      }

      debugPrint('LocalTranslationService: Found ${downloadedLanguages.length} downloaded languages');
      return downloadedLanguages;
    } catch (e) {
      debugPrint('LocalTranslationService: Failed to get downloaded languages - $e');
      return [];
    }
  }

  /// Dispose all translators and clean up resources
  Future<void> dispose() async {
    try {
      for (final translator in _translators.values) {
        await translator.close();
      }
      _translators.clear();
      _downloadedLanguages.clear();

      // Close language identifier
      if (_isInitialized) {
        _languageIdentifier.close();
      }

      _isInitialized = false;
      debugPrint('LocalTranslationService: Disposed all resources');
    } catch (e) {
      debugPrint('LocalTranslationService: Error during disposal - $e');
    }
  }
}

/// Custom exception for translation operations
class TranslationException implements Exception {
  final String message;
  final dynamic originalError;

  const TranslationException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'TranslationException: $message (Original error: $originalError)';
    }
    return 'TranslationException: $message';
  }
}

/// Helper class for common language pairs
class LanguagePairs {
  static const englishToJapanese = LanguagePair(TranslateLanguage.english, TranslateLanguage.japanese);
  static const japaneseToEnglish = LanguagePair(TranslateLanguage.japanese, TranslateLanguage.english);
  static const englishToChinese = LanguagePair(TranslateLanguage.english, TranslateLanguage.chinese);
  static const chineseToEnglish = LanguagePair(TranslateLanguage.chinese, TranslateLanguage.english);
  static const englishToKorean = LanguagePair(TranslateLanguage.english, TranslateLanguage.korean);
  static const koreanToEnglish = LanguagePair(TranslateLanguage.korean, TranslateLanguage.english);
}

/// Represents a language pair for translation
class LanguagePair {
  final TranslateLanguage source;
  final TranslateLanguage target;

  const LanguagePair(this.source, this.target);

  @override
  String toString() => '${source.bcpCode} -> ${target.bcpCode}';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguagePair && other.source == source && other.target == target;
  }

  @override
  int get hashCode => source.hashCode ^ target.hashCode;
}
