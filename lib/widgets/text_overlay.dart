import 'dart:async';
import 'dart:math';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:imtrans/models/product_model.dart';
import 'package:imtrans/util/text_overlay_settings.dart';
import 'package:imtrans/widgets/stroke_text.dart';
import 'package:imtrans/widgets/text_area_clipper.dart';
import 'package:google_fonts/google_fonts.dart';
// import '../util/util.dart';

/// 生成翻译叠加图片的组件
/// 传入参数为：
/// "imageUrl": 原始图片的url
/// "texts": 文本数据 格式与ProductItem中的translate一致{bound: [...], translated_text: "..."}
/// "caches": 缓存数据可根据现有情况选择传入，如果传入任何一个则优先使用 格式为Map<String, dynamic> {
///   "imageData": 图片数据
///   "imagePath": 图片路径
/// }
/// "visible": 是否显示text, 默认显示, 如果为false, 则只显示图片
///
/// 返回一个SizedBox，里面包含原始图片和翻译文本
class TextOverlay extends StatefulWidget {
  final String _imageUrl;
  final List<dynamic> _texts;
  final Map<String, dynamic> _caches;
  final bool visible;  

  const TextOverlay(this._imageUrl, this._texts, {Map<String, dynamic>? caches, this.visible = true}) 
    : _caches = caches ?? const {
      'imageData': <int>[],
      'imagePath': '',
    };

  static double calculateDistance(Offset point1, Offset point2) {
    var dx = point1.dx - point2.dx;
    var dy = point1.dy - point2.dy;
    return sqrt(dx * dx + dy * dy);
  }

  static double calculateAngle(Offset p1,Offset p2){
    final x1 = p1.dx;
    final y1 = p1.dy;
    final x2 = p2.dx;
    final y2 = p2.dy;
    // 计算两点之间的倾斜角度
    var angleRadians = atan2(y2 - y1, x2 - x1);
    // 将弧度转换为角度
    //var angleDegrees = angleRadians * (180 / pi);
    return angleRadians;
  }
  
  @override
  State<StatefulWidget> createState() => _TextOverlayState();
}

class _TextOverlayState extends State<TextOverlay> {
  double _winWidth = 0;
  double _scale = 1.0;
  double? _imgHeight;
  Widget? _baseImage;  // Changed from Image? to Widget?
  String? _currentImageUrl;  // 添加标记当前图片URL
  final TextOverlaySettings _settings = TextOverlaySettings();

  @override
  void initState() {
    super.initState();
    _settings.addListener(_onSettingsChanged);
  }

  @override
  void dispose() {
    _settings.removeListener(_onSettingsChanged);
    super.dispose();
  }

  void _onSettingsChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void didUpdateWidget(TextOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget._imageUrl != _currentImageUrl) {
      _currentImageUrl = widget._imageUrl;
      _initializeImage();
    }
  }

  void _initializeImage() {
    debugPrint(" ----- Making text overlay ----- ");
    try {
      ImageProvider provider;
      if (widget._caches["imageData"] != null && widget._caches["imageData"].isNotEmpty) {
        debugPrint("Using preloaded image data");
        provider = Image.memory(widget._caches["imageData"]).image;
      } 
      else if (widget._caches["imagePath"] != null && widget._caches["imagePath"].isNotEmpty) {
        final imagePath = ProductImage.localCacheDir.path + '/' + widget._caches["imagePath"];
        debugPrint("Using local file: $imagePath");
        provider = Image.file(File(imagePath)).image;
      } 
      else {
        debugPrint("Using network image: ${widget._imageUrl}");
        provider = CachedNetworkImageProvider(
          widget._imageUrl,
          // 添加缓存优化策略
          maxWidth: (MediaQuery.of(context).size.width * MediaQuery.of(context).devicePixelRatio).round(),
          maxHeight: (MediaQuery.of(context).size.height * MediaQuery.of(context).devicePixelRatio).round(),
        );
      }

      provider
          .resolve(const ImageConfiguration())
          .addListener(ImageStreamListener((ImageInfo info, bool _) {
            if (!mounted) return;
            // 确保图片尺寸有效
            final imageWidth = info.image.width > 0 ? info.image.width : 1;
            final imageHeight = info.image.height > 0 ? info.image.height : 1;
            
            setState(() {
              _scale = _winWidth / imageWidth;
              _imgHeight = imageHeight * _scale;
            });
          },
          onError: (exception, stackTrace) {
            // 捕获异步加载过程中的错误
            debugPrint("TextOverlay - Error loading image: $exception");
            if (mounted) {
              setState(() {
                _baseImage = _buildErrorPlaceholder();
                _scale = 1.0; // 设置默认缩放比例
                _imgHeight = _winWidth; // 设置默认高度
              });
            }
          },
      ));

      _baseImage = Image(
        image: provider,
          width: _winWidth,
          fit: BoxFit.fitWidth,
          alignment: Alignment.topCenter, // 添加顶部对齐，与原图保持一致
          filterQuality: FilterQuality.high,
        );
    } catch (e) {
      debugPrint("TextOverlay - Error loading image: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    _winWidth = MediaQuery.of(context).size.width;
    
    if (_currentImageUrl == null) {
      _currentImageUrl = widget._imageUrl;
      _initializeImage();
    }

    return _buildImageAndTexts();
  }

  Widget _buildImageAndTexts() {
    // 确保尺寸有效
    final validWidth = _winWidth > 0 ? _winWidth : 100.0;
    final validHeight = (_imgHeight ?? _winWidth) > 0 ? (_imgHeight ?? _winWidth) : 100.0;
    
    return SizedBox(
      width: validWidth,
      height: validHeight,
      child: Stack(
        fit: StackFit.expand, // 改为expand以确保子组件有明确的尺寸约束
        children: [
          _baseImage ?? _buildErrorPlaceholder(),
          // 调试用边框
          // ...widget._item["translate"].map((item) {
          //   final points = item["bound"];
          //   return CustomPaint(
          //     painter: DebugBorderPainter(points, _scale),
          //   );
          // }).toList(),
          // 添加模糊层
          if (widget.visible && _scale > 0) ...[
            ...widget._texts.map((item) {
            final points = item["bound"];
            if (points == null || points.length < 4) {
              return Container();
            }
            return ClipPath(
              clipper: TextAreaClipper(points, _scale),
              child: BackdropFilter(
                filter: _settings.backgroundFilter,
                child: Container(
                  color: Colors.transparent,
                ),
              ),
            );
          }).toList(),
          if (_scale > 0) // 只有在缩放比例有效时才显示文本覆盖
            FutureBuilder(
              future: _buildTextOverlays(),
              builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Container();
                } else if (snapshot.connectionState == ConnectionState.done) {
                  if (snapshot.data != null) {
                    return Stack(
                      children: snapshot.data,
                    );
                  }
                  return Container();
                } else {
                  return Container();
                }
                },
              )
          ]
        ],
      ),
    );
  }

  // 创建错误占位图
  Widget _buildErrorPlaceholder() {
    return Container(
      color: Colors.grey[300],
      width: _winWidth,
      height: _winWidth,
      child: const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error, color: Colors.red, size: 40),
            SizedBox(height: 8),
            Text("Error Loading Image", style: TextStyle(color: Colors.red)),
          ],
        ),
      ),
    );
  }

  Future<List<Widget>> _buildTextOverlays() async {
    const double offsetX = 10;
    if (_scale <= 0) return [];

    final List<dynamic>? items = widget._texts;
    if (items == null) return [];

    return items.map((item) {
      final points = item["bound"];
      if (points == null || points.length < 4) {
        return Container(); // 返回空容器如果点数据无效
      }
      
      final sx = 1.0;

      final off0 =
          Offset(points[0]["x"] * sx - offsetX, points[0]["y"] * sx - 1);
      final off1 =
          Offset(points[1]["x"] * sx + offsetX, points[1]["y"] * sx - 1);
      final off3 =
          Offset(points[3]["x"] * sx - offsetX, points[3]["y"] * sx + 10);

      final width = TextOverlay.calculateDistance(off0 * _scale, off1 * _scale);
      final height = TextOverlay.calculateDistance(off0 * _scale, off3 * _scale);
      
      // 确保尺寸有效
      final size = Size(
        width > 0 ? width : 100,
        height > 0 ? height : 20,
      );

      String text = item["translated_text"] ?? "";
      if (text.isEmpty) {
        return Container(); // 返回空容器如果文本为空
      }

      return _buildOverlay(off0, size, text);
    }).toList();
  }

  Widget _buildOverlay(Offset position, Size size, String text) {
    // 确保尺寸有效
    final validSize = Size(
      size.width > 0 ? size.width : 100,
      size.height > 0 ? size.height : 20,
    );
    
    return Positioned(
      top: position.dy * _scale,
      left: position.dx * _scale,
      width: validSize.width, // 明确指定宽度
      height: validSize.height, // 明确指定高度
      child: Material(
        color: Colors.transparent,
        child: Transform.rotate(
          angle: TextOverlay.calculateAngle(position, position + Offset(validSize.width, 0)),
          alignment: Alignment.center, // 添加明确的对齐方式
          child: Container(
            width: validSize.width,
            height: validSize.height,
            alignment: Alignment.center,
            child: StrokeText(
              text,
              size: Size(validSize.width * _settings.fontSize, validSize.height * _settings.fontSize),
              strokeWidth: _settings.strokeWidth,
              strokeColor: _settings.strokeColor,
              textColor: _settings.textColor,
              style: _getTextStyleWithFont(_settings.fontFamily).copyWith(
                fontWeight: _settings.fontWeight,
                shadows: [
                  Shadow(
                    offset: _settings.shadowOffset,
                    blurRadius: _settings.shadowBlurRadius,
                    color: _settings.shadowColor,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  TextStyle _getTextStyleWithFont(String fontFamily) {
    switch (fontFamily) {
      case 'Comic Neue':
        return GoogleFonts.comicNeue();
      case 'Bangers':
        return GoogleFonts.bangers();
      case 'Luckiest Guy':
        return GoogleFonts.luckiestGuy();
      case 'Creepster':
        return GoogleFonts.creepster();
      case 'Fredoka One':
        return GoogleFonts.fredoka();
      case 'Bungee':
        return GoogleFonts.bungee();
      case 'Righteous':
        return GoogleFonts.righteous();
      case 'Kalam':
        return GoogleFonts.kalam();
      case 'Architects Daughter':
        return GoogleFonts.architectsDaughter();
      case 'Caveat':
        return GoogleFonts.caveat();
      case 'Inter':
        return GoogleFonts.inter();
      default:
        return TextStyle(fontFamily: fontFamily);
    }
  }
}


class DebugBorderPainter extends CustomPainter {
  final List<dynamic> points;
  final double scale;

  DebugBorderPainter(this.points, this.scale);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final path = Path();
    path.moveTo(points[0]["x"] * scale, points[0]["y"] * scale);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i]["x"] * scale, points[i]["y"] * scale);
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
