import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:http/http.dart' as http;
import 'package:imtrans/util/util.dart';

/// 图片提取器接口
/// 定义所有图片提取器必须实现的方法
abstract class IImageExtractor {
  /// 提取图片URL列表
  Future<List<String>> extractImageUrls();
  
  /// 下载图片
  /// 子类可以选择是否重载此方法
  Future<List<Uint8List>> downloadImages(List<String> urls);
  
  /// 检查是否需要使用WebView获取图片
  bool needsWebViewForImages();
  
  /// 获取单张图片数据
  Future<Uint8List?> getImageData(String imageUrl, {required bool useWebView});

  /// 获取当前页面的Cookie
  Future<String> getCookies();
}

/// 图片下载策略接口
/// 定义不同的图片下载策略
abstract class IImageDownloadStrategy {
  /// 下载图片
  Future<Uint8List?> downloadImage(String url, {Map<String, String>? headers});
}

/// 图片提取进度监听器
typedef ImageExtractionProgressCallback = void Function(int current, int total, String url);

/// 基础图片提取器
/// 提供通用功能实现，特定网站提取器可继承此类并重写特定方法
abstract class BaseImageExtractor implements IImageExtractor {
  final InAppWebViewController controller;
  final String currentUrl;
  final List<ImageExtractionProgressCallback> _progressListeners = [];
  
  BaseImageExtractor({
    required this.controller,
    required this.currentUrl,
  });
  
  /// 添加进度监听器
  void addProgressListener(ImageExtractionProgressCallback listener) {
    _progressListeners.add(listener);
  }
  
  @override
  bool needsWebViewForImages() {
    return false;
  }

  /// 下载图片的默认实现
  /// 子类可以选择重载此方法或使用此默认实现
  @override
  Future<List<Uint8List>> downloadImages(List<String> urls) async {
    return await getImages(urls);
  }
  
  /// 移除进度监听器
  void removeProgressListener(ImageExtractionProgressCallback listener) {
    _progressListeners.remove(listener);
  }
  
  /// 通知进度更新
  void notifyProgress(int current, int total, String url) {
    for (final listener in _progressListeners) {
      listener(current, total, url);
    }
  }
  
    /// 统一的图片获取方法，根据提取器配置决定使用WebView还是HTTP直接获取
  Future<List<Uint8List>> getImages(List<String> imageUrls) async {
    debugPrint('开始批量获取图片，共 ${imageUrls.length} 张');
    List<Uint8List> results = [];
    int successCount = 0;
    int failCount = 0;
    
    // 根据提取器配置决定使用哪种方式获取图片
    final useWebView = needsWebViewForImages();
    debugPrint('图片获取方式: ${useWebView ? "WebView" : "HTTP直接请求"}');
    
    for (int i = 0; i < imageUrls.length; i++) {
      final url = imageUrls[i];
      try {
        debugPrint('处理第 ${i+1}/${imageUrls.length} 张图片: $url');
        notifyProgress(i, imageUrls.length, url);
        
        // 根据配置选择获取方式
        final Uint8List? imageData = await getImageData(url, useWebView: useWebView);
        
        if (imageData != null) {
          // 验证图像格式
          if (Util.isValidImageFormat(imageData)) {
            results.add(imageData);
            successCount++;
            debugPrint("✅ 成功获取图片 #${i+1}: $url (${imageData.length} 字节)");
          } else {
            failCount++;
            debugPrint("❌ 图片格式无效 #${i+1}: $url (${imageData.length} 字节)");
          }
        } else {
          failCount++;
          debugPrint("❌ 获取图片返回null #${i+1}: $url");
        }
      } catch (e) {
        failCount++;
        debugPrint('❌ 获取图片异常 #${i+1} $url: $e');
      }
      
      // 添加短暂延迟，避免过快请求导致的问题
      await Future.delayed(const Duration(milliseconds: 300));
    }
    
    debugPrint("图片获取统计: 成功 $successCount 张, 失败 $failCount 张, 总计 ${imageUrls.length} 张");
    return results;
  }
  
  /// 获取单张图片数据
  /// [useWebView] 是否使用WebView获取图片
  /// [customHeaders] 自定义HTTP请求头
  Future<Uint8List?> getImageData(String imageUrl, {required bool useWebView, Map<String, String>? customHeaders}) async {
    if (useWebView) {
      debugPrint('开始从webview获取图片: $imageUrl');
      return await _getImageFromWebView(imageUrl);
    } else {
      debugPrint('开始从HTTP获取图片: $imageUrl');
      return await _getImageFromHttp(imageUrl, customHeaders: customHeaders);
    }
  }
  
  /// 获取当前页面的Cookie
  Future<String> getCookies() async {
    try {
      final cookieScript = "document.cookie;";
      final dynamic cookieResult = await controller
          .evaluateJavascript(source: cookieScript)
          .timeout(const Duration(seconds: 3))
          .catchError((error) {
        debugPrint('获取Cookie错误: $error');
        return "";
      });

      if (cookieResult != null && cookieResult is String) {
        debugPrint('获取Cookie成功');
        return cookieResult;
      }
    } catch (e) {
      debugPrint('获取Cookie异常: $e');
    }
    return "";
  }
  
  /// 从HTTP直接获取图片数据
  Future<Uint8List?> _getImageFromHttp(String imageUrl, {Map<String, String>? customHeaders}) async {
    try {
      debugPrint('开始从HTTP获取图片: $imageUrl');
      
      // 获取Cookie
      final String cookies = await getCookies();
      
      // 构建HTTP请求头
      Map<String, String> headers = {
        'Referer': currentUrl,
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
      };
      
      // 添加Cookie
      if (cookies.isNotEmpty) {
        headers['Cookie'] = cookies;
      }
      
      // 添加自定义请求头
      if (customHeaders != null) {
        headers.addAll(customHeaders);
      }
      
      final fullUrl = imageUrl.startsWith('http') ? imageUrl : 'https:$imageUrl';
      
      final response = await http.get(
        Uri.parse(fullUrl),
        headers: headers,
      ).timeout(const Duration(seconds: 15));
      
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        if (bytes.isNotEmpty) {
          debugPrint('HTTP成功获取图片数据: ${bytes.length} 字节');
          return bytes;
        }
      }
      
      debugPrint('HTTP获取图片失败: 状态码 ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('HTTP获取图片异常: $e');
      return null;
    }
  }
  
  /// 从WebView获取图片的二进制数据
  Future<Uint8List?> _getImageFromWebView(String imageUrl) async {
    try {
      debugPrint('开始从WebView获取图片: $imageUrl');
      
      final String script = '''(async function() {
        try {
          const img = new Image();
          img.crossOrigin = "anonymous";
          img.src = "$imageUrl";
          
          return new Promise((resolve) => {
            img.onload = function() {
              const canvas = document.createElement('canvas');
              canvas.width = img.width;
              canvas.height = img.height;
              
              const ctx = canvas.getContext('2d');
              ctx.drawImage(img, 0, 0);
              
              // 获取base64数据
              resolve(canvas.toDataURL('image/png').split(',')[1]);
            };
            
            img.onerror = function() {
              console.error('图片加载失败');
              resolve(null);
            };
            
            // 设置超时
            setTimeout(() => resolve(null), 5000);
          });
        } catch (e) {
          console.error('获取图片失败:', e);
          return null;
        }
      })();''';
      
      final result = await controller.evaluateJavascript(source: script);
      if (result is String && result != "null") {
        final bytes = base64Decode(result);
        debugPrint('WebView成功获取图片数据: ${bytes.length} 字节');
        return bytes;
      }
      
      debugPrint('WebView获取图片失败: 结果为null或无效');
      return null;
    } catch (e) {
      debugPrint('WebView获取图片异常: $e');
      return null;
    }
  }
}