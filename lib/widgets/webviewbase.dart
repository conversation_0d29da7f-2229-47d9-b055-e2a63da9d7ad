import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewBase extends StatefulWidget {
  final dynamic url;
  final VoidCallback? onPageStarted;
  final VoidCallback? onPageFinished;

  const WebViewBase(
    this.url, {
    super.key,
    this.onPageStarted,
    this.onPageFinished,
  });

  @override
  State<WebViewBase> createState() => _WebViewBaseState();
}

class _WebViewBaseState extends State<WebViewBase> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    // 初始化 WebView
    WebViewController controller = WebViewController();

    // 设置 JavaScript 模式
    controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    // 设置背景以防加载过程闪黑色
    controller.setBackgroundColor(const Color(0x00000000)); 
    // 设置导航代理
    controller.setNavigationDelegate(
      NavigationDelegate(
        onPageStarted: (String url) {
          widget.onPageStarted?.call();
        },
        onPageFinished: (String url) {
          widget.onPageFinished?.call();
        },
        onProgress: (int progress) {
          // 进度回调，可以用于显示加载进度
        },
      ),
    );

    // 加载指定 URL
    controller.loadRequest(Uri.parse(widget.url));

    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
        ],
      ),
    );
  }
}
