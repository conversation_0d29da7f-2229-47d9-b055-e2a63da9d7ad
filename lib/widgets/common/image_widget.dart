import 'dart:io';

import 'package:flutter/material.dart';
import '../../util/theme_manager.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

/// 图片相关的通用widgets
class ImageWidget {
  /// 构建占位图像的辅助方法
  static Widget placeholder(double width, double height) {
    // Calculate a reasonable icon size
    double iconSize = 24.0;
    if (width.isFinite && height.isFinite) {
      iconSize = (width < height ? width : height) * 0.3;
    }

    return Container(
      width: width.isFinite ? width : double.infinity,
      height: height.isFinite ? height : double.infinity,
      color: ThemeManager.currentTheme.borderAreaBgColor.withValues(alpha: .3),
      child: Center(
        child: Icon(
          Icons.image_outlined,
          size: iconSize,
          color: ThemeManager.currentTheme.textColor.withValues(alpha: .5),
        ),
      ),
    );
  }

  /// 从本地文件加载图片
  static Widget loadImage(BuildContext context, String imagePath, {double width = 40, double height = 40}) {
    return Container(
      width: width,
      height: height,
      child: Image.file(
        File(imagePath),
        width: width,
        height: height,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Error loading image: $error');
          return Container(
            width: width,
            height: height,
            color: Colors.grey[200],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image_rounded,
                  size: 32,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 8),
                Text(
                  AppLocalizations.of(context)!.imageNotFound,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 显示Pro图标
  static Widget proIcon() {
    return Image.asset(
      'images/icons/icon_pro_l.png',
      height: 20,
      fit: BoxFit.contain,
    ); 
  }

  static Widget proIconS() {
    return Image.asset(
      'images/icons/icon_pro_s.png',
      height: 20,
      width: 20,
      fit: BoxFit.contain,
    ); 
  }
}