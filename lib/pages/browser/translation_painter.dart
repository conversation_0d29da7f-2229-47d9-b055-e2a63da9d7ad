import 'package:flutter/material.dart';
import 'dart:math' as math; 

class TranslationPainter extends CustomPainter {
  final List<Map<String, dynamic>> translationData;
  final List<Map<String, dynamic>> imageRects;
  
  TranslationPainter({
    required this.translationData,
    required this.imageRects,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (translationData.isEmpty || imageRects.isEmpty) {
      debugPrint("TranslationPainter: 没有数据可绘制 - 翻译数据: ${translationData.length}, 图片位置: ${imageRects.length}");
      return;
    }
    
    debugPrint("TranslationPainter: 开始绘制 ${translationData.length} 张图片的翻译，画布大小: ${size.width} x ${size.height}");
    
    for (var data in translationData) {
      final int imageIndex = data['imageIndex'];
      if (imageIndex >= imageRects.length) {
        debugPrint("TranslationPainter: 图片索引越界 $imageIndex >= ${imageRects.length}");
        continue;
      }
      
      final Map<String, dynamic> imageRect = imageRects[imageIndex];
      final List<dynamic> translations = data['translations'];
      
      // 获取图片位置信息
      final double left = (imageRect['left'] as num).toDouble();
      final double top = (imageRect['top'] as num).toDouble();
      final double width = (imageRect['width'] as num).toDouble();
      final double height = (imageRect['height'] as num).toDouble();
      
      // 计算图片的缩放比例 - 服务器返回的坐标是基于原始图片尺寸的
      final num? naturalWidth = imageRect['naturalWidth'];
      final num? naturalHeight = imageRect['naturalHeight'];
      
      // 确保原始尺寸有效，否则使用合理的默认值
      final double scaleX = width / (naturalWidth != null && naturalWidth > 0 ? naturalWidth.toDouble() : width);
      final double scaleY = height / (naturalHeight != null && naturalHeight > 0 ? naturalHeight.toDouble() : height);
      
      // 绘制图片边框（调试用）
      final debugPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;
      canvas.drawRect(Rect.fromLTWH(left, top, width, height), debugPaint);
      
      for (var item in translations) {
        final points = item["bound"];
        final text = item["translated_text"];
        
        if (points == null || points.isEmpty || text == null) {
          debugPrint("TranslationPainter: 无效的翻译数据");
          continue;
        }
        
        // 计算文本在WebView中的实际位置
        final path = Path();
        
        // 创建文本区域路径 - 将API返回的坐标（以图片左上角为原点）转换为屏幕坐标
        final List<Offset> offsets = [];
        for (var point in points) {
          // 应用缩放并加上图片在页面中的偏移
          final double x = left + (point["x"] as num).toDouble() * scaleX;
          final double y = top + (point["y"] as num).toDouble() * scaleY;
          offsets.add(Offset(x, y));
        }
        
        // 绘制文本区域
        path.moveTo(offsets[0].dx, offsets[0].dy);
        for (int i = 1; i < offsets.length; i++) {
          path.lineTo(offsets[i].dx, offsets[i].dy);
        }
        path.close();
        
        // 绘制半透明背景
        canvas.drawPath(
          path, 
          Paint()..color = Colors.white.withValues(alpha: .8)
        );
        
        // 计算文本位置（中心点）
        double centerX = 0;
        double centerY = 0;
        for (var offset in offsets) {
          centerX += offset.dx;
          centerY += offset.dy;
        }
        centerX = centerX / offsets.length;
        centerY = centerY / offsets.length;
        
        // 计算文本区域大小
        double minX = double.infinity;
        double minY = double.infinity;
        double maxX = 0;
        double maxY = 0;
        
        for (var offset in offsets) {
          minX = math.min(minX, offset.dx);
          minY = math.min(minY, offset.dy);
          maxX = math.max(maxX, offset.dx);
          maxY = math.max(maxY, offset.dy);
        }
        
        final textWidth = maxX - minX;
        
        // 绘制文本
        final textPainter = TextPainter(
          text: TextSpan(
            text: text,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.ltr,
        );
        textPainter.layout(maxWidth: textWidth > 0 ? textWidth : 100);
        
        // 居中绘制
        textPainter.paint(
          canvas, 
          Offset(
            centerX - textPainter.width / 2,
            centerY - textPainter.height / 2
          )
        );
        
        // 绘制文本区域边框（调试用）
        canvas.drawPath(
          path,
          Paint()
            ..color = Colors.red
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1.0
        );
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant TranslationPainter oldDelegate) {
    // 添加一个简单的缓存机制，使用哈希值比较
    final bool dataChanged = _computeDataHash() != oldDelegate._computeDataHash();
    
    // 如果数据变化了，可以打印一条日志
    if (dataChanged) {
      debugPrint("TranslationPainter: 数据已变化，需要重绘");
    }
    
    return dataChanged;
  }
  
  // 计算数据的哈希值，用于快速比较
  int _computeDataHash() {
    int hash = 0;
    
    // 计算 translationData 的哈希值
    for (var data in translationData) {
      hash = hash * 31 + (data['imageIndex'] ?? 0).hashCode;
      final translations = data['translations'] as List?;
      hash = hash * 31 + (translations?.length ?? 0).hashCode;
    }
    
    // 计算 imageRects 的哈希值
    for (var rect in imageRects) {
      hash = hash * 31 + ((rect['left'] ?? 0).hashCode);
      hash = hash * 31 + ((rect['top'] ?? 0).hashCode);
      hash = hash * 31 + ((rect['width'] ?? 0).hashCode);
      hash = hash * 31 + ((rect['height'] ?? 0).hashCode);
    }
    
    return hash;
  }
}