import UIKit

class LaunchViewController: UIViewController {
    
    let logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "logo")
        imageView.contentMode = .scaleAspectFit
        imageView.alpha = 0  // 初始透明度
        return imageView
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        animateLogo()
    }
    
    func setupUI() {
        view.backgroundColor = .white
        view.addSubview(logoImageView)
        logoImageView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            logoImageView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            logoImageView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            logoImageView.widthAnchor.constraint(equalToConstant: 100),
            logoImageView.heightAnchor.constraint(equalToConstant: 100)
        ])
    }
    
    func animateLogo() {
        UIView.animate(withDuration: 1.5, animations: {
            self.logoImageView.alpha = 1
        }) { _ in
            // 动画完成后的操作
            self.performSegue(withIdentifier: "showMain", sender: nil)
        }
    }
}
