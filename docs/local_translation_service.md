# Local Translation Service Documentation

## Overview

The `LocalTranslationService` is a singleton service that provides offline text translation functionality using Google's ML Kit Translation package. It supports multiple language pairs and manages language model downloads automatically.

## Features

- **Singleton Pattern**: Ensures only one instance of the service exists
- **Offline Translation**: Works without internet connection once models are downloaded
- **Multiple Language Support**: Supports all languages available in ML Kit Translation
- **Model Management**: Automatic download and management of language models
- **Batch Translation**: Support for translating multiple texts efficiently
- **Error Handling**: Comprehensive error handling with custom exceptions
- **Language Pair Helpers**: Predefined common language pairs for convenience

## Installation

The service uses the `google_mlkit_translation` package which is already included in the project's `pubspec.yaml`:

```yaml
dependencies:
  google_mlkit_translation: ^0.13.0
```

## Usage

### Basic Usage

```dart
import 'package:imtrans/services/local_translation_service.dart';
import 'package:google_mlkit_translation/google_mlkit_translation.dart';

// Get the singleton instance
final translationService = LocalTranslationService();

// Initialize the service (required before use)
await translationService.initialize();

// Check if language models are downloaded
final bool englishDownloaded = await translationService.isLanguageDownloaded(TranslateLanguage.english);
final bool japaneseDownloaded = await translationService.isLanguageDownloaded(TranslateLanguage.japanese);

// Download language models if needed
if (!englishDownloaded) {
  await translationService.downloadLanguage(TranslateLanguage.english);
}
if (!japaneseDownloaded) {
  await translationService.downloadLanguage(TranslateLanguage.japanese);
}

// Translate text
final String translatedText = await translationService.translateText(
  'こんにちは',
  TranslateLanguage.japanese,
  TranslateLanguage.english,
);
print(translatedText); // "Hello"

// Clean up when done
await translationService.dispose();
```

### Batch Translation

```dart
final List<String> textsToTranslate = ['こんにちは', '世界', 'ありがとう'];

final List<String> translatedTexts = await translationService.translateTexts(
  textsToTranslate,
  TranslateLanguage.japanese,
  TranslateLanguage.english,
);

for (int i = 0; i < textsToTranslate.length; i++) {
  print('${textsToTranslate[i]} -> ${translatedTexts[i]}');
}
```

### Using Predefined Language Pairs

```dart
import 'package:imtrans/services/local_translation_service.dart';

// Use predefined language pairs
final pair = LanguagePairs.japaneseToEnglish;
final translatedText = await translationService.translateText(
  'こんにちは',
  pair.source,
  pair.target,
);
```

### Language Management

```dart
// Get all available languages
final List<TranslateLanguage> availableLanguages = translationService.getAvailableLanguages();

// Get downloaded languages
final List<TranslateLanguage> downloadedLanguages = await translationService.getDownloadedLanguages();

// Download a language model
final bool success = await translationService.downloadLanguage(TranslateLanguage.chinese);

// Delete a language model to free space
final bool deleted = await translationService.deleteLanguage(TranslateLanguage.chinese);
```

## API Reference

### LocalTranslationService

#### Methods

##### `initialize()`
```dart
Future<void> initialize()
```
Initializes the translation service. Must be called before using any translation functionality.

**Throws**: `TranslationException` if initialization fails.

##### `translateText(String text, TranslateLanguage sourceLanguage, TranslateLanguage targetLanguage)`
```dart
Future<String> translateText(String text, TranslateLanguage sourceLanguage, TranslateLanguage targetLanguage)
```
Translates text from source language to target language.

**Parameters**:
- `text`: Text to translate
- `sourceLanguage`: Source language
- `targetLanguage`: Target language

**Returns**: Translated text

**Throws**: `TranslationException` if translation fails or models not downloaded

##### `translateTexts(List<String> texts, TranslateLanguage sourceLanguage, TranslateLanguage targetLanguage)`
```dart
Future<List<String>> translateTexts(List<String> texts, TranslateLanguage sourceLanguage, TranslateLanguage targetLanguage)
```
Translates multiple texts in batch.

**Parameters**:
- `texts`: List of texts to translate
- `sourceLanguage`: Source language
- `targetLanguage`: Target language

**Returns**: List of translated texts in the same order

**Throws**: `TranslationException` if translation fails

##### `isLanguageDownloaded(TranslateLanguage language)`
```dart
Future<bool> isLanguageDownloaded(TranslateLanguage language)
```
Checks if a language model is downloaded.

**Parameters**:
- `language`: Language to check

**Returns**: True if model is downloaded, false otherwise

##### `downloadLanguage(TranslateLanguage language)`
```dart
Future<bool> downloadLanguage(TranslateLanguage language)
```
Downloads a language model.

**Parameters**:
- `language`: Language model to download

**Returns**: True if download successful, false otherwise

**Throws**: `TranslationException` if download fails

##### `deleteLanguage(TranslateLanguage language)`
```dart
Future<bool> deleteLanguage(TranslateLanguage language)
```
Deletes a language model to free storage space.

**Parameters**:
- `language`: Language model to delete

**Returns**: True if deletion successful, false otherwise

**Throws**: `TranslationException` if deletion fails

##### `getAvailableLanguages()`
```dart
List<TranslateLanguage> getAvailableLanguages()
```
Gets list of all available languages for translation.

**Returns**: List of available languages

##### `getDownloadedLanguages()`
```dart
Future<List<TranslateLanguage>> getDownloadedLanguages()
```
Gets list of downloaded language models.

**Returns**: List of downloaded languages

##### `dispose()`
```dart
Future<void> dispose()
```
Disposes all translators and cleans up resources.

#### Properties

##### `isInitialized`
```dart
bool get isInitialized
```
Returns whether the service has been initialized.

### LanguagePair

Represents a language pair for translation.

#### Properties

- `TranslateLanguage source`: Source language
- `TranslateLanguage target`: Target language

#### Methods

- `String toString()`: String representation
- `bool operator ==(Object other)`: Equality comparison
- `int get hashCode`: Hash code for collections

### LanguagePairs

Helper class with predefined common language pairs.

#### Static Properties

- `LanguagePair englishToJapanese`
- `LanguagePair japaneseToEnglish`
- `LanguagePair englishToChinese`
- `LanguagePair chineseToEnglish`
- `LanguagePair englishToKorean`
- `LanguagePair koreanToEnglish`

### TranslationException

Custom exception for translation operations.

#### Properties

- `String message`: Error message
- `dynamic originalError`: Original error (optional)

#### Methods

- `String toString()`: String representation including original error if present

## Error Handling

The service uses custom `TranslationException` for error handling:

```dart
try {
  final result = await translationService.translateText(
    'Hello',
    TranslateLanguage.english,
    TranslateLanguage.japanese,
  );
} catch (e) {
  if (e is TranslationException) {
    print('Translation Error: ${e.message}');
    if (e.originalError != null) {
      print('Original Error: ${e.originalError}');
    }
  }
}
```

## Common Error Scenarios

1. **Service Not Initialized**: Call `initialize()` before using translation functions
2. **Language Model Not Downloaded**: Download required language models first
3. **Empty Text**: Service handles empty/whitespace text gracefully
4. **Network Issues**: May affect model downloads but not offline translation

## Supported Languages

The service supports all languages available in ML Kit Translation:

- Arabic, Bengali, Chinese, Czech, Danish, Dutch, English
- Finnish, French, German, Greek, Hebrew, Hindi, Hungarian
- Italian, Japanese, Korean, Norwegian, Polish, Portuguese
- Russian, Spanish, Swedish, Thai, Turkish, Ukrainian, Urdu

## Performance Considerations

- **Model Downloads**: Language models are ~30-50MB each
- **First Translation**: May be slower as models are loaded into memory
- **Subsequent Translations**: Very fast as models are cached
- **Memory Usage**: Each active translator uses memory; dispose when done
- **Storage Management**: Delete unused language models to save space

## Integration with Browser OCR

The service is integrated with the browser's local OCR feature:

```dart
// In MultiBrowserPage
final textElements = await _ocrService.extractTextFromBytes(imageBytes);
for (final element in textElements) {
  final translatedText = await _translationService.translateText(
    element.text,
    TranslateLanguage.japanese,
    TranslateLanguage.english,
  );
  // Create overlay with translated text at element.boundingBox position
}
```

## Testing

Comprehensive unit tests are available in `test/services/local_translation_service_test.dart`. Run tests with:

```bash
flutter test test/services/local_translation_service_test.dart
```

## Best Practices

1. **Initialize Once**: Initialize the service once at app startup
2. **Check Downloads**: Always check if models are downloaded before translation
3. **Handle Errors**: Wrap translation calls in try-catch blocks
4. **Manage Storage**: Regularly clean up unused language models
5. **Batch Operations**: Use `translateTexts()` for multiple translations
6. **Dispose Properly**: Call `dispose()` when shutting down the app

## Model Storage Requirements

Language models require significant storage space:
- Each model: ~30-50MB
- Recommended: Keep only frequently used language pairs
- Consider user preferences for automatic model management

## Offline Capabilities

Once language models are downloaded:
- Translation works completely offline
- No network connection required
- Fast processing for real-time applications
- Ideal for privacy-sensitive applications
