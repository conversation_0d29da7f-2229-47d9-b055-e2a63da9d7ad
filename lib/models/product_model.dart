/// 翻译图片的数据基础处理文件
/// 包括三个类和一个Exception类：
/// 1. ProductImage 翻译图片的数据基础类
/// 2. Product 产品数据基础类
/// 3. ProductModel 产品数据模型类
/// 4. ProductModelException 产品数据模型异常类
///
/// 每一个被翻译的“产品”都是一个Product实例，Product实例中包含一个ProductImage实例的的列表
/// 如果“产品”只包含一个ProductImage实例，则为单张图片上传，如果包含多个，则为ZIP文件上传
/// 结构为：
/// Product
///   |- id: ID //感觉没什么用目前
///   |- productId: 产品ID 与服务器保持一致的id
///   |- name: 产品名称
///   |- coverUrl: 封面图片的URL
///   |- localCoverFileName: 封面图片的本地缓存文件名
///   |- targetLang: 翻译目标语言
///   |- createdAt: 创建时间
///   |- status: 翻译状态
///   |- isRead: 是否已读
///   |- height: 封面图片的高度
///   |- images: ProductImage实例的列表
///      | -[{
///         name: 图片名称1
///         url: 图片的URL1
///         localFileName: 本地缓存文件名1
///         translateStatus: 翻译状态1
///         translateResult: 翻译结果1
///         |- [{   
///            bound: {x, y}, {x, y}, {x, y}, {x, y}翻译结果的边界1
///            translated_text:  {"Text1"} 翻译结果的文本1
///            },
///         |- {
///            bound: {x, y}, {x, y}, {x, y}, {x, y}翻译结果的边界2
///            translated_text:  {"Text2"} 翻译结果的文本2
///            }]
///         },
///      |- {
///         name: 图片名称2
///         url: 图片的URL2
///         translateStatus: 翻译状态2
///         translateResult: 翻译结果2
///         localFileName: 本地缓存文件名2
///       }
///     ]
/// 

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:imtrans/util/util.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../services/server_request.dart';

/// 产品图片类
class ProductImage {
  ProductImage();

  String? _name;
  String? _url; // 原图片url
  int? _translateStatus = Product.STATUS_TRANSLATING;
  // String? _cacheFilePath = ""; // 缓存文件路径
  String? _localFileName = ""; // 本地缓存文件名

  /// 翻译结果
  /// 
  /// 由于服务器返回的数据结构中，translate_result的格式为: 
  /// translate_result {
  ///   translated_text: [
  ///     {bound: [{},{},{},{}], translated_text: {'en': 'text'}},
  ///     {bound: [{},{},{},{}], translated_text: {'en': 'text'}}
  ///   ]
  ///   ... 其他字段
  /// }
  /// 所以需要精简无用的数据，只将translated_text保存，并去掉其中的目标语言key
  /// 改变结构为，这也是缓存中的结构：
  /// _translateResult [
  ///   {bound: [{},{},{},{}], translated_text: text},
  ///   {bound: [{},{},{},{}], translated_text: text}
  /// ]
  List<dynamic>? _translateResult = [];

  String? get name => _name;
  String? get url => _url;
  int? get translateStatus => _translateStatus;
  List<dynamic>? get translateResult => _translateResult;
  String? get localFileName => _localFileName;
  static Directory get localCacheDir => Directory('${Util.appPath}/product_images');

  void setName(String? name) => _name = name;
  void setUrl(String? url) => _url = url;
  void setTranslateResult(List<dynamic>? result) => _translateResult = result;
  void setTranslateStatus(int? status) => _translateStatus = status;
  void setLocalFileName(String? name) => _localFileName = name;

  Map<String, dynamic> toMap() => {
    "image_name": _name == null || _name!.isEmpty ? "img_${DateTime.now().microsecondsSinceEpoch}" : _name,
    "image_url": _url,
    "translate_status": _translateStatus,
    "translate_result": _translateResult,
    "local_file_name": _localFileName,
  };

  /// 从Map创建ProductImage实例
  factory ProductImage.fromMap(Map<String, dynamic> json) {
    final productImage = ProductImage();
    productImage.setName(json["image_name"]);
    productImage.setUrl(json["image_url"]);
    productImage.setTranslateStatus(json["translate_status"]);
    productImage.setTranslateResult(json["translate_result"]);
    productImage.setLocalFileName(json["local_file_name"]);
    return productImage;
  }
}

/// 产品类
class Product {
  Product();
  // static const Map<String, String> targetLanguages = {
  //   'English': 'en',
  //   'Japanese': 'ja',
  //   'Korean': 'ko',
  //   'French': 'fr',
  //   'German': 'de',
  //   'Spanish': 'es',
  //   'Italian': 'it',
  //   'Thai': 'th',
  //   'Vietnamese': 'vi',
  //   'Indonesian': 'id',
  //   'Malay': 'ms',
  //   'Chinese': 'zh',
  // };
  // static const String DEFAULT_TARGET_LANG = 'English';

  // 修改为
  static const Map<String, String> targetLanguages = {
    'en': 'en',
    'ja': 'ja',
    'ko': 'ko',
    'fr': 'fr',
    'de': 'de',
    'es': 'es',
    'it': 'it',
    'th': 'th',
    'vi': 'vi',
    'id': 'id',
    'ms': 'ms',
    'zh': 'zh',
  };
  static const String DEFAULT_TARGET_LANG = 'en';

  static const int STATUS_TRANSLATING = 0;
  static const int STATUS_TRANSLATED = 1;
  static const int STATUS_FAILED = 2;
  static const int RETURN_CODE_SUCCESS = 0;
  static const int RETURN_CODE_NEEDVIP = -2;

  // Basic info
  int? _id;
  String? _productId;
  String? _name;
  String? _coverUrl;
  String? _localCoverPath;
  String? _localCoverFileName;
  String? _targetLang;
  String? _createdAt;
  List<ProductImage> _images = [];
  bool _selected = false;
  double? _height;
  int? _status;
  double? _opacity = 1.0;
  // Getters
  int? get id => _id;
  String? get productId => _productId;
  String? get name => _name;
  String? get coverUrl => _coverUrl;
  String? get localCoverPath => _localCoverPath;
  String? get localCoverFileName => _localCoverFileName;
  String? get targetLang => _targetLang;
  String? get createdAt => _createdAt;
  List<ProductImage> get images => _images;
  int? get status => _status;
  bool get selected => _selected;
  double? get height => _height;
  double? get opacity => _opacity;
  bool isRead = false;

  // Setters
  void setName(String? name) => _name = name;
  void setCoverUrl(String? url) => _coverUrl = url;
  void setLocalCoverPath(String? path) => _localCoverPath = path;
  void setLocalCoverFileName(String? path) => _localCoverFileName = path;
  void setTargetLang(String? lang) => _targetLang = lang;
  void setStatus(int status) => _status = status;
  void addImage(ProductImage image) => _images.add(image);
  void setSelected(bool value) => _selected = value;
  void setHeight(double? height) => _height = height;
  void setOpacity(double? opacity) => _opacity = opacity;
  void setImages(List<ProductImage> newImages) {
    _images = newImages;
  }

  /// 用于在控制器中创建产品所需的数据
  Map<String, dynamic> toCreateRequestBody() => {
    "product_name": _name ?? "p_${DateTime.now().microsecondsSinceEpoch}",
    "cover_img": _coverUrl,
    "target_lang": _targetLang,
    "product_images": _images.map((image) => image.toMap()).toList(),
  };

  /// 从基础Map数据创建Product实例的私有方法
  static Product _fromMap(Map<String, dynamic> json, {bool fromCache = false}) {
    final product = Product();
    product._id = json["id"] is String ? int.parse(json["id"]) : json["id"];
    product._productId = json["product_id"];
    product._name = json["product_name"];
    product._coverUrl = json["cover_img"];
    product._targetLang = json["target_lang"];
    product._createdAt = json["created_at"];
    product._localCoverPath = json["local_cover_path"];
    product._status = json["status"] is String ? int.parse(json["status"]) : json["status"];
    product.isRead = json['is_read'] ?? false;
    product.setHeight(json["height"]?.toDouble());

    // 处理图片列表
    if (json["product_images"] != null) {
      final images = json["product_images"] as List;
      product._images = images.map((img) {
        final productImage = ProductImage();
        productImage.setName(img["image_name"]);
        productImage.setUrl(img["image_url"]);
        productImage.setTranslateStatus(img["translate_status"]);
        productImage.setLocalFileName(img["local_file_name"]);

        if (img["translate_result"] != null && img["translate_result"].isNotEmpty) {
          if (fromCache) {
            productImage.setTranslateResult(img["translate_result"]);
          }
          else {
            final translatedTexts = img["translate_result"]['translated_text'] as List;
            productImage.setTranslateResult(translatedTexts.map((item) {
              if (item["translated_text"] != null && item["translated_text"][product._targetLang] != null) {
              return {
                "bound": item["bound"],
                "translated_text": item["translated_text"][product._targetLang]
              };
            }
              return item;
            }).toList());
          }
        }
        return productImage;
      }).toList();

      // 更新产品的翻译状态
      product.updateTranslationStatus();
    }
    return product;
  }

  /// 从服务器返回的list数据Map创建Product实例
  /// 对应的API是https://apifox.com/apidoc/shared-82cad01a-0cb4-4534-ba73-cc1b6f4d362e/api-237956041
  factory Product.fromMap(Map<String, dynamic> json) {
    return _fromMap(json, fromCache: false);
  }

  /// 从缓存文件中加载Product实例
  /// 对应的translate_result结构和服务器返回的结构不同，需要单独处理
  factory Product.fromCacheMap(Map<String, dynamic> json) {
    return _fromMap(json, fromCache: true);
  }

  Map<String, dynamic> toCacheMap() => {
    "id": id,
    "product_id": productId,
    "product_name": name,
    "cover_img": coverUrl,
    "target_lang": targetLang,
    "created_at": createdAt,
    "product_images": images.map((img) => img.toMap()).toList(),
    "is_read": isRead,
    "local_cover_path": localCoverPath,
    "height": height,
  };

  /// 从图片文件读取高度
  Future<double> _getImageHeight(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final completer = Completer<double>();
    
    decodeImageFromList(bytes, (Image image) {
      completer.complete(image.height.toDouble());
    });
    
    return completer.future;
  }

  // 检查并更新产品的翻译状态
  void updateTranslationStatus() {
    if (_images.isEmpty) {
      _status = 0;
      return;
    }

    // 检查是否所有图片的翻译状态都不为0
    bool allTranslated = _images.every((image) => image.translateStatus != 0);
    _status = allTranslated ? 1 : 0;
  }
}

/// 产品模型异常类
class ProductModelException implements Exception {
  final String message;
  final int? code;
  
  ProductModelException(this.message, {this.code});
  
  @override
  String toString() => 'ProductModelException: $message${code != null ? ' (code: $code)' : ''}';
}

/// 产品模型类
class ProductModel {
  List<Product> _products = [];
  
  // Getters
  List<Product> get products => _products;

  Future<File> get _productsCacheFile async {
    return File('${Util.appPath}/products_cache.json');
  }

  Future<File> _imagesCacheFile(String productId) async {
    return File('${Util.appPath}/product_images/$productId.json');
  }

  // Directory get imagesDir => ProductImage.localCacheDir;

  /// 验证服务器响应
  void _validateResponse(Map<String, dynamic>? response, {String? operation}) {
    if (response == null) {
      throw ProductModelException('No response from server', code: 99);
    }
    
    final code = response["code"];
    if (code != Product.RETURN_CODE_SUCCESS) {
      throw ProductModelException(
        response["msg"] ?? 'Unknown error',
        code: code
      );
    }

    // 验证响应数据完整性
    if (operation == 'list' && !response.containsKey("list")) {
      throw ProductModelException('Invalid response: missing products data');
    }
    if (operation == 'create' && !response.containsKey("product_id")) {
      throw ProductModelException('Invalid response: missing product_id');
    }
  }

  /// 创建产品
  Future<String?> create(Product product) async {
    try {
      // 数据验证
      if (product.targetLang == null) {
        throw ProductModelException('Target language is required');
      }
      if (product.images.isEmpty) {
        throw ProductModelException('At least one image is required');
      }
      if (product.coverUrl == null) {
        throw ProductModelException('Cover image is required');
      }

      final response = await ServerRequest.postData(
        ServerRequest.createProduct,
        product.toCreateRequestBody()
      );

      _validateResponse(response, operation: 'create');
      return response["product_id"];
    } catch (e) {
      debugPrint('Error creating product in model: $e');
      rethrow;
    }
  }

  /// 查询产品状态
  Future<Product?> query(String productId) async {
    try {
      if (productId.isEmpty) {
        throw ProductModelException('Product ID is required');
      }
      final response = await ServerRequest.getData(
        ServerRequest.queryProduct,
        {"product_id": productId}
      );
      _validateResponse(response, operation: 'query');
      
      if (response["product"] == null) {
        // debugPrint('No product data found for ID: $productId');
        return null;
      }
      return Product.fromMap(response["product"]);
    } 
    catch (e) {
      debugPrint('Error querying product: $e');
      rethrow;
    }
  }

  /// 更新产品信息
  Future<bool> update(Map<String, dynamic> data) async {
    try {
      // 数据验证
      if (!data.containsKey("product_id")) {
        throw ProductModelException('Product ID is required');
      }
      if (data["product_id"]?.toString().isEmpty ?? true) {
        throw ProductModelException('Invalid product ID');
      }

      final response = await ServerRequest.postData(
        ServerRequest.editProduct,
        data
      );

      _validateResponse(response, operation: 'update');
      return true;
    } catch (e) {
      debugPrint('Error updating product: $e');
      return false;
    }
  }

  /// 删除产品
  Future<bool> delete(String productId) async {
    try {
      if (productId.isEmpty) {
        throw ProductModelException('Product ID is required');
      }

      final response = await ServerRequest.postData(
        ServerRequest.deleteProduct,
        {"product_id": productId}
      );

      _validateResponse(response, operation: 'delete');
      return true;
    } catch (e) {
      debugPrint('Error deleting product: $e');
      return false;
    }
  }

  /// 上传图片
  Future<String?> uploadImage(Uint8List imageData, {String? filename}) async {
    try {
      if (imageData.isEmpty) {
        throw ProductModelException('Image data is empty');
      }

      final response = await ServerRequest.upload(
        ServerRequest.fileUpload,
        imageData,
        filename ?? 'img_${DateTime.now().microsecondsSinceEpoch}.jpg'
      );
      _validateResponse(response, operation: 'upload');
      
      return response["file_url"];
    } catch (e) {
      debugPrint('Error uploading image: $e');
      return null;
    }
  }

  /// 创建封面图
  Future<Uint8List?> createCoverImage(Uint8List imageData) async {
    try {
      if (imageData.isEmpty) {
        throw ProductModelException('Image data is empty');
      }

      return await compute((Uint8List data) {
        final decoder = img.decodeImage(data);
        if (decoder == null) {
          throw ProductModelException('Failed to decode image');
        }
        
        final width = decoder.width;
        final height = decoder.height;
        final coverHeight = (height * 200 / width).round();
        
        final cover = img.copyResize(
          decoder,
          width: 200,
          height: coverHeight,
          interpolation: img.Interpolation.average
        );
        
        return img.encodeJpg(cover, quality: 85);
      }, imageData);
    } catch (e) {
      debugPrint('Error creating cover image: $e');
      return null;
    }
  }

  /// 获取产品列表
  /// 使用此方法获得的产品列表，只调用了服务端的list接口，不包含每个image的翻译结果
  /// 需要调用 _loadProductsTranslations 方法，异步加载每个产品的详细翻译结果
  Future<List<Product>> loadFromNetwork({required int page, required int pageSize}) async {
    List<Product> products = [];
    try {
      if (page < 1) throw ProductModelException('Invalid page number');
      if (pageSize < 1) throw ProductModelException('Invalid page size');

      final response = await ServerRequest.getData(ServerRequest.getProductList, {"page": page, "page_size": pageSize});

      _validateResponse(response, operation: 'list');
      final List<dynamic> productsData = response["list"] ?? [];
      products = productsData.map((data) => Product.fromMap(data)).toList();

      for (var product in products) {
        product.isRead = await isRead(product.productId!);
      }

      return products;
    } catch (e) {
      debugPrint('Error in product list: $e');
      return products;
    }
  }

  /// 从缓存加载产品列表
  Future<List<Product>> loadFromCache() async {
    Directory imagesDir = ProductImage.localCacheDir;
    try {
        final file = await _productsCacheFile;
        debugPrint('Loading products from cache file: ${file.path}');
        if (!await file.exists()) {
            debugPrint('Cache file does not exist.');
            return [];
        }

        final contents = await file.readAsString();
        final List<dynamic> productsJson = jsonDecode(contents);

        // 逐个处理产品数据，找出问题所在
        // List<Product> products = [];
        // for (var json in productsJson) {
        //   debugPrint('Processing product: ${json["product_id"]}');
        //   try {
        //     Product p = Product.fromMap(json);
        //     debugPrint(' --- Successfully');
        //   } catch (e) {
        //     debugPrint('Error parsing product JSON: $json');
        //     debugPrint('Error details: $e');
        //   }
        // }

        final products = productsJson.map((json) => Product.fromCacheMap(json)).toList();
        // debugPrint('Deserialized ${products.length} products from cache.');

        // 加载本地已读状态和验证图片缓存
        for (var product in products) {
          // debugPrint('Processing product: ${product.productId}');
          if (product.productId != null) {
            product.isRead = await isRead(product.productId!);

            // 验证本地封面图片
            if (product.localCoverFileName != null) {
              final coverImageFile = File('${imagesDir.path}/${product.localCoverFileName!}');
              if (!await coverImageFile.exists() && product.coverUrl != null) {
                // debugPrint('Cover image not found locally, downloading from ${product.coverUrl}');
                final response = await http.get(Uri.parse(product.coverUrl!));
                if (response.statusCode == 200) {
                    await coverImageFile.writeAsBytes(response.bodyBytes);
                    product.setHeight(await product._getImageHeight(coverImageFile));
                } else {
                    // debugPrint('Failed to download cover image: ${response.statusCode}');
                    product.setLocalCoverFileName(null);
                }
              } else if (await coverImageFile.exists()) {
                  product.setHeight(await product._getImageHeight(coverImageFile));
              } else {
                  // debugPrint('Cover image not available for product: ${product.productId}');
                  product.setLocalCoverFileName(null);
              }
            }

            // 加载本地产品图片
            final images = await loadImagesFromCache(product.productId!);
            if (images.isNotEmpty) {
              product._images = images;
            }

            // 验证本地产品图片是否存在
            for (var image in product.images) {
              if (image.localFileName != null) {
                final imageFile = File('${imagesDir.path}/${image.localFileName!}');
                if (!await imageFile.exists()) {
                    // debugPrint('Product image file does not exist for image: ${image.localFileName}');
                    image.setLocalFileName(null);
                }
              }
            }
          }
        }

        // debugPrint('Completed loading products from cache.');
        return products;
    } catch (e) {
        debugPrint('Error loading products from cache: $e');
        return [];
    }
  }

  /// 缓存产品列表和图片
  /// 同时缓存图片的翻译结果
  Future<void> saveToCache(List<Product> products) async {
    try {
      Directory imagesDir = ProductImage.localCacheDir;
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }
      for (var product in products) {
        // Cache cover image
        if (product.coverUrl != null) {
          final String coverUrl = product.coverUrl!;
          final String coverFileName = '${product.productId}_cover.jpg';
          final File coverImageFile = File('${imagesDir.path}/$coverFileName');
          product.setLocalCoverFileName(coverFileName);

          if (await coverImageFile.exists()) {
            product.setHeight(await product._getImageHeight(coverImageFile));
          } else {
            debugPrint('Downloading cover image from $coverUrl');
            final response = await http.get(Uri.parse(coverUrl));
            if (response.statusCode == 200) {
              await coverImageFile.writeAsBytes(response.bodyBytes);
              // debugPrint('Cached cover image to ${coverImageFile.path}');
            } else {
              debugPrint('Failed to download cover image: ${response.statusCode}');
            }
          }
        }
        // 缓存图片翻译结果
        await saveImagesToCache(product);
      }

      final file = await _productsCacheFile;
      final productsJson = products.map((p) => p.toCacheMap()).toList();
      await file.writeAsString(jsonEncode(productsJson));
      // debugPrint('Saved products to cache file at ${file.path}');
    } catch (e) {
      debugPrint('Error caching products: $e\nStack trace: ${StackTrace.current}');
    }
  }

  /// 缓存产品图片，包含翻译结果
  /// 由于服务器的list接口不返回翻译结果，单独保存图片部分包括翻译结果
  /// 在Draft建立产品后调用
  Future<void> saveImagesToCache(Product product) async {
    try {
      final imagesDir = Directory('${Util.appPath}/product_images');
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }
      
      for (ProductImage image in product.images) {
        if (image.url != null) {
          final String imageUrl = image.url!;
          final String imageFileName = '${product.productId}_${image.name}';
          final File imageFile = File('${imagesDir.path}/$imageFileName');
          image.setLocalFileName(imageFileName);

          if (!await imageFile.exists()) {
            final response = await http.get(Uri.parse(imageUrl));
            if (response.statusCode == 200) {
              await imageFile.writeAsBytes(response.bodyBytes);
              debugPrint('Cached product image to ${imageFile.path}');
            } else {
              debugPrint('Failed to download product image: ${response.statusCode}');
            }
          }

          // // 对于文件夹的图片 List的时候没有返回翻译内容，需要单独读取
          // if (image.translateResult == null || image.translateResult.isEmpty) {
          //   // 读取图片翻译结果
          //   final productDetail = await query(product.productId!);
          //   if (productDetail != null) {
          //     image.setTranslateResult(productDetail.);
          //   }
          // }
        }
      }

      final file = await _imagesCacheFile(product.productId!);
      final imagesJson = product.images.map((p) => p.toMap()).toList();
      await file.writeAsString(jsonEncode(imagesJson));
      // debugPrint('Successfully saved images to cache file at ${file.path}');
    } catch (e) {
      debugPrint('Error saving images to cache: $e');
    }
  }

  Future<List<ProductImage>> loadImagesFromCache(String productId) async {
    try {
      final file = await _imagesCacheFile(productId);
      if (!await file.exists()) {
        // debugPrint('Cache file does not exist for product: $productId');
        return [];  
      }

      final contents = await file.readAsString();
      final List<dynamic> imagesJson = jsonDecode(contents);
      // debugPrint('Successfully loaded images from cache for product: $productId');
      return imagesJson.map((json) => ProductImage.fromMap(json)).toList();
    } catch (e) {
      debugPrint('Error loading images from cache: $e');
      return [];
    }
  }

  /// 获取已读状态
  Future<bool> isRead(String productId) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'product_read_$productId';
    return prefs.getBool(key) ?? false;
  }

  /// 设置已读状态
  Future<void> setRead(String productId, bool isRead) async {
    final prefs = await SharedPreferences.getInstance();
    final key = 'product_read_$productId';
    await prefs.setBool(key, isRead);
  }

  /// 清空所有缓存
  /// 包括产品列表缓存、图片缓存和翻译结果缓存
  Future<void> clearCache() async {
    try {
      // 1. 删除产品列表缓存文件
      final productsFile = await _productsCacheFile;
      if (await productsFile.exists()) {
        await productsFile.delete();
        // debugPrint('Deleted products cache file');
      }

      // 2. 删除并重建图片缓存目录
      final imagesDir = ProductImage.localCacheDir;
      if (await imagesDir.exists()) {
        await imagesDir.delete(recursive: true);
        // debugPrint('Deleted images directory');
      }
      await imagesDir.create(recursive: true);
      // debugPrint('Created new images directory');

    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }
} 