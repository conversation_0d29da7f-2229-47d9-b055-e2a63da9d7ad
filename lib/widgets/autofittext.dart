import 'dart:math';
import 'package:flutter/material.dart';

class AutoFitText extends StatefulWidget {
  final String text;
  final Size size;
  final TextStyle? style;
  final TextAlign textAlign;
  final Widget Function(BuildContext, TextStyle?)? builder;
  
  const AutoFitText({
    required this.text,
    required this.size,
    this.style,
    this.textAlign = TextAlign.left,
    this.builder,
    super.key,
  });

  @override
  State<AutoFitText> createState() => _AutoFitTextState();
}

class _AutoFitTextState extends State<AutoFitText> {
  static const double defaultFontSize = 50.0;
  static const double minStep = 0.5;
  
  static final Map<String, double> _fontSizeCache = {};
  double _lastWidth = 0;
  double _lastHeight = 0;
  TextStyle _textStyle = const TextStyle(
    fontWeight: FontWeight.bold,
    color: Colors.black,
  );

  @override
  void initState() {
    super.initState();
    _textStyle = widget.style ?? const TextStyle(
      fontWeight: FontWeight.bold,
      color: Colors.black,
    );
    _lastWidth = widget.size.width;
    _lastHeight = widget.size.height;
  }

  double _calculateFontSize() {
    // 1. 检查尺寸是否变化
    if (_lastWidth == widget.size.width && 
        _lastHeight == widget.size.height) {
      // 2. 使用缓存键
      String cacheKey = '${widget.text}_${widget.size.width}_${widget.size.height}';
      if (_fontSizeCache.containsKey(cacheKey)) {
        return _fontSizeCache[cacheKey]!;
      }
      
      // 3. 计算并缓存结果
      double size = _binarySearchFontSize();
      _fontSizeCache[cacheKey] = size;
      return size;
    }

    // 4. 更新尺寸记录
    _lastWidth = widget.size.width;
    _lastHeight = widget.size.height;
    
    // 5. 优化初始值
    double estimatedSize = min(
      widget.size.height,
      widget.size.width / (widget.text.length * 0.6)
    );
    double maxSize = min(defaultFontSize, estimatedSize);
    
    return _binarySearchFontSize(maxSize: maxSize);
  }

  // 分离二分查找逻辑
  double _binarySearchFontSize({double maxSize = defaultFontSize}) {
    double minSize = 0;
    double optimalSize = maxSize;
    
    // 循环条件
    int maxIterations = 10;
    int iterations = 0;
    
    while ((maxSize - minSize) > minStep && iterations < maxIterations) {
      iterations++;
      double midSize = (minSize + maxSize) / 2;
      
      if (_doesTextFit(midSize)) {
        optimalSize = midSize;
        minSize = midSize;
      } else {
        maxSize = midSize;
      }
    }
    
    return optimalSize;
  }

  bool _doesTextFit(double fontSize) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: widget.text,
        style: _textStyle.copyWith(fontSize: fontSize),
      ),
      textDirection: TextDirection.ltr,
      textAlign: widget.textAlign,
    );

    textPainter.layout(maxWidth: widget.size.width);
    return textPainter.width <= widget.size.width && 
           textPainter.height <= widget.size.height;
  }

  @override
  void dispose() {
    // 清理缓存
    if (_fontSizeCache.length > 100) {
      _fontSizeCache.clear();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.text.isEmpty) return const SizedBox.shrink();

    return LayoutBuilder(
      builder: (context, constraints) {
        final fontSize = _calculateFontSize();
        final style = _textStyle.copyWith(fontSize: fontSize);
        
        return widget.builder?.call(context, style) ??
          Text(
            widget.text,
            style: style,
            textAlign: widget.textAlign,
          );
      },
    );
  }
}

