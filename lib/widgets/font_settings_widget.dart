import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/text_overlay_settings.dart';

/// 字体设置组件
/// 
/// 包含字体族选择器和字体权重滑块的组合组件
class FontSettingsWidget extends StatelessWidget {
  final TextOverlaySettings settings;
  final VoidCallback? onChanged;

  const FontSettingsWidget({
    Key? key,
    required this.settings,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Font family selector (takes 50% of width)
        Expanded(
          flex: 1,
          child: Container(
            margin: const EdgeInsets.only(left: 16),
            child: _buildFontFamilySelector(),
          ),
        ),
        const SizedBox(width: 8),
        // Font weight slider (takes 50% of width)
        Expanded(
          flex: 1,
          child: _buildFontWeightSlider(),
        ),
      ],
    );
  }

  /// 构建字体族选择器
  Widget _buildFontFamilySelector() {
    final fontMap = {
      // Poppins font (weight controlled by slider)
      'Poppins-Regular': 'Poppins',

      // Comic and manga style fonts
      'Comic Neue': 'Comic Neue',
      'Bangers': 'Bangers',
      'Anime Ace': 'Anime Ace',
      'Komika Title': 'Komika Title',
      'Manga Temple': 'Manga Temple',

      // Modern fonts
      'Inter': 'Inter',
      'M PLUS Rounded 1c': 'M PLUS Rounded',
      'Dela Gothic One': 'Dela Gothic One',

      // Traditional fonts
      'Shippori Mincho': 'Shippori Mincho',
      'Roboto Condensed': 'Roboto Condensed',

      // System fonts
      'Arial': 'Arial',
      'Helvetica': 'Helvetica',
      'Roboto': 'Roboto',
    };

    // Ensure the current font family is in the list, if not, add it
    String currentFont = settings.fontFamily;
    if (!fontMap.containsKey(currentFont)) {
      fontMap[currentFont] = currentFont;
    }

    return Container(
      width: double.infinity,
      height: 34,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
      decoration: BoxDecoration(
        color: ThemeManager.currentTheme.slideBarInactiveColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: currentFont,
          dropdownColor: ThemeManager.currentTheme.backgroundColor,
          isDense: true,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 13,
            fontFamily: currentFont,
          ),
          itemHeight: 48,
          items: fontMap.entries.map((entry) {
            return DropdownMenuItem<String>(
              value: entry.key,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2),
                alignment: Alignment.centerLeft,
                child: Text(
                  entry.value,
                  style: TextStyle(
                    color: ThemeManager.currentTheme.textColor,
                    fontSize: 12,
                    fontFamily: _getFontFamilyForDisplay(entry.key),
                  ),
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              settings.setFontFamily(newValue);
              onChanged?.call();
            }
          },
        ),
      ),
    );
  }

  /// 构建字体权重滑块
  Widget _buildFontWeightSlider() {
    return _buildCustomSlider(
      label: '',
      value: settings.fontWeight.value.toDouble(),
      min: 100.0,
      max: 900.0,
      divisions: 8,
      onChanged: (value) {
        final weightValue = value.round();
        FontWeight newWeight;
        if (weightValue <= 200) {
          newWeight = FontWeight.w100;
        } else if (weightValue <= 300) {
          newWeight = FontWeight.w200;
        } else if (weightValue <= 400) {
          newWeight = FontWeight.w300;
        } else if (weightValue <= 500) {
          newWeight = FontWeight.w400;
        } else if (weightValue <= 600) {
          newWeight = FontWeight.w500;
        } else if (weightValue <= 700) {
          newWeight = FontWeight.w600;
        } else if (weightValue <= 800) {
          newWeight = FontWeight.w700;
        } else if (weightValue <= 900) {
          newWeight = FontWeight.w800;
        } else {
          newWeight = FontWeight.w900;
        }
        settings.setFontWeight(newWeight);
        onChanged?.call();
      },
      displayValue: settings.fontWeight.value.toString(),
      isFontWeightSlider: true, // 标记为字体粗细滑块
      showLeftLabel: false,
    );
  }

  /// 获取正确的字体族名称用于显示
  String _getFontFamilyForDisplay(String fontKey) {
    switch (fontKey) {
      case 'Poppins-Regular':
        return 'Poppins-Regular';
      case 'Comic Neue':
        return 'Comic Neue';
      case 'Bangers':
        return 'Bangers';
      case 'Anime Ace':
        return 'Anime Ace';
      case 'Komika Title':
        return 'Komika Title';
      case 'Manga Temple':
        return 'Manga Temple';
      case 'Inter':
        return 'Inter';
      case 'M PLUS Rounded 1c':
        return 'M PLUS Rounded 1c';
      case 'Dela Gothic One':
        return 'Dela Gothic One';
      case 'Shippori Mincho':
        return 'Shippori Mincho';
      case 'Roboto Condensed':
        return 'Roboto Condensed';
      default:
        // For system fonts, return as-is
        return fontKey;
    }
  }

  /// 构建自定义滑块
  Widget _buildCustomSlider({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
    required String displayValue,
    String? trackLabel,
    bool isFontWeightSlider = false, // 标记是否为字体粗细滑块
    bool showLeftLabel = true,
  }) {
    return Builder(
      builder: (BuildContext context) {
        return Row(
          children: [
            // Left label (only show if showLeftLabel is true)
            if (showLeftLabel) ...[
              SizedBox(
                width: 60,
                child: Text(
                  label,
                  style: TextStyle(
                    color: ThemeManager.currentTheme.textColor,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
            ],
            // Slider with track label
            Expanded(
              child: Stack(
                children: [
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                  trackHeight: 32, // Match original styling
                  thumbShape: CustomSliderThumbShape(
                    enabledThumbRadius: 16, // Match original styling
                    displayValue: displayValue,
                    textColor: Colors.black, // Match original styling
                  ),
                  overlayShape: SliderComponentShape.noOverlay, // Match original styling
                  activeTrackColor: ThemeManager.currentTheme.slideBarActiveColor, // Match original styling
                  inactiveTrackColor: ThemeManager.currentTheme.slideBarInactiveColor, // Match original styling
                  thumbColor: Colors.white, // Match original styling
                ),
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  divisions: divisions,
                  onChanged: (newValue) => onChanged(newValue),
                ),
              ),
              // Track label positioned on the right side (for non-font-weight sliders)
              if (trackLabel != null && trackLabel.isNotEmpty && !isFontWeightSlider)
                Positioned(
                  right: 8,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      trackLabel,
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              // Font weight indicators inside track (for font weight slider)
              if (isFontWeightSlider) ...[
                // Light "B" on the left
                Positioned(
                  left: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      'B',
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                        fontSize: 14,
                        fontWeight: FontWeight.w300, // Light weight
                      ),
                    ),
                  ),
                ),
                // Bold "B" on the right
                Positioned(
                  right: 24,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      'B',
                      style: TextStyle(
                        color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                        fontSize: 16,
                        fontWeight: FontWeight.w900, // Bold weight
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
      },
    );
  }
}

/// 自定义滑块拇指形状，在拇指中心显示数值
class CustomSliderThumbShape extends SliderComponentShape {
  final double enabledThumbRadius;
  final String displayValue;
  final Color textColor;

  const CustomSliderThumbShape({
    required this.enabledThumbRadius,
    required this.displayValue,
    required this.textColor,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(enabledThumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // Draw thumb circle with enhanced shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.25)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    canvas.drawCircle(center + const Offset(0, 2), enabledThumbRadius, shadowPaint);

    // Draw thumb circle
    final paint = Paint()
      ..color = sliderTheme.thumbColor ?? Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, enabledThumbRadius, paint);

    // Draw border (match original styling)
    final borderPaint = Paint()
      ..color = Colors.grey.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawCircle(center, enabledThumbRadius, borderPaint);

    // Draw text (match original styling)
    final textSpan = TextSpan(
      text: displayValue,
      style: TextStyle(
        color: textColor,
        fontSize: 10,
        fontWeight: FontWeight.w600, // Match original styling
      ),
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: textDirection,
    );

    textPainter.layout();

    final textOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );

    textPainter.paint(canvas, textOffset);
  }
}
