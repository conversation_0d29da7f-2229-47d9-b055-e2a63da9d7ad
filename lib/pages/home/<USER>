import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../models/product_model.dart';
import 'package:imtrans/util/util.dart';
import '../../controllers/product_controller.dart';
import 'package:imtrans/util/theme_manager.dart';

class ImageCatalog extends StatefulWidget {
  final dynamic dart;
  final List<Product> list;
  final int currentIndex;
  final ProductController productController;

  const ImageCatalog({
    required this.dart,
    required this.list,
    required this.currentIndex,
    required this.productController,
    super.key
  });

  @override
  State<ImageCatalog> createState() => _ImageCatalogState();
}

class _ImageCatalogState extends State<ImageCatalog> with TickerProviderStateMixin {
  late final AnimationController _inController;
  final _scrollController=ScrollController();
  late Animation<Offset> _animation;
  int _page=1;
  final int _pageNum=21;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      if(_scrollController.position.pixels==_scrollController.position.maxScrollExtent){
        if(mounted){
          nextPage();
        }
      }
    });
    _inController=AnimationController(vsync: this, duration: const Duration(milliseconds: 200));
    _animation= Tween<Offset>(
      begin: const Offset(0,1),
      end: const Offset(0,0),
    ).animate(_inController);

    _inController.forward();
  }

  @override
  void dispose(){
    Util.popNull(widget);
    _scrollController.dispose();
    _inController.dispose();
    super.dispose();
  }

  nextPage(){
    setState(() {
      _page++;
    });
  }

  void closeView(){
    _inController.reverse().then((value){
      widget.dart.showCatalog(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    
    if (widget.currentIndex < 0 || widget.currentIndex >= widget.list.length) {
      debugPrint('Invalid currentIndex in ProductCatalog: ${widget.currentIndex}');
      return Container();
    }
    
    return GestureDetector(
      onTap: () => closeView(),
      child: Material(
        color: Colors.transparent,
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 半透明背景层
            AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: 1.0,
              child: Container(
                color: ThemeManager.currentTheme.blackOverlayColor,
              ),
            ),
            // 目录内容层
            Align(
              alignment: Alignment.bottomCenter,
              child: GestureDetector(
                onTap: () {},  // 防止点击穿透
                child: SlideTransition(
                  position: _animation,
                  child: SizedBox(
                    width: size.width,
                    height: size.height * 0.67,
                    child: Container(
                      decoration: BoxDecoration(
                        color: ThemeManager.currentTheme.bottomBarBackgroundColor,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(10),
                          topRight: Radius.circular(10),
                        ),
                        boxShadow: [
                          BoxShadow(blurRadius: 20, color: ThemeManager.currentTheme.shadowColor)
                        ]
                      ),
                      child: Column(
                        children: [
                          // 头部
                          Container(
                            height: 60,
                            alignment: Alignment.center,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: size.width * 0.8,
                                  alignment: Alignment.centerLeft,
                                  padding: const EdgeInsets.only(left: 20, right: 20),
                                ),
                                Container(
                                  width: size.width * 0.2,
                                  padding: const EdgeInsets.only(right: 10),
                                  alignment: Alignment.centerRight,
                                  child: IconButton(
                                    onPressed: () => closeView(),
                                    icon: Icon(
                                      Icons.close,
                                      size: 26,
                                      color: ThemeManager.currentTheme.closeColor,
                                    )
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // 内容区域
                          Expanded(
                            child: Container(
                              width: size.width,
                              padding: const EdgeInsets.symmetric(horizontal: 20),
                              child: SingleChildScrollView(
                                controller: _scrollController,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Wrap(
                                      spacing: 15,
                                      runSpacing: 10,
                                      // alignment: WrapAlignment.start,
                                      children: createListView(size),
                                    ),
                                    const SizedBox(height: 10),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static createImage(Product item,imageWidth,imageHeight){
    BoxFit fit=BoxFit.cover;
    var image=CachedNetworkImage(
      imageUrl: item.coverUrl!,
      width: imageWidth,
      height: imageHeight,
      fit: fit,
    );
    return SizedBox(
      width: imageWidth,
      height: imageHeight,
      child: image,
    );
  }

  createListView(size){
    if (widget.list.isEmpty) {
      return [Container()];
    }

    double imageWidth = (size.width-70)/3;
    double imageHeight = imageWidth*1.44;
    List<Widget> widgets = [];

    int end = _page * _pageNum;
    end = end > widget.list.length ? widget.list.length : end;
    
    if (end <= 0) {
      return [Container()];
    }

    List<Product> list = widget.list.sublist(0, end);
    for(var i=0;i<list.length;i++){
      Product product = list[i];
      if(product.images.length == 1){
        var item=list[i];
        var image=Container(
          width:imageWidth,
          height: imageHeight,
          clipBehavior: Clip.hardEdge,
          decoration: BoxDecoration(
            color: ThemeManager.currentTheme.itemBorderColor,
            borderRadius: BorderRadius.circular(10),
            border: widget.currentIndex==i?Border.all(color: const Color(0xffcdee2d),width: 2):Border.all(color: const Color.fromARGB(255, 197, 199, 202),width: 1),
            boxShadow: [
              BoxShadow(
                  color: ThemeManager.currentTheme.itemBorderColor,
                  // blurRadius: 3,
                  blurStyle: BlurStyle.normal
              ),
            ]
          ),
          alignment: Alignment.topCenter,
          child: Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.none,
            children: [
              Container(
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: createImage(item,imageWidth,imageHeight),
              ),
              // createItemDetail(item,imageWidth+(widget.currentIndex==i?0:4),imageHeight/2),
            ],
          ),
        );
        Widget con = GestureDetector(
          onTapUp: (e){
            closeView();
            widget.dart.selectIndex(i);
          },
          child: image,
        );
        widgets.add(con);
      }
    }
    // widgets.add(const SizedBox(height: 10,));
    return widgets;
  }
}
