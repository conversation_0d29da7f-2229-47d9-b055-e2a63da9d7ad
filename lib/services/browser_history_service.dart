import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/services/bookmark_service.dart';

/// Browser history item model
class BrowserHistoryItem {
  final String url;
  final String title;
  final DateTime timestamp;
  final String? favicon;
  final bool faviconCached; // 标记favicon是否已缓存处理

  BrowserHistoryItem({
    required this.url,
    required this.title,
    required this.timestamp,
    this.favicon,
    this.faviconCached = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'title': title,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'favicon': favicon,
      'faviconCached': faviconCached,
    };
  }

  factory BrowserHistoryItem.fromJson(Map<String, dynamic> json) {
    return BrowserHistoryItem(
      url: json['url'] ?? '',
      title: json['title'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] ?? 0),
      favicon: json['favicon'],
      faviconCached: json['faviconCached'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BrowserHistoryItem && other.url == url;
  }

  @override
  int get hashCode => url.hashCode;

  /// 创建副本并更新favicon信息
  BrowserHistoryItem copyWithFavicon({String? favicon, bool? faviconCached}) {
    return BrowserHistoryItem(
      url: url,
      title: title,
      timestamp: timestamp,
      favicon: favicon ?? this.favicon,
      faviconCached: faviconCached ?? this.faviconCached,
    );
  }
}

/// Service for managing browser history
class BrowserHistoryService {
  static const String _historyKey = 'browser_history';
  static const int _maxHistoryItems = 20; // Maximum number of history items to keep

  /// Add a new history item with favicon support
  static Future<void> addHistoryItem({
    required String url,
    required String title,
    String? favicon,
    bool enableFaviconFetch = true,
  }) async {
    try {
      // Skip empty URLs or common non-history URLs
      if (url.isEmpty || 
          url == 'about:blank' || 
          url.startsWith('data:') ||
          url.startsWith('javascript:')) {
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historyKey) ?? '[]';
      final List<dynamic> historyList = jsonDecode(historyJson);
      
      // Convert to BrowserHistoryItem objects
      final List<BrowserHistoryItem> history = historyList
          .map((item) => BrowserHistoryItem.fromJson(item))
          .toList();

      // Remove existing entry with same URL if it exists
      history.removeWhere((item) => item.url == url);

      // Create new item
      var newItem = BrowserHistoryItem(
        url: url,
        title: title.isNotEmpty ? title : url,
        timestamp: DateTime.now(),
        favicon: favicon,
        faviconCached: false,
      );

      // Try to fetch favicon if not provided and fetch is enabled
      if (enableFaviconFetch && (favicon == null || favicon.isEmpty)) {
        debugPrint('尝试为历史记录获取favicon: $url');
        try {
          final fetchedFavicon = await BookmarkService.instance.fetchFavicon(url);
          if (fetchedFavicon != null) {
            newItem = newItem.copyWithFavicon(
              favicon: fetchedFavicon,
              faviconCached: true,
            );
            debugPrint('成功为历史记录获取favicon: $title');
          } else {
            newItem = newItem.copyWithFavicon(faviconCached: true);
            debugPrint('无法获取历史记录favicon，已标记: $title');
          }
        } catch (e) {
          debugPrint('获取历史记录favicon异常: $e');
          newItem = newItem.copyWithFavicon(faviconCached: true);
        }
      }

      // Add new item at the beginning (most recent first)
      history.insert(0, newItem);

      // Keep only the most recent items
      if (history.length > _maxHistoryItems) {
        history.removeRange(_maxHistoryItems, history.length);
      }

      // Save back to SharedPreferences
      final updatedJson = jsonEncode(history.map((item) => item.toJson()).toList());
      await prefs.setString(_historyKey, updatedJson);

      debugPrint('Added to history: $title ($url)');
    } catch (e) {
      debugPrint('Failed to add history item: $e');
    }
  }

  /// Get all history items
  static Future<List<BrowserHistoryItem>> getHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historyKey) ?? '[]';
      final List<dynamic> historyList = jsonDecode(historyJson);
      
      return historyList
          .map((item) => BrowserHistoryItem.fromJson(item))
          .toList();
    } catch (e) {
      debugPrint('Failed to load history: $e');
      return [];
    }
  }

  /// Delete a specific history item
  static Future<void> deleteHistoryItem(BrowserHistoryItem item) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historyKey) ?? '[]';
      final List<dynamic> historyList = jsonDecode(historyJson);
      
      // Convert to BrowserHistoryItem objects
      final List<BrowserHistoryItem> history = historyList
          .map((historyItem) => BrowserHistoryItem.fromJson(historyItem))
          .toList();

      // Remove the item
      history.removeWhere((historyItem) => historyItem.url == item.url);

      // Save back to SharedPreferences
      final updatedJson = jsonEncode(history.map((historyItem) => historyItem.toJson()).toList());
      await prefs.setString(_historyKey, updatedJson);

      debugPrint('Deleted from history: ${item.title} (${item.url})');
    } catch (e) {
      debugPrint('Failed to delete history item: $e');
    }
  }

  /// Clear all history
  static Future<void> clearHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_historyKey);
      debugPrint('Cleared all browser history');
    } catch (e) {
      debugPrint('Failed to clear history: $e');
    }
  }

  /// Get recent history items (limited number)
  static Future<List<BrowserHistoryItem>> getRecentHistory({int limit = 10}) async {
    final history = await getHistory();
    return history.take(limit).toList();
  }
}
