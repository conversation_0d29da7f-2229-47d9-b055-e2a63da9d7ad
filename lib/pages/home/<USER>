import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/widgets/text_overlay.dart';
import '../../util/config.dart';
import '../../util/util.dart';
import '../../models/product_model.dart';
import '../../controllers/product_controller.dart';
import '../../util/loading_manager.dart';
import '../../util/theme_manager.dart';
import '../../services/image_save_service.dart';
import 'folder_catalog.dart';
import 'package:imtrans/widgets/common/common_widgets.dart';

/// 包含多个图片文件的产品的查看方法
/// 例如Zip文件上传后产生的产品
class FolderViewer extends StatefulWidget {
  final Product product;
  final int currentIndex;
  const FolderViewer({required this.product, required this.currentIndex, super.key});

  @override
  State<FolderViewer> createState() => _FolderViewerState();
}

class _FolderViewerState extends State<FolderViewer> with TickerProviderStateMixin {
  double _width = 0;
  double _height = 0;
  bool _showOrigin = false;
  int _index = 0;
  bool _showAppBar = false;

  late final AnimationController _inController;
  late Animation<Offset> _animation;

  late final AnimationController _inControllerLeft;
  // late Animation<Offset> _animationLeft;

  bool _showCatalog = false;
  dynamic _imageItem;
  Widget? _originImage;
  double _scale = 1.0;
  final ProductController _controller = ProductController();

  @override
  void initState() {
    super.initState();
    // Controller.showZipImage = this;
    _index = widget.currentIndex;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _width = MediaQuery.of(context).size.width;
        _height = MediaQuery.of(context).size.height;
      });
      createResult(widget.product.images[widget.currentIndex]);
    });

    _inController = AnimationController(vsync: this, duration: const Duration(milliseconds: 200));
    _animation = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(0, -1),
    ).animate(_inController);

    _inControllerLeft = AnimationController(vsync: this, duration: const Duration(milliseconds: 200));
    // _animationLeft = Tween<Offset>(
    //   begin: const Offset(0, 0),
    //   end: const Offset(-1, 0),
    // ).animate(_inControllerLeft);

    showBar();

    // _controller.markAsRead(widget.product.productId!);
  }

  @override
  void dispose() {
    // Controller.showZipImage = null;
    Util.popNull(widget);
    _inController.dispose();
    _inControllerLeft.dispose();
    super.dispose();
  }

  createAppBar() {
    List<ProductImage> list = widget.product.images;
    String name = "";
    if (_index >= 0 && _index <= list.length - 1) {
      name = list[_index].name ?? "";
    }
    return SlideTransition(
      position: _animation,
      child: Container(
          height: 60 + MediaQuery.of(context).padding.top, // 使用系统状态栏高度
          padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
          decoration: const BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0x79000000), Color(0x00000000)]),
          ),
          child: Row(
            children: [
              Container(
                width: _width,
                padding: const EdgeInsets.only(left: 10, right: 10),
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        ButtonWidget.createImageButton(ThemeManager.getImagePath('btn_back'), 40, 40,
                            onPressed: () {
                          Util.closeView(widget);
                        }),
                        const SizedBox(width: 10),
                        Material(
                          color: Colors.transparent,
                          child: SizedBox(
                            width: _width * 0.8,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.product.name ?? "",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: ThemeManager.currentTheme.textColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  name,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    color: ThemeManager.currentTheme.textColor,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Row(
                    //   children: [
                    //     Controller.createButton(ThemeManager.getImagePath('icon_download'), 40, 40,
                    //         onPressed: () async {
                    //       LoadingManager.instance.show(context);
                    //       try {
                    //         await Util.saveImg();
                    //       } catch (e) {
                    //         debugPrint('Error saving image: $e');
                    //       } finally {
                    //         LoadingManager.instance.hide(context);
                    //       }
                    //     }),
                    //     const SizedBox(width: 10),
                    //   ],
                    // ),
                  ],
                ),
              ),
            ],
          )),
    );
  }

  createCacheImage(ProductImage item) {
    return CachedNetworkImage(
      imageUrl: item.url!,
      fit: BoxFit.fitWidth,
      width: _width,
      placeholder: (context, url) {
        // if (item.imageData != null) {
        //   return Image.memory(
        //     item.imageData!,
        //     fit: BoxFit.fitWidth,
        //     width: MediaQuery.of(context).size.width
        //   );
        // }
        return Container(); // 或者返回一个加载指示器
      },
    );
  }

  createResult(ProductImage item) async {
    // 如果本地翻译结果不存在，则从网络加载
    if (item.translateResult == null || item.translateResult!.isEmpty) {
      // Add a small delay before showing loading
      await Future.delayed(const Duration(milliseconds: 100));
      LoadingManager.instance.show(context);
      
      try {
        final product = await _controller.query(widget.product.productId!);
        if (product != null) {
          item.setTranslateResult(product.images[_index].translateResult);
        }
      } catch (e) {
        debugPrint('Error loading translation: $e');
      } finally {
        LoadingManager.instance.hide(context);
      }
    }
    
    var caches = {
      "imagePath": item.localFileName != null && item.localFileName!.isNotEmpty ?  item.localFileName! : "",
    };
    
    if (item.url != null && item.translateResult != null) {
      _imageItem = TextOverlay(item.url!, item.translateResult!, caches: caches);
    } else {
      _imageItem = createCacheImage(item);
    }
    _originImage = createCacheImage(item);   
    
    if (mounted) {
      setState(() {
        _scale = 1.0;
      });
    }
  }

  createImage() {
    Container con = Container(
        width: _width,
        constraints: BoxConstraints(minHeight: _height),
        alignment: Alignment.center,
        child: Center(
          child: Listener(
            child: InteractiveViewer(
                transformationController: TransformationController(
                  // 设置初始缩放值
                  Matrix4.identity()..scale(_scale),
                ),
                boundaryMargin: const EdgeInsets.all(0.0),
                minScale: 0.5,
                maxScale: 5,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: _width,
                      constraints: BoxConstraints(
                        minHeight: _height,
                        maxHeight: double.infinity,
                      ),
                      alignment: Alignment.center,
                      //widget.isCover?_imageItem:
                      child: _showOrigin
                          ? _originImage
                          : RepaintBoundary(
                              key: Util.globalKey,
                              child: _imageItem,
                            ),
                      //child: createResult(item),
                    ),
                  ],
                )),
          ),
        ));
    return con;
  }

  showCatalog(bool catalog) {
    setState(() {
      _showCatalog = catalog;
    });
  }

  selectIndex(index) {
    List<ProductImage> list = widget.product.images;
    try {
      setState(() {
        _index = index;
      });
    } catch (e) {
      debugPrint("selectIndex error: $e");
    }
    if (_index < list.length) {
      var item = list[_index];
      createResult(item);
    }
  }

  showBar() {
    _showAppBar = true;
    _inController.reverse();
    _inControllerLeft.reverse();
  }

  hideBar() {
    _inControllerLeft.forward();
    _inController.forward().then((value) {
      _showAppBar = false;
    });
  }

  actionEvent(int offset) {
    List<ProductImage> list = widget.product.images;
    _index = (_index + offset + list.length) % list.length;

    setState(() { });
    
    var nextImage = list[_index];
    createResult(nextImage);
  }

  Widget _buildMainImageView() {
    return Positioned(
      top: 0,
      child: Container(
        width: _width,
        height: _height,
        alignment: Alignment.center,
        child: SingleChildScrollView(
          // 确保每次构建时Hero的tag是唯一的
          child: Hero(
            tag: "folder_image_${widget.product.productId}_$_index",
            child: createImage(),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    double iconW = 26;
    return Positioned(
      left: 0,
      bottom: 0,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0),
          end: const Offset(0, 1), // 向下滑出屏幕
        ).animate(_inControllerLeft),
        child: Material(
          color: ThemeManager.currentTheme.backgroundColor.withValues(alpha: .5),
          child: Container(
            width: _width,
            padding: EdgeInsets.only(
              top: 10, 
              bottom: MediaQuery.of(context).padding.bottom, // 增加底部安全区域的padding
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                /// 目录按钮
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_contents'), iconW, iconW, 
                  onPressed: () => showCatalog(true)),
                /// 上一页按钮
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_prevpage'), iconW, iconW, 
                  onPressed: () => actionEvent(-1)),
                /// 下一页按钮
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_nextpage'), iconW, iconW, 
                  onPressed: () => actionEvent(1)),
                /// 下载按钮 
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_download'), iconW, iconW, 
                  onPressed: () async {
                    LoadingManager.instance.show(context);
                    try {
                      if (_index < 0 || _index >= widget.product.images.length) {
                        return;
                      }

                      if (_showOrigin) {
                        // 显示原图时，保存当前图片的原图
                        await ImageSaveService.instance.saveProductImage(
                          widget.product,
                          saveType: ImageSaveType.original,
                          imageIndex: _index,
                          context: context,
                        );
                      } else {
                        // 显示翻译图时，通过RepaintBoundary保存当前显示内容
                        // 使用更高的像素比例以提高图片质量
                        await ImageSaveService.instance.saveCurrentDisplayImage(
                          Util.globalKey,
                          context: context,
                          pixelRatio: 5.0, // 提高像素比例获得更高质量
                        );
                      }
                    } catch (e) {
                      debugPrint('Error saving image: $e');
                    } finally {
                      LoadingManager.instance.hide(context);
                    }
                  }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCatalogView() {
    if (!_showCatalog) return Container();
    
    return Positioned(
      left: 0,
      bottom: 0,
      child: FolderCatalog(
        productName: widget.product.name ?? "",
        dart: this,
        list: widget.product.images,
        currentIndex: _index,
      ),
    );
  }

  createContent(iconW) {
    return Container(
      width: _width,
      height: _height,
      alignment: Alignment.center,
      child: Stack(
        children: [
          _buildMainImageView(),
          if (_showAppBar) Positioned(top: 0, child: createAppBar()),
          _buildBottomControls(),
          _buildCatalogView(),
          // 添加悬浮的查看原图按钮，仅在不显示目录时显示
          if (!_showCatalog) Positioned(
            right: 25,
            bottom: 125, // 位于底部按钮栏上方
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _showOrigin = !_showOrigin;
                });
              },
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Color(0xffd9d9d9),
                    width: 1,
                  ),
                  shape: BoxShape.circle,
                  color: _showOrigin ?
                    ThemeManager.currentTheme.logoColor
                    : Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: .25),
                      // spreadRadius: 1,
                      blurRadius: 15,
                    ),
                  ],
                ),
                child: Center(
                  child: Image.asset(
                    _showOrigin
                        ? ThemeManager.getImagePath('icon_show_result')
                        : ThemeManager.getImagePath('icon_show_orginal'),
                    width: 20,
                    height: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    try {
      var size = MediaQuery.of(context).size;
      _width = size.width;
      _height = size.height;
      double iconW = 70;
      return Scaffold(
        backgroundColor: ThemeManager.currentTheme.backgroundColor,
        body: MaterialApp(
          color: Colors.transparent,
          theme: ThemeData(fontFamily: Config.fontFamily),
          home: GestureDetector(
            onTapUp: (e) {
              setState(() {
                if (!_showAppBar) {
                  showBar();
                } else {
                  hideBar();
                }
              });
            },
            child: createContent(iconW),
          ),
        ),
      );
    } catch (e) {
      debugPrint('Error in build: $e');
      return Scaffold(
        body: Center(
          child: Text(AppLocalizations.of(context)!.errorLoadingImage,),
        ),
      );
    }
  }
}
