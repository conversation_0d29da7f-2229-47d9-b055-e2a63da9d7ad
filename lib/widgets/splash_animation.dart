import 'package:flutter/material.dart';

class SplashAnimation extends StatefulWidget {
  final List<String> images;
  final Function() onAnimationFinish;
  final int endIndex;

  const SplashAnimation({
    required this.images,
    required this.endIndex,
    required this.onAnimationFinish, 
    super.key
  });

  @override
  State<SplashAnimation> createState() => _SplashAnimationState();
}

class _SplashAnimationState extends State<SplashAnimation> {

  int _index=0;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 50),(){
      animationEvent();
    });
  }

  animationEvent(){
    int per=80;
    if(_index==widget.endIndex){
      widget.onAnimationFinish();
    }else{
      setState(() {
        _index=(_index+1)%widget.images.length;
      });
      if(_index==4||_index==0){
        per=1500;
      }else if(_index>8){
        per=20;
      }else{
        per=80;
      }
      Future.delayed(Duration(milliseconds: per),(){
        animationEvent();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var size =MediaQuery.of(context).size;
    int count=widget.images.length;
    List<Widget> images=[];
    // 把所有图片都加载进内容，否则每一帧加载时会卡顿
    for (int i = 0; i < count; ++i) {
      images.add(Image.asset(
        widget.images[i],
        width: 0,
        height: 0,
      ));
    }

    images.insert(count,Image.asset(
      widget.images[_index],
      width: size.width,
      height: size.height,
      fit: BoxFit.fitWidth,
    ));


    return Container(
      color: Colors.white,
      child: Stack(alignment: AlignmentDirectional.center, children: images),
    );
  }
}
