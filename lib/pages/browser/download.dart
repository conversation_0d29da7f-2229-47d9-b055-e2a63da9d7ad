import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:imtrans/controllers/draft_controller.dart';
import 'package:imtrans/pages/home/<USER>';
import 'package:imtrans/services/event_log.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/util.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/widgets/toast_widget.dart';

/// Web Image Download Selection Page
/// Displays a list of images scraped from the web, allowing users to select images to download
class BrowserDownloadPage extends StatefulWidget {
  final List<String> imageUrls; // Modified to URL list instead of image data
  final List<Uint8List>? preloadedImages; // 预加载的图片数据
  final String? cookies; // Cookie字符串
  final String? referer; // 引用页面URL
  
  const BrowserDownloadPage({
    Key? key, 
    required this.imageUrls,
    this.preloadedImages,
    this.cookies,
    this.referer,
  }) : super(key: key);

  @override
  State<BrowserDownloadPage> createState() => _BrowserDownloadPageState();
}

class _BrowserDownloadPageState extends State<BrowserDownloadPage> {
  // Collection of selected image indices
  final Set<int> _selectedIndices = {};
  bool _isProcessing = false;
  bool _isLoading = false; // 初始设置为加载状态
  bool _isDownloading = false; // Whether images are being downloaded
  List<String> _imageUrls = []; // Store image URLs
  List<Uint8List?> _preloadedImages = []; // 预加载的图片数据，用于预览
  int _loadedCount = 0;
  int _totalCount = 0;

  @override
  void initState() {
    super.initState();
    _imageUrls = widget.imageUrls;
    _totalCount = _imageUrls.length;
    _preloadedImages = List.filled(_totalCount, null);
    
    // 如果有预加载的图片数据，直接使用
    if (widget.preloadedImages != null) {
      for (int i = 0; i < widget.preloadedImages!.length && i < _totalCount; i++) {
        _preloadedImages[i] = widget.preloadedImages![i];
      }
      _loadedCount = widget.preloadedImages!.length;
      _isLoading = false;
    }
    
    // 默认全选所有图片
    for (int i = 0; i < _totalCount; i++) {
      _selectedIndices.add(i);
    }
    
    // 如果没有预加载的图片数据，开始预加载
    // if (widget.preloadedImages == null) {
    //   _startPreloading();
    // }
  }
  
  /// Download selected images
  Future<List<Uint8List>> _downloadSelectedImages() async {
    setState(() {
      _isDownloading = true;
      _loadedCount = 0;
    });
    
    List<Uint8List> selectedImages = [];
    final Map<String, dynamic> logData = {
      'image_count': _selectedIndices.length,
      'image_urls': _selectedIndices.map((index) => _imageUrls[index]).toList(),
      'timestamp': DateTime.now(),
      'success_count': 0,
      'failure_urls': <String>[], 
    };
    for (int index in _selectedIndices) {
      if (!mounted) break;

      try {
        // 如果已经预加载了图片数据，直接使用
        if (_preloadedImages[index] != null) {
          selectedImages.add(_preloadedImages[index]!);
          // setState(() {
            _loadedCount++;
            logData['success_count'] = _loadedCount;
            // 
          // });
          continue;
        }
        
        // 否则重新下载
        final url = _imageUrls[index];
        final imageBytes = await _fetchImageHttp(url);
        selectedImages.add(imageBytes);
        if (imageBytes.isEmpty) {
          logData['failure_urls'].add(url);
        }

      } catch (e) {
        debugPrint("图片下载失败: ${_imageUrls[index]} - $e");
        // 添加空数据
        selectedImages.add(Uint8List(0));
        logData['failure_urls'].add(_imageUrls[index]); 
      }
      
      if (mounted) {
        // setState(() {
          _loadedCount++;
        // });
      }
    }
    
    if (mounted) {
      setState(() {
        _isDownloading = false;
      });
    }
    // log
    EventLogService.logMangaDownload(logData);
    // 过滤掉空数据
    return selectedImages.where((data) => data.isNotEmpty).toList();
  }

  // Toggle image selection status
  void _toggleSelection(int index) {
    setState(() {
      if (_selectedIndices.contains(index)) {
        _selectedIndices.remove(index);
      } else {
        _selectedIndices.add(index);
      }
    });
  }

  // Select all/deselect all
  void _toggleSelectAll() {
    setState(() {
      if (_selectedIndices.length == _imageUrls.length) {
        // If all selected, deselect all
        _selectedIndices.clear();
      } else {
        // Otherwise select all
        _selectedIndices.clear();
        for (int i = 0; i < _imageUrls.length; i++) {
          _selectedIndices.add(i);
        }
      }
    });
  }

  // 确认下载选中的图片
  Future<void> _confirmDownload() async {
    if (_selectedIndices.isEmpty) {
      ToastWidget.show(AppLocalizations.of(context)!.selectAtLeastOneImage);
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // Download selected images
      final selectedImages = await _downloadSelectedImages();
      
      if (selectedImages.isEmpty) {
        if (mounted) {
          ToastWidget.show(AppLocalizations.of(context)!.noValidImagesToDownload);
          setState(() {
            _isProcessing = false;
          });
        }
        return;
      }
      
      if (!mounted) return;
      
      // Use DraftController to process selected images
      final draftController = DraftController();
      // Wait for DraftController initialization to complete
      await draftController.initialized;
      await draftController.handleSelection(selectedImages);
      
      if (!mounted) return;
      
      // Navigate to draft page
      Util.navigatorPush(const DraftView());
    } catch (e) {
      if (!mounted) return;
      ToastWidget.show(AppLocalizations.of(context)!.failedToProcessImages(e.toString()));
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: ThemeManager.currentTheme.backgroundColor,
        iconTheme: IconThemeData(color: ThemeManager.currentTheme.textColor),
        automaticallyImplyLeading: false,
        // 添加自定义的返回按钮
        leading: IconButton(
          icon: Image.asset(ThemeManager.getImagePath("btn_back"), width: 40, height: 40),
          onPressed: () {
            Navigator.of(context).pop();
          },
          padding: const EdgeInsets.only(left: 10),
        ),
        // 选中数量文本
        title: Text(
          _isLoading 
            ? AppLocalizations.of(context)!.loading + "$_loadedCount/$_totalCount" 
            : _isDownloading 
              ? AppLocalizations.of(context)!.downloading + "$_loadedCount/${_selectedIndices.length}" 
              : AppLocalizations.of(context)!.selectedCountWithTotal(_selectedIndices.length, _totalCount),
          style: TextStyle(color: ThemeManager.currentTheme.textColor, fontSize: 16),
        ),
        centerTitle: true,
        actions: [
          // Select all/deselect all按钮
          Row(
            children: [
              GestureDetector(
                onTap: () => _toggleSelectAll(),
                child: Container(
                  width: 26,
                  height: 26,
                  margin: const EdgeInsets.only(right: 16),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFF1B1C1E),
                      width: 1,
                    ),
                    color: _selectedIndices.length == _imageUrls.length && _imageUrls.isNotEmpty 
                        ? ThemeManager.currentTheme.selectedItemColor 
                        : Colors.white,
                  ),
                  child: _selectedIndices.length == _imageUrls.length && _imageUrls.isNotEmpty
                    ? const Icon(Icons.check, color: Color(0xFF1B1C1E), size: 16)
                    : null,
                ),
              ),
            ],
          ),
        ],
      ),

      body: _imageUrls.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(ThemeManager.getImagePath("nodata"), width: 100, height: 100),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.noImagesFound,
                    style: TextStyle(color: ThemeManager.currentTheme.textColor, fontSize: 16),
                  ),
                ],
              ),
            )
          : _isLoading && _loadedCount == 0
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        AppLocalizations.of(context)!.loadingImages,
                        style: TextStyle(color: ThemeManager.currentTheme.textColor, fontSize: 16),
                      ),
                    ],
                  ),
                )
              : GridView.builder(
                  padding: const EdgeInsets.all(8),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: _imageUrls.length,
                  itemBuilder: (context, index) {
                    final bool isSelected = _selectedIndices.contains(index);
                    
                    return GestureDetector(
                      onTap: () => _toggleSelection(index),
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          // 图片容器
                          Container(
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: .08),
                                  offset: const Offset(0, 4),
                                  blurRadius: 10,
                                ),
                              ],
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.white, // 添加背景色以便阴影效果更明显
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: _buildImageWidget(index),
                            ),
                          ),
                          // 选中状态指示器
                          if (isSelected)
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: ThemeManager.currentTheme.logoColor,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: const Color(0xff1b1c1e), width: 1), 
                                ),
                                child: Icon(
                                  Icons.check,
                                  color: const Color(0xFF1b1c1e),
                                  size: 12,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),

      // 底部导航栏
      bottomNavigationBar: Container(
        padding: const EdgeInsets.only(top: 8),
        decoration: BoxDecoration(
          color: ThemeManager.currentTheme.backgroundColor,
        ),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center, // 居中对齐
            children: [
              // 将Expanded替换为固定宽度的按钮
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.8, 
                child: ElevatedButton(
                  onPressed: _isProcessing ? null : _confirmDownload,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFCDEE2D), // 荧光绿色
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16), 
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: const BorderSide(color: Colors.black, width: 1), 
                    ),
                  ),
                  child: _isProcessing
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: ThemeManager.currentTheme.textColor,
                              ),
                            ),
                          ],
                        )
                      :  Text(
                          AppLocalizations.of(context)!.ok,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // 构建图片显示组件
  Widget _buildImageWidget(int index) {
    // 检查URL是否来自需要特殊处理的网站
    final url = _imageUrls[index];
    final needsWebView = url.contains('mangadex.org') || url.contains('pixiv') || url.contains('fanfox');
    debugPrint("构建图片组件: $url, 需要WebView: $needsWebView");
    // 如果已经预加载了图片数据，直接显示
    if (_preloadedImages[index] != null) {
      debugPrint("使用预加载的图片: $url");
      return Image.memory(
        _preloadedImages[index]!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Icon(Icons.broken_image, color: Colors.grey),
          );
        },
      );
    }
    
    // 对于需要WebView处理的网站，显示占位符
    if (needsWebView) {
      return Stack(
        fit: StackFit.expand,
        children: [
          // 显示一个灰色背景
          Container(color: Colors.grey.shade200),
          // 显示网站图标或提示
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported_outlined,
                  size: 32,
                  color: Colors.grey.shade600,
                )
              ],
            ),
          ),
        ],
      );
    }
    
    final headers = _generateHttpHeaders();

    // 使用CachedNetworkImage加载
    return CachedNetworkImage(
      imageUrl: url,
      httpHeaders: headers,  // 添加HTTP请求头
      fit: BoxFit.cover,
      placeholder: (context, url) => const Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: Colors.grey,
          ),
        ),
      ),
      errorWidget: (context, url, error) => const Center(
        child: Icon(Icons.broken_image, color: Colors.grey),
      ),
      // 图片加载成功后，保存到预加载列表中
      imageBuilder: (context, imageProvider) {
        // 图片已经显示出来了，在后台异步获取二进制数据，不影响UI显示
        if (_preloadedImages[index] == null) {
          // 使用Future.microtask确保不阻塞UI
          Future.microtask(() => _fetchImageData(index, url));
        }
        return Image(image: imageProvider, fit: BoxFit.cover);
      },
    );
  }

  // 生成Http Header
  Map<String, String> _generateHttpHeaders() {
    // 构建HTTP请求头
    Map<String, String> headers = {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    };
    
    // 添加Referer
    if (widget.referer != null && widget.referer!.isNotEmpty) {
      headers['Referer'] = widget.referer!;
    }
    
    // 添加Cookie
    if (widget.cookies != null && widget.cookies!.isNotEmpty) {
      headers['Cookie'] = widget.cookies!;
    }

    return headers;
  }
  
  // 获取图片内容的HTTP请求方法
  Future<Uint8List> _fetchImageHttp(String url) async {
    try {
      // 使用统一的方法生成HTTP请求头
      final headers = _generateHttpHeaders();

      debugPrint('获取图片: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        if (bytes.isNotEmpty && Util.isValidImageFormat(bytes)) {
          debugPrint('成功获取图片: $url (${bytes.length} 字节)');
          return bytes;
        }
        else {
          debugPrint("获取图片失败: 图片格式无效或为空");
        }
      }
      else {
        debugPrint('获取图片失败: $url, HTTP错误，状态码 ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('获取图片失败: $url - $e');
    }
    return Uint8List(0);
  }

  // 异步获取图片数据并保存到预加载列表
  Future<void> _fetchImageData(int index, String url) async {
    if (_preloadedImages[index] != null) return;

    try {
      final imageBytes = await _fetchImageHttp(url);

      if (mounted) {
        // 直接更新数据，不触发UI刷新
        _preloadedImages[index] = imageBytes;
        _loadedCount++;
      }
    } catch (e) {
      debugPrint('预加载图片失败: $url - $e');
      // 即使失败也要标记为已处理，避免重复尝试
      if (mounted) {
        _preloadedImages[index] = Uint8List(0);
      }
    }
  }
}