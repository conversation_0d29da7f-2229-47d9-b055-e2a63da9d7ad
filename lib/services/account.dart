import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:imtrans/services/server_request.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:auth0_flutter/auth0_flutter.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// 账户管理类
/// 包含账户创建、删除、修改和读取
/// 所有与账户相关逻辑在此实现
///
/// *** 使用方法 ***
/// 单例：此类为单例模式不能直接实例化
/// 异步：在构造函数中需要读取Keychain，所以必须异步实例化Account类
/// Account account = await Account.instance;
///
/// *** 关于登录 ***
/// 相关方法为：singinWith(String type)
/// 目前有四种登录方式：苹果登录[APPLE], Auth0登录[AUTH0], Google[GOOGLE], 实例ID登录[INSTANCE]
/// 登录过程为根据不同的登录方式构造post数据，从服务器获取相应的登录TOKEN，登录成功后会将其保存在Keychain中，
///
/// *** 鉴权 ***
/// 每次对服务器的调用时，透过Header中增加Key为[Authorization],值为[Bearer TOKEN]（字符串"Bearer "注意有空格 加上TOKEN）进行用户鉴权
class Account with ChangeNotifier {
  static const String _keychainKeyOfInstanceId = 'AIMANGA_INSTANCE_ID';
  static const String _keychainKeyOfSigninType = "AIMANGA_SIGNIN_TYPE";
  static const String _keychainKeyOfSigninToken = "AIMANGA_SIGNIN_TOKEN";

  static const Map<String, String> signinTypes = {
    "APPLE": "SIGNIN_TYPE_APPLE",
    "AUTH0": "SIGNIN_TYPE_AUTH0",
    "GOOGLE": "SIGNIN_TYPE_GOOGLE",
    "INSTANCE": "SIGNIN_TYPE_INSTANCE",
  };

  static const String signinTypeApple = "SIGNIN_TYPE_APPLE";
  static const String signinTypeAuth0 = "SIGNIN_TYPE_AUTH0";
  static const String signinTypeGoogle = "SIGNIN_TYPE_GOOGLE";
  static const String signinTypeInstance = "SIGNIN_TYPE_INSTANCE";

  // Auth0登录需要的配置信息
  // static const String _auth0Scheme = "login.imtrans.littlegrass.cc";
  // static const String _auth0Domain = "mytoon-littlegrass.us.auth0.com";
  static const String _auth0Domain = "login.imtrans.littlegrass.cc";
  // static const String _auth0ClientId = "dk1rK4e8wXmfcbDuz68obF6vXksDsfvs";
  static const String _auth0ClientId = "x8BScocsuTYOCBDusiVJuTNrXNFRcuUr";

  // 用于保存唯一的 Account 实例
  static Account? _instance;

  static const FlutterSecureStorage secureStorage = FlutterSecureStorage();

  // 类变量
  String? _instanceId; // 当前实例ID
  String? _signinType; // 当前的登录类型值为signinTypes的KEY
  String? _signinToken; // 当前登录的Token
  String? _udid; // 当前设备的UDID
  
  // 当前账户的信息
  final Map<String, dynamic> _info = {
    "uid": "",
    "uuid": "",
    "name": "",
    "isVip": false,
    "vipExpire": ""
  };

  // 私有构造函数，防止外部直接创建实例
  Account._(this._instanceId, this._signinType, this._signinToken);

  // 返回单例实例的方法
  static Future<Account> get instance async {
    // 如果实例不存在，进行初始化
    _instance ??= await Account._init();
    return _instance!;
  }

  /// 异步初始化方法
  /// 每次类初始化时候就从secureStorage中读取登录情况
  /// 当有对实例内容进行更新的操作例如：登录、登出、删除账户等操作时也需要被调用以更新当前实例
  static Future<Account> _init() async {
    String? instanceId = await secureStorage.read(key: _keychainKeyOfInstanceId);
    String? signinType = await secureStorage.read(key: _keychainKeyOfSigninType);
    String? signinToken = await secureStorage.read(key: _keychainKeyOfSigninToken);

    Account account = Account._(instanceId, signinType, signinToken);
    // 获取并保存设备 UDID
    account._udid = await account.getDeviceUniqueId();
    await account.reloadInfo();
    // 更新 _instance 静态变量，确保外部访问的是最新的实例
    _instance = account;

    debugPrint(" ---- Account initialized ---- ");
    debugPrint(" instanceId: ${_instance!.instanceId}");
    debugPrint(" signinType: ${_instance!.signinType}");
    debugPrint(" signinToken: ${_instance!.signinToken}");
    debugPrint(" udid: ${_instance!.udid}");
    debugPrint(" userInfo: ${_instance?.info}");
    return account;
  }

  /// Getters
  String? get instanceId => _instanceId;
  String? get signinType => _signinType;
  String? get signinToken => _signinToken;
  String? get udid => _udid;
  Map<String, dynamic> get info => _info;
  bool get isVip => _info['isVip'];

  /// 生成实例ID
  /// 实例ID是用户未登录时用来确认用户身份的，此ID在用户删除App时随之一同删除否则一直不变
  ///
  /// ** 生成逻辑 **
  /// 使用当前时间戳加四位随机数字再经由md5后当做实例ID
  /// 然后存储到Keychain和类变量中
  ///
  /// ** 返回值 **
  /// 返回实例ID
  Future<String> generateInstanceId() async {
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    int rd = Random().nextInt(9000) + 1000;
    String timestampStr = "$timestamp$rd";
    var bytes = utf8.encode(timestampStr);
    String instanceId = md5.convert(bytes).toString();

    // 更新Keychain中的instanceId
    await secureStorage.write(key: _keychainKeyOfInstanceId, value: instanceId);
    // 更新类变量
    _instanceId = instanceId;

    return instanceId;
  }

  /// 清除单例
  static void clearInstance() {
    _instance = null;
  }

  /// 在本地Keychain存储登录信息
  /// Keychain内存储的登录信息有两个Key
  /// 1. $_keychainKeyOfSigninType : 当前登录的类型，可选内容为 $signinType的Key
  /// 2. $_keychainKeyOfSigninToken: 当前登录Token
  ///
  /// ** 返回值 **
  /// 成功或失败：Boolean
  Future<bool> _updateSigninKeychain(String type, String token) async {
    if (signinTypes.containsKey(type)) {
      await secureStorage.write(key: _keychainKeyOfSigninType, value: type);
      await secureStorage.write(key: _keychainKeyOfSigninToken, value: token);
      return true;
    }
    return false;
  }

  /// 退出实名登录
  /// 注意：Instance登录无法退出，只用于退出当前绑定的"实名"账户例如[APPLE]或[AUTH0]
  /// 逻辑为：如果是实名登录，则删除Token，重新用实例ID登录，简化为：直接使用实例ID登录
  ///
  /// *** 返回值 ***
  /// bool 成功true 失败false
  Future<bool> signOut() async {
    // 如果是实名登录，清除登录信息
    if (signinType != "INSTANCE" && signinType != null) {
      // 清除Keychain
      await secureStorage.delete(key: _keychainKeyOfSigninType);
      await secureStorage.delete(key: _keychainKeyOfSigninToken);
      // 退出实名登录后，自动使用当前实例ID进行匿名登录
      return await signIn();
    }
    return false;
  }

  /// 删除账户
  /// 通知服务器标记删除,清空当前keychain, 清除instanceid和token并重新初始化实例
  ///
  /// *** 返回值 ***
  /// bool 成功true 失败false
  Future<bool> delete() async {
    try {
      Map<String, dynamic> result = await ServerRequest.postData(ServerRequest.deleteData, {});
      if (!result.containsKey("code") || result["code"] != 0) {
        throw Exception("Server return: $result");
      }
      // 清除所有信息
      await secureStorage.delete(key: _keychainKeyOfSigninType);
      await secureStorage.delete(key: _keychainKeyOfSigninToken);
      await secureStorage.delete(key: _keychainKeyOfInstanceId);
      _instanceId = null;

      // 删除账户后，自动进行匿名登录
      if (!await signIn()) {
        throw Exception("Signin error");
      }
      return true;
    } catch (e) {
      debugPrint("Delete failed: $e");
    }
    return false;
  }

  /// 用户登录
  /// 登录成功后会重新初始化实例以更新用户最新信息
  ///
  /// *** 参数 ***
  /// type: 登录类型，值为signinTypes的Key
  ///
  /// *** 返回值 ***
  /// bool 成功返回true 失败返回false
  Future<bool> signIn({String? type}) async {
    bool isSuccess = false; // 用户取消登录或者登录出现错误
    switch (type) {
      case signinTypeApple:
        // Apple登录只在iOS上可用
        if (Platform.isIOS) {
          isSuccess = await _signInWith("APPLE");
        } else {
          debugPrint("Apple Sign In is only available on iOS");
          return false;
        }
        break;

      case signinTypeAuth0:
        isSuccess = await _signInWith("AUTH0");
        break;

      case signinTypeGoogle:
        isSuccess = await _signInWith("GOOGLE");
        break;

      default:
        isSuccess = await _signInWith("INSTANCE");
    }
    return isSuccess;
  }

  /// 更新当前实例的属性
  /// 用于在登录状态改变时同步更新当前实例
  void _updateCurrentInstance(Account newInstance) {
    _instance = newInstance;
    _instanceId = newInstance._instanceId;
    _signinType = newInstance._signinType;
    _signinToken = newInstance._signinToken;
    _info.clear();
    _info.addAll(newInstance._info);
    notifyListeners();
  }

  Future<bool> _signInWith(String type) async {
    String postUri = "";
    String instanceId = this.instanceId ?? await generateInstanceId();
    String deviceUniqueId = await getDeviceUniqueId() ?? "";
    Map<String, dynamic> postData = {"device_id": instanceId, "udid": deviceUniqueId};

    try {
      switch (type) {
        case "APPLE":
          // Apple登录只在iOS上可用
          if (!Platform.isIOS) {
            throw Exception('Apple Sign In is only available on iOS');
          }
          postUri = ServerRequest.appleLogin;
          final credential = await SignInWithApple.getAppleIDCredential(scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ]);
          String fullName = credential.givenName ?? "";
          if (credential.familyName != null) {
            fullName += " ${credential.familyName}";
          }
          if (credential.identityToken != null) {
            postData.addAll({
              "identifier": credential.userIdentifier,
              "authorizationCode": credential.authorizationCode,
              "identityToken": credential.identityToken,
              "email": credential.email,
              "name": fullName
            });
          }
          break;

        case "GOOGLE":
          postUri = ServerRequest.googleLogin;
          try {
            final GoogleSignIn googleSignIn = GoogleSignIn(
              signInOption: SignInOption.standard,
              scopes: ['email', 'profile'],
              forceCodeForRefreshToken: false
            );
            final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
            if (googleUser == null) {
              // 用户取消登录
              // throw Exception('Google sign in cancelled');
              return false;
            }
            
            // 获取Google身份验证
            final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
            
            // 获取凭证
            final credential = GoogleAuthProvider.credential(
              accessToken: googleAuth.accessToken,
              idToken: googleAuth.idToken,
            );
            
            // 使用Firebase进行身份验证
            final UserCredential userCredential = 
                await FirebaseAuth.instance.signInWithCredential(credential);
                
            // 获取用户信息
            final User? user = userCredential.user;
            if (user != null) {
              debugPrint("Google sign in success: ${user.email}");
              postData.addAll({
                "id_token": googleAuth.idToken,
                "email": user.email,
                "name": user.displayName ?? "",
              });
            } else {
              debugPrint("Google sign in failed: ${userCredential}");
              return false;
            }
          } catch (e) {
            debugPrint("Google sign in error: $e");
            return false;
          }
          break;

        case "AUTH0":
          postUri = ServerRequest.auth0Login;
          final auth0 = Auth0(_auth0Domain, _auth0ClientId);
          // useEphemeralSession: true 配置不弹出警告框
          final credential = await auth0.webAuthentication().login(useEphemeralSession: true);
          postData.addAll({"auth0_access_token": credential.idToken});
          break;

        case "INSTANCE":
          postUri = ServerRequest.deviceLogin;
          break;

        default:
          throw Exception('Error: Signin type not exist');
      }

      var result = await ServerRequest.postData(postUri, postData);
      if (result.containsKey("code") && result["code"] == 0) {
        if (await _updateSigninKeychain(type, result["data"]["access_token"])) {
          Account newInstance = await _init();
          _updateCurrentInstance(newInstance);
          
          debugPrint(" ++ isVip: ${isVip}");
          debugPrint(" ++ isSignedIn: ${isSignedIn()}");
          return true;
        }
      }
    } catch (e) {
      debugPrint("$e");
    }
    return false;
  }

  /// 判断当前是否已经是登录状态
  /// 判断逻辑是_singinToken是否为null
  ///
  /// *** 返回值 ***
  /// boolean
  bool isSignedIn() {
    if (!_instance!._signinType!.contains("INSTANCE") && _instance!._signinToken != null) {
      return true;
    }
    return false;
  }

  /// 获取剩余VIP天数
  /// 如果从未成为VIP，则返回空字符串
  /// 否则返回剩余天数
  String getRemainingVipDays() {
    if (_info['vipExpire'] == "") return "";

    DateTime currentTime = DateTime.now();
    DateTime expireTime = DateTime.parse(_info['vipExpire']);
    Duration difference = expireTime.difference(currentTime);

    if (difference.isNegative) {
      // VIP已过期
      Duration expiredDuration = -difference;
      int days = expiredDuration.inDays;
      return "Expired ${days} ${days > 1 ? 'days' : 'day'} ago";
    } else {
      // VIP未过期
      int days = difference.inDays;
      if (days == 0) {
        return "Expires today";
      }
      return "$days ${days > 1 ? 'days' : 'day'} left";
    }
  }

  /// 从服务器读取当前用户的信息
  /// 主要是是否是VIP状态isVip、以及VIP过期时间vipExpire
  ///
  /// *** 返回值 ***
  /// 获取成功返回包含用户信息的Map, 格式参考服务端文档
  /// 失败返回null
  Future<Map<String, dynamic>?> _getAccountInfo() async {
    //需要判断signinToken是否为空，如果不判断新用户就会导致死循环
    if (signinToken != null) {
      try {
        Map<String, dynamic> result = await ServerRequest.getData(
            ServerRequest.getUserInfo, {}, signinToken);
        if (result.containsKey("code") && result["code"] == 0) {
          return result["user"];
        }
        // 如果code为-1，则表示用户token过期, 重新登录
        else if (result.containsKey("code") && result["code"] == -1){
          signIn();
        }
      } catch (e) {
        debugPrint("Error getting account info: $e");
      }
    }
    return null;
  }

  /// 重载账户信息
  ///
  ///
  /// *** 返回值 ***
  /// 成功与否 bool
  Future<bool> reloadInfo() async {
    var userInfo = await _getAccountInfo();
    if (userInfo != null) {
      // 保存旧的信息用于比较
      Map<String, dynamic> oldInfo = Map.from(_info);
      
      // 解析新信息
      _info["uid"] = userInfo["uid"] ?? "";
      _info["uuid"] = userInfo["uuid"] ?? "";
      _info["name"] = userInfo["name"] ?? "";
      _info["isVip"] = userInfo["vip"] == 0 ? false : true;
      _info["vipExpire"] = userInfo["vip_expired_at"] ?? "";

      // 只有当信息真正发生变化时才通知监听器
      bool hasChanged = false;
      for (String key in _info.keys) {
        if (_info[key] != oldInfo[key]) {
          hasChanged = true;
          debugPrint('Account info changed: $key: ${oldInfo[key]} -> ${_info[key]}');
          break;
        }
      }
      
      if (hasChanged) {
        notifyListeners();
      }
      
      return true;
    }
    return false;
  }

  /// 获取设备的唯一ID
  /// 用于校验用户防止多次注册
  Future<String?> getDeviceUniqueId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id; // Unique ID on Android
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor; // Unique ID on iOS
    }
    return null;
  }
}
