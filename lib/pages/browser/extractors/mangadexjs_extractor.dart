import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:webview_flutter/webview_flutter.dart';
import 'base_extractor.dart';

/// MangaDex网站图片提取器
/// 专门处理MangaDex网站的图片提取和下载
class MangaDexJsExtractor extends BaseImageExtractor {
  MangaDexJsExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) : super(
    controller: controller,
    currentUrl: currentUrl,
  );
  
  @override
  Future<List<String>> extractImageUrls() async {
    const String jsMangaDexImages = '''
      function getMangaDexImages() {
        const images = [];
        
        // 获取漫画阅读器中的图片
        const readerImages = document.querySelectorAll('.md-reader img');
        readerImages.forEach(img => {
          if (img.src && img.src.trim() !== '' && !img.src.startsWith('data:')) {
            images.push(img.src);
          }
        });
        
        // 如果上面的选择器没有找到图片，尝试其他选择器
        if (images.length === 0) {
          // 尝试获取漫画封面图片
          const coverImages = document.querySelectorAll('.cover-art img');
          coverImages.forEach(img => {
            if (img.src && img.src.trim() !== '' && !img.src.startsWith('data:')) {
              images.push(img.src);
            }
          });
          
          // 尝试获取所有大图
          const allImgs = document.querySelectorAll('img');
          allImgs.forEach(img => {
            // 过滤掉小图标和广告
            if (img.src && img.src.trim() !== '' && 
                !img.src.startsWith('data:') && 
                img.width > 100 && img.height > 100) {
              images.push(img.src);
            }
          });
        }
        
        return JSON.stringify([...new Set(images)]);
      }
      getMangaDexImages();

    ''';
    
    try {
      final result = await controller.evaluateJavascript(source: jsMangaDexImages);
      if (result == null) return [];
      
      // 解析JSON结果
      final List<dynamic> srcList = jsonToList(result);
      final List<String> urls = srcList.map((e) => e.toString()).toList();
      
      // 调试输出
      debugPrint("从MangaDex提取到 ${urls.length} 个图片URL");
      for (int i = 0; i < urls.length; i++) {
        debugPrint("MangaDex图片[$i]: ${urls[i]}");
      }
      
      return urls;
    } catch (e) {
      debugPrint("MangaDex图片提取失败: $e");
      return [];
    }
  }
  
  @override
  bool needsWebViewForImages() {
    return true; // MangaDex网站需要使用WebView获取图片
  }
  
  /// 将JSON字符串转换为List
  List<dynamic> jsonToList(String jsonStr) {
    try {
      // 移除可能的引号包裹
      String processedStr = jsonStr;
      if (processedStr.startsWith('"') && processedStr.endsWith('"')) {
        processedStr = processedStr.substring(1, processedStr.length - 1);
        // 处理转义字符
        processedStr = processedStr.replaceAll('\\"', '"');
      }
      
      try {
        // 尝试直接解析JSON
        final decoded = json.decode(processedStr);
        if (decoded is List) {
          return decoded;
        }
      } catch (e) {
        // 如果直接解析失败，使用备用方法
        debugPrint('直接JSON解析失败，使用备用方法: $e');
      }
      
      return List<dynamic>.from(Uri.decodeComponent(processedStr).codeUnits)
          .map((e) => String.fromCharCode(e))
          .join('')
          .trim()
          .split(',');
    } catch (e) {
      debugPrint('JSON解析错误: $e');
      return [];
    }
  }
}