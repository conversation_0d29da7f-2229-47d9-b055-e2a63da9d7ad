import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'account.dart';
import 'package:mime/mime.dart';

class ServerRequest{
  //host
  static const String _host="api.littlegrass.cc";
  static const String _appName = "ai-manga";

  //user
  static const String deviceLogin = "/user/login_device";
  static const String appleLogin = "/user/login_apple";
  static const String googleLogin = "/user/login_google";
  static const String auth0Login = "/user/login_auth0";
  static const String getUserInfo = "/user/info";
  static const String deleteData = "/user/delete";

  //task
  static const String fileUpload = "/common/upload";
  static const String createTask = "/task/create";
  static const String queryTask = "/task/query";
  static const String getTaskList= "/task/list";
  static const String deleteTask= "/task/delete";

  //产品（任务完成创建的图片和zip）
  static const String createProduct = "/product/create";
  static const String editProduct = "/product/edit";
  static const String deleteProduct = "/product/delete";
  static const String getProductList = "/product/list";
  static const String queryProduct = "/product/query";
  static const String freeLimit = "/free_limit/query";
  static const String increaseQuota = "/free_limit/add";

  //apple支付
  static const String paymentCreate = "/payment/create";
  static const String paymentQuery = "/payment/query";
  static const String paymentApplePay = "/payment/apple_iap";
  //反馈
  static const String feedBack = "/feedback/create";
  static const String apiRecommendedSites = "/recommend_link/list";

  // 服务器返回的结果代码对应的信息
  static const Map<String, String> resultCode = {
    "0"  : "Success",
    "-1" : "Failed",
    "-2" : "Require VIP",
  };
  
  // 基础header内容
  static const String _keyOfHeaderAuth = "Authorization";
  static const Map<String,String> baseHeader = {
    "Content-Type": "application/json",
    _keyOfHeaderAuth: "Bearer "
  };

  static Future<Map<String, String>> getHeader() async {
    Account account = await Account.instance;
    String token = account.signinToken ?? "";

    Map<String, String> header = Map.from(baseHeader);
    header[_keyOfHeaderAuth] = baseHeader[_keyOfHeaderAuth]! + token;
    return header;
  }

  /// 传入用户token来制作请求头
  /// 用于在需要降低request对account耦合的情况下进行服务器访问
  /// 
  /// ** 返回值 **
  /// 包含http header的map
  static Map<String, String> makeHeader([String? token]) {
    Map<String, String> header = Map.from(baseHeader);
    if (token != null) {
      header[_keyOfHeaderAuth] = baseHeader[_keyOfHeaderAuth]! + token;
    }
    else {
      header.remove(_keyOfHeaderAuth);
    }
    return header;
  }

  static Future< Map<String, dynamic>> _request(String method, String url, Map<String,dynamic> data, [String? token]) async {
    http.Response response;
    url = "/$_appName$url";

    // 如果传入了用户Token则直接makeheader无需再调用account取得header
    Map<String, String> requestHeader;
    if (token != null) {
      requestHeader = makeHeader(token);
    }
    else {
      requestHeader = await getHeader();
    }
    
    try {
      switch (method) {
        case "get" :
          var uri = Uri.https(_host, url, data);
          response = await http.get(uri, headers: requestHeader);
          break;
          
        case "post" :
          var uri = Uri.https(_host, url);
          response = await http.post(uri, body: jsonEncode(data), headers: requestHeader);
          break;

        default:
          throw Exception("Unsupported HTTP method: $method");
      }

      var body = response.body;
      debugPrint("--------- HTTP REQUEST METHOD($method) ---------");
      debugPrint("$_host$url");
      debugPrint("Header: $requestHeader");
      debugPrint("Send: $data");
      if (response.statusCode != 200) {
        throw Exception("Failed: http code: ${response.statusCode}");
      }
      debugPrint("Response: $body");
      return jsonDecode(body);
    } 
    catch(e) { 
      debugPrint("Server Request Failed: ${e.toString()}");
      return {"code":-1, "error":e};
    }
  }

  static Future<Map<String, dynamic>> getData(String url, Map<String, dynamic> data, [String? token]) async {
    // 将所有参数值转换为字符串
    final stringParams = data.map((key, value) => MapEntry(key, value.toString()));
    return await _request("get", url, stringParams, token);
  }

  static Future<Map<String, dynamic>> postData(String url, Map<String, dynamic> data, [String? token]) async {
    return await _request("post", url, data, token);
  }

  static Future<Map<String, dynamic>> upload(url, Uint8List fileData, String filename) async {
    var responseBody;
    var headers=await getHeader();
    url = "/$_appName$url";

    final mimeType = lookupMimeType(filename, headerBytes: fileData);
    var uri = Uri.https(_host, url);
    var multipartFile = http.MultipartFile.fromBytes(
      'file',
      fileData,
      filename: filename,
      contentType: MediaType(mimeType?.split('/')[0] ?? 'image', mimeType?.split('/')[1] ?? 'jpeg'),
    );
    var request = http.MultipartRequest("POST", uri);
    request.headers.addAll(headers);
    request.files.add(multipartFile);

    try {
      var response = await request.send();
      responseBody = await response.stream.bytesToString();

      debugPrint("---------- File Upload ----------");
      debugPrint("$_host$url");
      debugPrint("headers: $headers");
      debugPrint("filename: $filename");
      debugPrint("mimeType: $mimeType");
      debugPrint("response: $responseBody");
    } catch(e) {
      debugPrint("File Upload Error: ${e.toString()}");
      return {"code": 1001, "error": e};
    }

    try {
      Map<String, dynamic> decodedData = jsonDecode(responseBody);
      return decodedData;
    } catch (e) {
      debugPrint("Response decode failed: ${e.toString()}");
      return {"code": -1, "error": e};
    }
  }
}