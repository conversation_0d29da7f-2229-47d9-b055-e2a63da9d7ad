import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'base_extractor.dart';

/// Fanfox网站图片提取器
class FanfoxExtractor extends BaseImageExtractor {
  FanfoxExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) : super(
          controller: controller,
          currentUrl: currentUrl,
        );

  @override
  Future<List<String>> extractImageUrls() async {
    const String jsFanfoxImages = '''
      (function() {
        try {
          var images = [];
          var imgElement = document.querySelector('.mangaread-img img');
          if (imgElement) {
            var src = imgElement.getAttribute('src') || imgElement.getAttribute('data-src');
            if (src) {
              if (src.startsWith('//')) {
                src = 'https:' + src;
              }
              images.push(src);
            }
          }
          
          return JSON.stringify(images);
        } catch (e) {
          console.error('提取图片错误:', e);
          return JSON.stringify([]);
        }
      })();
    ''';

    try {
      // 执行JavaScript获取当前页面的图片URL
      final dynamic result = await controller
          .evaluateJavascript(source: jsFanfoxImages)
          .timeout(const Duration(seconds: 15))
          .catchError((error) {
        debugPrint('JavaScript执行错误: $error');
        return [];
      });

      // 调试输出JavaScript执行结果
      debugPrint('JavaScript执行结果类型: ${result.runtimeType}');

      List<String> urls = [];
      if (result != null) {
        if (result is String) {
          try {
            final List<dynamic> decoded = json.decode(result);
            urls = decoded.map((e) => e.toString()).toList();
          } catch (e) {
            debugPrint('JSON解析错误: $e');
            urls = result.split('|').where((url) => url.isNotEmpty).toList();
          }
        } else if (result is List) {
          urls = result.map((e) => e.toString()).toList();
        }
      }

      // 调试输出
      if (urls.isNotEmpty) {
        debugPrint('Fanfox提取到 ${urls.length} 个图片URL');
        for (int i = 0; i < urls.length && i < 5; i++) {
          debugPrint('图片[$i]: ${urls[i]}');
        }
        if (urls.length > 5) {
          debugPrint('... 还有 ${urls.length - 5} 个URL未显示');
        }
      } else {
        debugPrint('未找到任何图片URL，尝试备用方法');
        urls = await _fallbackImageExtraction();
      }

      return urls;
    } catch (e) {
      debugPrint('执行JavaScript出错: $e');
      return await _fallbackImageExtraction();
    }
  }

  /// 备用图片提取方法，当主方法失败时使用
  Future<List<String>> _fallbackImageExtraction() async {
    try {
      // 使用更简单的JavaScript代码
      const String simpleJs = '''
        (function() {
          var urls = [];
          var imgs = document.getElementsByTagName('img');
          for (var i = 0; i < imgs.length; i++) {
            if (imgs[i].src && imgs[i].src.length > 0) {
              urls.push(imgs[i].src);
            }
          }
          return urls.join('|');
        })();
      ''';

      final dynamic result = await controller
          .evaluateJavascript(source: simpleJs)
          .timeout(const Duration(seconds: 5));

      if (result != null && result is String && result.isNotEmpty) {
        return result.split('|').where((url) => url.isNotEmpty).toList();
      }

      return [];
    } catch (e) {
      debugPrint('备用图片提取也失败: $e');
      return [];
    }
  }
}
