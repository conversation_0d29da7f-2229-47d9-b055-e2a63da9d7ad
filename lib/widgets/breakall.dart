import 'package:flutter/material.dart';

class BreakAllTextSpan extends TextSpan {
  BreakAllTextSpan(String text,TextStyle style) : super(text: text.breakString,style: style);

  @override
  void computeToPlainText(StringBuffer buffer,
      {bool includeSemanticsLabels = true, bool includePlaceholders = true}) {
    if (semanticsLabel != null && includeSemanticsLabels) {
      buffer.write(semanticsLabel);
    } else if (text != null) {
      buffer.write(text?.unbreakString);
    }
    if (children != null) {
      for (final child in children!) {
        child.computeToPlainText(
          buffer,
          includeSemanticsLabels: includeSemanticsLabels,
          includePlaceholders: includePlaceholders,
        );
      }
    }
  }
}

const zeroWidthSpace = '\u200B';

extension StringExtension on String {
  String get breakString {
    // Makes the string in [Text] widget behave like with CSS style `word-break: break-all;`
    // (src: https://github.com/flutter/flutter/issues/61081#issuecomment-1103330522)
    return runes
        .map<String>(
            (element) => '${String.fromCharCode(element)}$zeroWidthSpace')
        .join();
  }

  String get unbreakString {
    return replaceAll(zeroWidthSpace, String.fromCharCode(0xFFFC));
  }
}

class BreakAllText extends StatelessWidget {
  final String text;
  final TextStyle style;
  final int? maxLines;

  const BreakAllText(this.text, {
    super.key,
    required this.style,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      BreakAllTextSpan(text, style),
      maxLines: maxLines,
    );
  }
}