import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product_model.dart';

/// 支持的翻译目标语言管理类
class LanguageManager extends ChangeNotifier {
  // 单例模式
  static final LanguageManager _instance = LanguageManager._internal();
  factory LanguageManager() => _instance;
  
  LanguageManager._internal();
  
  // 语言相关属性
  static String _currentLanguage = Product.DEFAULT_TARGET_LANG; // 默认语言
  static const String _prefKey = 'app_language'; // SharedPreferences键名
  static bool _initialized = false; // 是否已初始化

  // Getters
  static String get currentLanguage => _currentLanguage;
  static bool get initialized => _initialized;
  
  // 获取当前语言代码
  static String get currentLanguageCode {
    return Product.targetLanguages[_currentLanguage] ?? 'en';
  }
  
  // 设置新的语言
  static Future<void> setLanguage(String language) async {
    if (_currentLanguage == language) return;
    
    _currentLanguage = language;
    await _saveLanguage();
    _instance.notifyListeners();
  }
  
  // 保存语言设置到SharedPreferences
  static Future<void> _saveLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_prefKey, _currentLanguage);
  }
  
  // 从SharedPreferences加载语言设置
  static Future<void> loadLanguage() async {
    if (_initialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_prefKey);
      
      if (savedLanguage != null && Product.targetLanguages.containsKey(savedLanguage)) {
        _currentLanguage = savedLanguage;
      }
    } catch (e) {
      debugPrint("Error loading language setting: $e");
    }
    
    _initialized = true;
    _instance.notifyListeners();
  }
  
  // 初始化语言设置
  static Future<void> initialize() async {
    if (_initialized) return;
    await loadLanguage();
  }
}