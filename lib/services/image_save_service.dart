import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gal/gal.dart';
import 'package:http/http.dart' as http;
import 'package:imtrans/l10n/generated/app_localizations.dart';
import '../models/product_model.dart';
import '../widgets/toast_widget.dart';

/// 图片保存类型枚举
enum ImageSaveType {
  /// 原图（翻译前）
  original,
  /// 翻译后的图片（包含翻译文字覆盖）
  translated,
  /// 自动选择（如果有翻译结果则保存翻译图，否则保存原图）
  auto,
}

/// 图片保存结果
class ImageSaveResult {
  final bool success;
  final String? message;
  final String? savedPath;

  ImageSaveResult({
    required this.success,
    this.message,
    this.savedPath,
  });

  factory ImageSaveResult.success({String? message, String? savedPath}) {
    return ImageSaveResult(
      success: true,
      message: message,
      savedPath: savedPath,
    );
  }

  factory ImageSaveResult.failure(String message) {
    return ImageSaveResult(
      success: false,
      message: message,
    );
  }
}

/// 统一图片保存服务
/// 负责处理产品列表和图片查看器的图片保存功能
/// 支持保存原图、翻译后图片等多种类型
class ImageSaveService {
  static final ImageSaveService _instance = ImageSaveService._internal();
  factory ImageSaveService() => _instance;
  ImageSaveService._internal();

  static ImageSaveService get instance => _instance;

  /// 保存产品图片
  /// [product] 要保存的产品
  /// [saveType] 保存类型
  /// [imageIndex] 要保存的图片索引，默认为0（第一张图片）
  /// [context] 用于显示提示信息的上下文
  Future<ImageSaveResult> saveProductImage(
    Product product, {
    ImageSaveType saveType = ImageSaveType.auto,
    int imageIndex = 0,
    BuildContext? context,
  }) async {
    try {
      // 验证输入参数
      if (product.images.isEmpty) {
        final message = context != null 
            ? AppLocalizations.of(context)!.noImagesToSave
            : 'No images to save';
        if (context != null) {
          ToastWidget.showError(message);
        }
        return ImageSaveResult.failure(message);
      }

      if (imageIndex < 0 || imageIndex >= product.images.length) {
        final message = context != null
            ? AppLocalizations.of(context)!.imageIndexOutOfRange
            : 'Image index out of range';
        if (context != null) {
          ToastWidget.showError(message);
        }
        return ImageSaveResult.failure(message);
      }

      final productImage = product.images[imageIndex];

      // 根据保存类型决定保存方式
      switch (saveType) {
        case ImageSaveType.original:
          return await _saveOriginalImage(productImage, context);

        case ImageSaveType.translated:
          if (productImage.translateResult != null && 
              productImage.translateResult!.isNotEmpty) {
            return await _saveTranslatedImage(productImage, context);
          } else {
            final message = context != null
                ? AppLocalizations.of(context)!.noTranslationResultSavingOriginal
                : 'No translation result, saving original image';
            if (context != null) {
              ToastWidget.show(message);
            }
            return await _saveOriginalImage(productImage, context);
          }

        case ImageSaveType.auto:
          if (productImage.translateResult != null && 
              productImage.translateResult!.isNotEmpty &&
              product.status == Product.STATUS_TRANSLATED) {
            return await _saveTranslatedImage(productImage, context);
          } else {
            return await _saveOriginalImage(productImage, context);
          }
      }
    } catch (e) {
      final message = '保存图片失败: $e';
      debugPrint(message);
      if (context != null) {
        ToastWidget.showError('${AppLocalizations.of(context)!.failedToSaveImage}: $e');
      }
      return ImageSaveResult.failure(message);
    }
  }

  /// 保存多个产品的图片
  /// [products] 要保存的产品列表
  /// [saveType] 保存类型
  /// [context] 用于显示提示信息的上下文
  Future<List<ImageSaveResult>> saveMultipleProductImages(
    List<Product> products, {
    ImageSaveType saveType = ImageSaveType.auto,
    BuildContext? context,
  }) async {
    final results = <ImageSaveResult>[];
    int successCount = 0;
    int failureCount = 0;

    for (final product in products) {
      final result = await saveProductImage(
        product,
        saveType: saveType,
        context: null, // 批量保存时不显示单个错误提示
      );
      results.add(result);
      
      if (result.success) {
        successCount++;
      } else {
        failureCount++;
      }
    }

    // 显示批量保存结果
    if (context != null) {
      if (failureCount == 0) {
        ToastWidget.showSuccess(AppLocalizations.of(context)!.savedSuccessfully(successCount));
      } else if (successCount == 0) {
        ToastWidget.showError(AppLocalizations.of(context)!.savingFailed);
      } else {
        ToastWidget.show(AppLocalizations.of(context)!.savedPartially(successCount, failureCount));
      }
    }

    return results;
  }

  /// 通过RepaintBoundary保存当前显示的图片（用于图片查看器）
  /// [globalKey] RepaintBoundary的GlobalKey
  /// [context] 用于显示提示信息的上下文
  /// [pixelRatio] 图片像素比，默认为3.0
  Future<ImageSaveResult> saveCurrentDisplayImage(
    GlobalKey globalKey, {
    BuildContext? context,
    double pixelRatio = 3.0,
  }) async {
    try {
      final RenderRepaintBoundary? boundary = 
          globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      
      if (boundary == null) {
        final message = context != null
            ? AppLocalizations.of(context)!.imageContentNotFound
            : 'Cannot get image content';
        if (context != null) {
          ToastWidget.showError(message);
        }
        return ImageSaveResult.failure(message);
      }

      // 为了确保捕获完整的长图，我们需要让RepaintBoundary在自然状态下进行渲染
      // 先检查boundary的实际大小
      final Size boundarySize = boundary.size;
      debugPrint('RepaintBoundary size: ${boundarySize.width} x ${boundarySize.height}');
      
      // 如果高度过小，可能是因为视窗限制，尝试获取更大的渲染尺寸
      double effectivePixelRatio = pixelRatio;
      
      // 对于可能的长图，适当降低像素比例以避免内存问题，但确保质量
      if (boundarySize.height > 2000) {
        effectivePixelRatio = 1.0; // 降低像素比例避免内存溢出
        debugPrint('Detected long image, adjusting pixel ratio to: $effectivePixelRatio');
      }

      // 生成高质量图片
      final ui.Image image = await boundary.toImage(pixelRatio: effectivePixelRatio);
      debugPrint('Generated image size: ${image.width} x ${image.height}');
      
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData == null) {
        final message = context != null
            ? AppLocalizations.of(context)!.imageDataGenerationFailed
            : 'Image data generation failed';
        if (context != null) {
          ToastWidget.showError(message);
        }
        return ImageSaveResult.failure(message);
      }

      final Uint8List imageBytes = byteData.buffer.asUint8List();
      
      // 直接使用Gal.putImageBytes保存
      await Gal.putImageBytes(imageBytes);

      final message = context != null
          ? AppLocalizations.of(context)!.imageSavedSuccessfully
          : 'Image saved successfully';
      if (context != null) {
        ToastWidget.showSuccess(message);
      }
      return ImageSaveResult.success(message: message);

    } catch (e) {
      final message = 'Save image failed: $e';
      debugPrint(message);
      if (context != null) {
        ToastWidget.showError(AppLocalizations.of(context)!.savingFailed);
      }
      return ImageSaveResult.failure(message);
    }
  }

  /// 保存原图
  Future<ImageSaveResult> _saveOriginalImage(
    ProductImage productImage,
    BuildContext? context,
  ) async {
    try {
      // 优先使用本地缓存文件的直接路径（避免质量损失）
      if (productImage.localFileName != null && productImage.localFileName!.isNotEmpty) {
        final imagePath = ProductImage.localCacheDir.path + '/' + productImage.localFileName!;
        final imageFile = File(imagePath);
        
        if (await imageFile.exists()) {
          // 直接使用文件路径保存
          await Gal.putImage(imagePath);
          
          final message = context != null
              ? AppLocalizations.of(context)!.originalImageSavedSuccessfully
              : 'Original image saved successfully';
          if (context != null) {
            ToastWidget.showSuccess(message);
          }
          return ImageSaveResult.success(message: message);
        }
      }

      // 如果本地缓存不存在，从网络下载
      if (productImage.url != null) {
        final response = await http.get(Uri.parse(productImage.url!));
        
        if (response.statusCode == 200) {
          final imageBytes = response.bodyBytes;
          
          // 直接使用Gal.putImageBytes保存
          await Gal.putImageBytes(imageBytes);
          
          final message = context != null
              ? AppLocalizations.of(context)!.originalImageSavedSuccessfully
              : 'Original image saved successfully';
          if (context != null) {
            ToastWidget.showSuccess(message);
          }
          return ImageSaveResult.success(message: message);
        } else {
          final statusMessage = context != null
              ? AppLocalizations.of(context)!.networkRequestFailed(response.statusCode.toString())
              : 'Network request failed: ${response.statusCode}';
          throw Exception(statusMessage);
        }
      }

      final message = context != null
          ? AppLocalizations.of(context)!.cannotGetImageData
          : 'Cannot get image data';
      throw Exception(message);

    } catch (e) {
      final message = context != null
          ? AppLocalizations.of(context)!.saveOriginalImageFailed(e.toString())
          : 'Failed to save original image: $e';
      throw Exception(message);
    }
  }

  /// 保存翻译后的图片（通过重新渲染TextOverlay）
  Future<ImageSaveResult> _saveTranslatedImage(
    ProductImage productImage,
    BuildContext? context,
  ) async {
    try {
      // 这里需要创建一个离屏的TextOverlay来生成翻译后的图片
      // 由于需要Widget渲染，这个方法需要在有context的环境下调用
      if (context == null) {
        throw Exception('Saving translated image requires Context');
      }

      // 这里需要实现离屏渲染逻辑
      // 由于Flutter的限制，实际实现时可能需要使用OffscreenWidget或其他方案
      // 暂时抛出提示，建议使用RepaintBoundary方式
      throw Exception(AppLocalizations.of(context)!.saveTranslatedImageUseRepaintBoundary);

    } catch (e) {
      final message = context != null
          ? AppLocalizations.of(context)!.saveTranslatedImageFailed(e.toString())
          : 'Failed to save translated image: $e';
      throw Exception(message);
    }
  }

  /// 检查相册权限
  Future<bool> checkGalleryPermission() async {
    try {
      // Gal插件会自动处理权限请求
      return true;
    } catch (e) {
      debugPrint('检查相册权限失败: $e');
      return false;
    }
  }

  /// 获取图片信息（用于调试和日志）
  Map<String, dynamic> getImageInfo(ProductImage productImage) {
    return {
      'name': productImage.name,
      'url': productImage.url,
      'localFileName': productImage.localFileName,
      'hasTranslation': productImage.translateResult?.isNotEmpty ?? false,
      'translateStatus': productImage.translateStatus,
    };
  }


} 