import 'dart:async';
import 'package:flutter/material.dart';
import 'package:imtrans/widgets/loading.dart';

class LoadingManager {
  static final LoadingManager _instance = LoadingManager._internal();
  factory LoadingManager() => _instance;
  LoadingManager._internal();

  // 单例模式
  static LoadingManager get instance => _instance;

  // Loading 状态
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // 超时计时器
  Timer? _timeoutTimer;
  
  // 当前的 loading context
  BuildContext? _currentContext;
  
  // 当前的 loading 对话框 key - 修改为使用 Loading 类型
  GlobalKey<LoadingState>? _loadingKey;

  // 显示 Loading
  void show(BuildContext context, {int timeoutSeconds = 30}) {
    if (_isLoading) return;
    
    _isLoading = true;
    _currentContext = context;
    _loadingKey = GlobalKey<LoadingState>();
    
    // 显示 Loading 对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => Loading(
        key: _loadingKey,
        // 添加超时后的取消按钮回调
        onTimeoutCancel: () {
          hide(dialogContext);
        },
      ),
    ).then((_) {
      // 对话框关闭时更新状态
      _isLoading = false;
      _cancelTimeout();
      _currentContext = null;
      _loadingKey = null;
    });
    
    // 设置超时
    _setTimeout(timeoutSeconds);
  }

  // 隐藏 Loading
  void hide(BuildContext context) {
    if (!_isLoading) return;
    
    _isLoading = false;
    _cancelTimeout();
    
    // 安全地关闭对话框
    try {
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('Error closing loading dialog: $e');
      // 尝试使用保存的context
      if (_currentContext != null) {
        try {
          if (Navigator.of(_currentContext!).canPop()) {
            Navigator.of(_currentContext!).pop();
          }
        } catch (e) {
          debugPrint('Error closing loading dialog with saved context: $e');
        }
      }
    }
  }

  // 设置超时
  void _setTimeout(int seconds) {
    _cancelTimeout();
    _timeoutTimer = Timer(Duration(seconds: seconds), () {
      debugPrint('Loading timeout after $seconds seconds');
      // 超时后不自动关闭，而是显示取消按钮
      if (_isLoading && _loadingKey != null && _loadingKey!.currentState != null) {
        // 直接调用 Loading 组件的状态方法显示取消按钮
        _loadingKey!.currentState!.showTimeoutButton();
      }
    });
  }

  // 取消超时
  void _cancelTimeout() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }
}