class Config{
  // static const bool _showGuide=true;
  static const String fontFamily="Poppins-Regular";
  static const String _mainPage="main";
  static const String _folderPage="folder";
  static const String _draftPage="draft";
  static const List<double> _heights=[130,150,180,200];
  static String imageNodataLine = "images/nodata_line.png";
  static final List<List<String>> _assetLists=[
    [
      "images/launch/1.png",
      "images/launch/2.png",
      "images/launch/3.png",
      "images/launch/4.png",
      "images/launch/5.png",
      "images/launch/6.png",
      "images/launch/7.png",
      "images/launch/8.png"
    ],
    [
      "images/launch/1.png",
      "images/launch/2.png",
      "images/launch/3.png",
      "images/launch/4.png",
      "images/launch/5.png",
      "images/launch/6.png",
      "images/launch/7.png",
      "images/launch/8.png",
      "images/launch/9.png",
      "images/launch/10.png",
      "images/launch/11.png",
      "images/launch/12.png",
      "images/launch/13.png",
      "images/launch/14.png",
      "images/launch/15.png"
    ]
  ];

  static getAssertList(int index){
    if(index<_assetLists.length){
      return _assetLists[index];
    }
    return [];
  }

  static String get mainPage => _mainPage;
  static String get folderPage => _folderPage;
  static String get draftPage => _draftPage;
  static List<double> get heights => _heights;
}
