import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'purchase_view_v1.dart';
import 'purchase_view_v2.dart';
import 'purchase_view_v3.dart';
import 'purchase_view_v4.dart';
import 'purchase_view_v5.dart';

/// 购买页面管理器，用于根据AB测试标识加载不同版本的购买页面
class PurchaseManager {  
  // AB测试标识，1表示版本1，2表示版本2，3表示版本3
  static int _payFlag = 3;
  
  /// 获取当前AB测试标识
  static int get payFlag => _payFlag;
  
  /// 设置AB测试标识
  static set payFlag(int value) {
    _payFlag = value;
    _savePayFlag(value);
  }
  
  /// 初始化AB测试标识
  /// 如果之前已经初始化过，则使用保存的值
  /// 否则随机生成一个值并保存
  static Future<void> initPayFlag() async {
    // final prefs = await SharedPreferences.getInstance();
    // final savedPayFlag = prefs.getInt('pay_flag');
    
    // if (savedPayFlag != null) {
    //   _payFlag = savedPayFlag;
    // } else {
      // 随机生成3-5
      _payFlag = Random().nextInt(2) + 3;
      // await _savePayFlag(_payFlag);
    // }
    debugPrint('PurchaseManager: PayFlag initialized to $_payFlag');
  }
  
  /// 保存AB测试标识到SharedPreferences
  static Future<void> _savePayFlag(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('pay_flag', value);
  }
  
  /// 获取对应版本的购买页面
  /// [payFlag] AB测试标识
  /// [products] 产品列表
  /// [userUuid] 用户UUID
  /// [onBuyFailed] 购买失败回调
  /// [onBuySucceed] 购买成功回调
  /// [context] 构建上下文，用于获取本地化字符串
  static Widget getPurchaseView({
    required int payFlag,
    required List products,
    required String? userUuid,
    required Function(String?) onBuyFailed,
    required Function() onBuySucceed,
    required BuildContext context,
  }) {
    // 使用映射表存储不同版本对应的视图类
    final viewMap = {
      1: PurchaseViewV1.new,
      2: PurchaseViewV2.new,
      3: PurchaseViewV3.new,
      4: PurchaseViewV4.new,
      5: PurchaseViewV5.new,
    };
    
    // 获取对应的构造函数，如果不存在则默认使用V3
    final viewConstructor = viewMap[payFlag] ?? PurchaseViewV3.new;
    
    // 使用构造函数创建视图实例
    return viewConstructor(
      products: products,
      userUuid: userUuid,
      onBuyFailed: onBuyFailed,
      onBuySucceed: onBuySucceed,
    );
  }
  
  /// 获取对应版本的背景颜色
  /// [payFlag] AB测试标识，1表示版本1，2表示版本2，3表示版本3
  static Color getBackgroundColor(int payFlag) {
    final colorMap = {
      1: PurchaseViewV1.getBackgroundColor,
      2: PurchaseViewV2.getBackgroundColor,
      3: PurchaseViewV3.getBackgroundColor,
      4: PurchaseViewV4.getBackgroundColor,
      5: PurchaseViewV5.getBackgroundColor,
    };
    
    // 获取对应的颜色函数，如果不存在则默认使用V3
    final colorGetter = colorMap[payFlag] ?? PurchaseViewV3.getBackgroundColor;
    
    return colorGetter();
  }
  
  /// 获取本地化的错误消息
  /// [errorCode] 错误代码
  /// [context] 构建上下文
  // static String getLocalizedErrorMessage(String errorCode, BuildContext context) {
  //   final l10n = AppLocalizations.of(context)!;
    
  //   // 根据错误代码返回对应的本地化错误消息
  //   switch (errorCode) {
  //     case 'purchase_cancelled':
  //       return l10n.purchaseCancelled;
  //     case 'network_error':
  //       return l10n.networkError;
  //     case 'product_not_available':
  //       return l10n.productNotAvailable;
  //     default:
  //       return l10n.unknownError;
  //   }
  // }
}