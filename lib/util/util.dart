// Dart core imports
import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;

// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

// Third party packages
// import 'package:camera/camera.dart';
import 'package:gal/gal.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

// App imports
import '../widgets/loading.dart';
import '../widgets/transparentoute.dart';
import '../widgets/toast_widget.dart';

class Util {
  // static List<CameraDescription> _cameras = [];
  static BuildContext? _mainContext;
  static Map<String, dynamic> viewIds = {};
  static final GlobalKey _globalKey = GlobalKey();
  static GlobalKey get globalKey => _globalKey;

  static String? _cachePath;
  static String? _appPath;

  static initPath() async {
    _cachePath = (await getTemporaryDirectory()).path;
    _appPath = (await getApplicationDocumentsDirectory()).path;
  }

  static getCachePath() {
    return _cachePath;
  }

  static String? get appPath => _appPath;

  // static initCamera() async {
  //   _cameras = await availableCameras();
  // }

  // static List<CameraDescription> get cameras => _cameras;

  static setMainContext(BuildContext? context) {
    _mainContext = context;
  }

  static BuildContext? get mainContext => _mainContext;

  static timeStampToDate(timeStamp) {
    if (timeStamp.runtimeType.toString() == "String") {
      timeStamp = int.parse(timeStamp);
    }
    var date = DateTime.fromMillisecondsSinceEpoch(timeStamp);
    String str = DateFormat('HH:mm MM-dd-yyyy').format(date);
    return str;
  }

  static getDateToENDate(String date) {
    DateTime? dt = DateTime.tryParse(date);
    if (dt != null) {
      int timestamp = dt.millisecondsSinceEpoch;
      return timeStampToDate(timestamp);
    } else {
      return date;
    }
  }

  static bool pageIsExist(String pageName){
    return viewIds[pageName]!=null;
  }

  static navigatorPush(view) async {
    if (mainContext == null) return;
    
    String name = view.runtimeType.toString();
    // 先检查并清理同名路由
    if (viewIds[name] != null) {
      viewIds.remove(name);
    }
    
    MaterialPageRoute router = MaterialPageRoute(
      builder: (context) => view,
      settings: RouteSettings(name: name),
    );
    
    viewIds[name] = router;
    if (mainContext!.mounted) {
      await Navigator.push(mainContext!, router).then((_) {
        // 路由退出时清理 viewIds
        viewIds.remove(name);
      });
    }
  }

  static navigatorReplace(view) async {
    if (mainContext == null || !mainContext!.mounted) return;
    
    String name = view.runtimeType.toString();
    // 清除已存在的路由记录
    if (viewIds[name] != null) {
      viewIds.remove(name);
    }
    
    MaterialPageRoute router = MaterialPageRoute(
      builder: (context) => view,
      settings: RouteSettings(name: name),
    );
    
    viewIds[name] = router;
    await Navigator.pushReplacement(mainContext!, router).then((_) {
      // 路由退出时清理 viewIds
      viewIds.remove(name);
    });
  }

  static navigatorTransparentPush(view) async {
    if (mainContext == null || !mainContext!.mounted) return;
    
    String name = view.runtimeType.toString();
    // 如果存在旧的路由，清理掉
    if (viewIds[name] != null) {
      viewIds.remove(name);
    }
    
    TransparentRoute router = TransparentRoute(
      name: name,
      builder: (context) => view,
    );
    
    viewIds[name] = router;
    await Navigator.push(mainContext!, router).then((_) {
      // 路由退出时清理 viewIds
      viewIds.remove(name);
    });
  }

  static closeView(var view, {String? name, bool? showAni = true}) async {
    if (mainContext == null || !mainContext!.mounted) return;
    
    if (view != null) {
      name = view.runtimeType.toString();
    }
    
    if (name != null && viewIds[name] != null) {
      if (showAni == true) {
        try {
          Navigator.pop(mainContext!);
        } catch (e) {
          debugPrint("Pop error: $e");
        }
      } else {
        Navigator.removeRoute(mainContext!, viewIds[name]);
      }
      viewIds.remove(name);
    }
  }

  // 添加一个标志来跟踪 loading 状态
  static bool _isLoadingShown = false;
  static Timer? _loadingTimer; // 添加计时器以确保loading不会无限显示

  static showLoading() {
    if (_isLoadingShown) {
      debugPrint("showLoading: loading is already shown");
      return;
    }
    
    _isLoadingShown = true;
    
    // 设置安全计时器，确保loading不会无限显示
    _loadingTimer?.cancel();
    _loadingTimer = Timer(const Duration(seconds: 10), () {
      debugPrint("Loading timeout after 10 seconds, force closing");
      closeLoading();
    });
    
    // 使用 Future.microtask 确保在下一个事件循环中执行
    Future.microtask(() {
      if (mainContext != null && mainContext!.mounted) {
        // 如果存在旧的 loading 视图，先关闭它
        if (viewIds["LoadingView"] != null) {
          debugPrint("showLoading: closing existing LoadingView");
          closeView(null, name: "LoadingView", showAni: false);
        }
        
        debugPrint("showLoading: showing new LoadingView");
        navigatorTransparentPush(const Loading()).then((_) {
          // 当 loading 被手动关闭时，更新状态
          _isLoadingShown = false;
          _loadingTimer?.cancel();
          _loadingTimer = null;
        });
      } else {
        _isLoadingShown = false;
        _loadingTimer?.cancel();
        _loadingTimer = null;
      }
    });
  }

  static closeLoading() {
    _loadingTimer?.cancel();
    _loadingTimer = null;
    
    if (!_isLoadingShown) {
      debugPrint("closeLoading: loading is not shown");
      return;
    }
    
    _isLoadingShown = false;
    
    // 使用 Future.microtask 确保在下一个事件循环中执行
    Future.microtask(() {
      if (mainContext != null && mainContext!.mounted) {
        if (viewIds["LoadingView"] != null) {
          debugPrint("closeLoading: closing LoadingView");
          closeView(null, name: "LoadingView", showAni: false);
        } else {
          debugPrint("closeLoading: LoadingView not found in viewIds");
        }
      } else {
        debugPrint("closeLoading: mainContext is null or not mounted");
      }
    });
  }

  static popNull(var view) {
    String name = view.runtimeType.toString();
    viewIds[name] = null;
  }

  // static Future<void> getZipFolder(String folderName) async {
  //
  //   Uint8List? data;
  //   File folderFile=File("${await Util.getRootPath()}/$folderName");
  //   if(folderFile.existsSync()){
  //     data=folderFile.readAsBytesSync();
  //   }
  //   Draft().createFolder(folderName, "en",folderData: data);
  // }

  static getImageName(String path) {
    String name = path.substring(path.lastIndexOf("#") + 1);
    int index1 = name.lastIndexOf("/");
    String path1 = name.substring(index1 + 1);

    int index2 = path1.indexOf(".");
    if (index2 == -1) {
      index2 = path1.length;
    }
    String folderName = path1.substring(0, index2);
    return folderName;
  }

  /// Convert JSON string to Map
  static Map<String, dynamic> jsonToMap(String jsonString) {
    try {
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint("JSON to Map error: $e");
      return {};
    }
  }
  
  /// Convert JSON string to List
  static List<dynamic> jsonToList(String jsonString) {
    try {
      return json.decode(jsonString) as List<dynamic>;
    } catch (e) {
      debugPrint("JSON to List error: $e");
      return [];
    }
  }

  static saveImg() async {
    RenderRepaintBoundary? boundary = _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary?;
    if (boundary != null) {
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
      await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        Uint8List picBytes = byteData.buffer.asUint8List();
        try {
          await Gal.putImageBytes(picBytes);
          ToastWidget.showSuccess("Download succeed!");
        } catch (e) {
          debugPrint("Error saving image: $e");
          ToastWidget.showError("Download failed!");
        }
      }
    } else {
      ToastWidget.showError("Download failed!");
    }
  }

  /// 检查图像数据是否为有效的图像格式
  static bool isValidImageFormat(Uint8List bytes) {
    if (bytes.length < 4) return false;
    
    // 检查常见图像格式的魔数
    // JPEG: FF D8 FF 
    if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return true;
    }
    
    // PNG: 89 50 4E 47
    if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
      return true;
    }
    
    // GIF: 47 49 46 38
    if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x38) {
      return true;
    }
    
    // WebP: 52 49 46 46 (RIFF) + 8字节 + 57 45 42 50 (WEBP)
    if (bytes.length >= 12 && 
        bytes[0] == 0x52 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x46 &&
        bytes[8] == 0x57 && bytes[9] == 0x45 && bytes[10] == 0x42 && bytes[11] == 0x50) {
      return true;
    }
    
    // BMP: 42 4D
    if (bytes[0] == 0x42 && bytes[1] == 0x4D) {
      return true;
    }
    
    // 检查是否为SVG (通常以<?xml或<svg开头)
    if (bytes.length > 5) {
      // 检查SVG文件头
      String header = String.fromCharCodes(bytes.sublist(0, bytes.length > 100 ? 100 : bytes.length));
      if (header.contains('<svg') || header.contains('<?xml') && header.contains('<svg')) {
        return false; // 排除SVG格式
      }
    }
    
    return false;
  }
}
