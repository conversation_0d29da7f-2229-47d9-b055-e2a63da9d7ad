import 'dart:async';
import 'package:flutter/material.dart';
import 'package:imtrans/pages/home/<USER>';
import 'package:imtrans/pages/browser/multi_browser_page.dart';
import 'package:imtrans/pages/home/<USER>';
import 'package:imtrans/pages/home/<USER>';
import 'package:imtrans/pages/home/<USER>';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:imtrans/controllers/draft_controller.dart';
import 'package:imtrans/controllers/product_controller.dart';
import 'package:imtrans/models/product_model.dart';
import 'package:imtrans/pages/purchase/vip.dart';
import 'package:imtrans/util/dialog_manager.dart';
import 'package:imtrans/util/util.dart';
import 'package:imtrans/util/theme_manager.dart'; 
import 'package:cached_network_image/cached_network_image.dart';
import 'package:imtrans/widgets/breakall.dart';
import 'package:imtrans/widgets/user_drawer.dart';
import 'package:imtrans/services/manga_sites.dart';
import 'package:imtrans/widgets/common/common_widgets.dart';
import 'package:imtrans/services/account.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/services/event_log.dart';
import 'package:imtrans/services/bookmark_service.dart';
import 'dart:math';

/// 首页站点数据类，用于统一处理书签和推荐站点
class HomePageSite {
  final String name;
  final String url;
  final String? icon;
  final bool isBookmark;
  final bool faviconCached;

  HomePageSite({
    required this.name,
    required this.url,
    this.icon,
    this.isBookmark = false,
    this.faviconCached = false,
  });

  /// 从书签数据创建
  factory HomePageSite.fromBookmark(BookmarkItem bookmark) {
    return HomePageSite(
      name: bookmark.title.isNotEmpty ? bookmark.title : bookmark.url,
      url: bookmark.url,
      icon: bookmark.favicon,
      isBookmark: true,
      faviconCached: bookmark.faviconCached,
    );
  }

  /// 从推荐站点创建
  factory HomePageSite.fromMangaSite(MangaSite site) {
    return HomePageSite(
      name: site.name,
      url: site.url,
      icon: site.icon,
      isBookmark: false,
    );
  }
}

class IndexPage extends StatefulWidget {
  const IndexPage({Key? key}) : super(key: key);

  @override
  State<IndexPage> createState() => _IndexPageState();
}

class _IndexPageState extends State<IndexPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isFocused = false;
  
  // 添加产品控制器和相关变量
  final ProductController _productController = ProductController();
  List<Product> _recentProducts = [];
  List<HomePageSite> _homePageSites = [];
  bool _isLoading = false;
  StreamSubscription? _productSubscription; // 产品更新监听
  double _width = 0.0; 
  late VoidCallback _themeListener;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _searchFocusNode.addListener(() {
      setState(() {
        _isFocused = _searchFocusNode.hasFocus;
      });
    });
    
    // 保存监听器引用
    _themeListener = () {
      if (mounted) {
        setState(() {});
      }
    };
    
    // 添加监听器
    ThemeManager().addListener(_themeListener);
    
    // 添加账户信息更新监听
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      Util.setMainContext(context);
      
      // 获取 Account 实例并添加监听器
      final account = await Account.instance;
      _accountListener = () {
        if (mounted) {
          debugPrint('Account update notification received in IndexPage');
          setState(() {});
          // 账户信息更新后刷新产品列表
          _loadRecentProducts(forceRefresh: true);
        }
      };
      account.addListener(_accountListener!);
    });

    // 加载最近文件
    _loadRecentProducts();
    
    // 加载首页站点（书签优先，否则推荐站点）
    _loadHomePageSites();
    
    // 添加产品更新监听
    _productSubscription = _productController.onProductsUpdated.listen((_) {
      debugPrint('Product update notification received in IndexPage');
      Future.delayed(Duration(milliseconds: 100), () {
        if (mounted) {
          _loadRecentProducts(forceRefresh: true);
        }
      });
    });
  }

  // 加载最近产品
  Future<void> _loadRecentProducts({bool forceRefresh = false}) async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final products = await _productController.list(
        page: 1,
        pageSize: 10, 
        forceRefresh: forceRefresh // 强制刷新以获取最新数据
      );
      
      if (mounted && products != null) {
        setState(() {
          _recentProducts = products;
        });
      }
    } catch (e) {
      debugPrint('Error loading recent products: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 加载首页站点（书签优先，否则推荐站点）
  Future<void> _loadHomePageSites() async {
    try {
      // 首先尝试加载书签
      final bookmarks = await BookmarkService.instance.getRecentBookmarks(limit: 4);
      final mangaSites = await MangaSitesRepository.getAllSites();

      List<HomePageSite> sites = [];

      if (bookmarks.length >= 4) {
        // 如果有4个或更多书签，只显示4个最新书签
        sites = bookmarks
            .take(4)
            .map((bookmark) => HomePageSite.fromBookmark(bookmark))
            .toList();
        debugPrint('显示4个书签');
      } else if (bookmarks.isNotEmpty) {
        // 如果有1-3个书签，显示所有书签 + 填充推荐站点到4个
        sites = bookmarks
            .map((bookmark) => HomePageSite.fromBookmark(bookmark))
            .toList();

        // 计算需要填充的推荐站点数量
        final remainingSlots = 4 - bookmarks.length;

        if (mangaSites.isNotEmpty && remainingSlots > 0) {
          // 随机选择推荐站点填充剩余位置
          final random = Random();
          final shuffledSites = List<MangaSite>.from(mangaSites)..shuffle(random);
          final recommendedSites = shuffledSites
              .take(remainingSlots)
              .map((site) => HomePageSite.fromMangaSite(site))
              .toList();

          sites.addAll(recommendedSites);
        }

        debugPrint('显示${bookmarks.length}个书签 + ${remainingSlots}个推荐站点');
      } else {
        // 如果没有书签，显示4个随机推荐站点
        if (mangaSites.isNotEmpty) {
          final random = Random();
          final shuffledSites = List<MangaSite>.from(mangaSites)..shuffle(random);
          sites = shuffledSites
              .take(4)
              .map((site) => HomePageSite.fromMangaSite(site))
              .toList();
        }
        debugPrint('显示4个推荐站点');
      }

      setState(() {
        _homePageSites = sites;
      });
    } catch (e) {
      debugPrint('Error loading home page sites: $e');
      // 如果出错，尝试加载默认推荐站点
      try {
        final mangaSites = await MangaSitesRepository.getAllSites();
        setState(() {
          _homePageSites = mangaSites
              .take(4)
              .map((site) => HomePageSite.fromMangaSite(site))
              .toList();
        });
      } catch (fallbackError) {
        debugPrint('加载默认推荐站点也失败: $fallbackError');
        setState(() {
          _homePageSites = [];
        });
      }
    }
  }

  // 保存 Account 监听器引用
  VoidCallback? _accountListener;
  
  @override
  void dispose() {
    _searchFocusNode.dispose();
    _searchController.dispose();
    _productSubscription?.cancel();
    ThemeManager().removeListener(_themeListener);
    
    // 移除 Account 监听器
    if (_accountListener != null) {
      Account.instance.then((account) {
        account.removeListener(_accountListener!);
      });
    }
    
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 设置主上下文，确保Util.navigatorPush可以正常工作
    Util.setMainContext(context);
    DialogManager.instance.setMainContext(context);
  }

  late ThemeColors themeColors;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    _width = size.width; 
    themeColors = ThemeManager.currentTheme;
    
    return Scaffold(
      key: _scaffoldKey,
      drawer: const UserDrawer(), // 侧边栏
      backgroundColor: themeColors.backgroundColor, 
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(
              20.0, 
              MediaQuery.of(context).padding.top + 0.0,
              20.0, 
              MediaQuery.of(context).padding.bottom + 20.0
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部导航栏
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 0), 
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 左侧用户头像按钮
                      Container(
                        width: 40,
                        height: 40,
                        child: IconButton(
                          padding: EdgeInsets.zero, // 移除IconButton的内边距
                          icon: Image.asset(ThemeManager.getImagePath('btn_user')),
                          onPressed: () {
                            // 打开侧边栏
                            _scaffoldKey.currentState?.openDrawer();
                          },
                        ),
                      ),
                      // 中间标题
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'Imtrans\n',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: themeColors.textColor,
                                height: 1.4 
                              ),
                            ),
                            TextSpan(
                              text: AppLocalizations.of(context)!.appSubtitle,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                                height: 1.2, 
                              ),
                            ),
                          ],
                        ),
                      ),
                      // 右侧草稿箱按钮
                      Container(
                        width: 40,
                        height: 40,
                        child: IconButton(
                          padding: EdgeInsets.zero,
                          icon: Image.asset(ThemeManager.getImagePath('btn_draft')),
                          onPressed: () {
                            // 跳转到草稿箱页面
                            Util.navigatorPush(const DraftView());
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 功能按钮区域
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  child: Container(
                    width: double.infinity,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 导入文件按钮
                        FutureBuilder<Widget>(
                          future: _buildFeatureButton(
                            icon: Image.asset('images/icons/btn_file.png'),
                            label: AppLocalizations.of(context)!.importFile,
                            requireVip: true,
                            onTap: () async {
                              final result = await FilePicker.platform.pickFiles(
                                type: FileType.custom,
                                allowedExtensions: ['zip', 'jpg', 'jpeg', 'png', 'webp'],
                              );
                              
                              if (result != null) {
                                String filePath = result.files.single.path!;
                                debugPrint('Selected file: $filePath');
                                final draftController = DraftController();
                                await draftController.initialized;
                                await draftController.handleSelection(filePath);
                                Util.navigatorPush(const DraftView());
                              }
                            },
                          ),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return snapshot.data!;
                            }
                            return Container(
                              width: _width / 3 - 24,
                              height: 100,
                            ); // 返回一个占位容器
                          },
                        ),
                        // 相册按钮
                        FutureBuilder<Widget>(
                          future: _buildFeatureButton(
                            icon: Image.asset('images/icons/btn_album.png'),
                            label: AppLocalizations.of(context)!.album,
                            onTap: () async {
                              // 直接从相册选择图片
                              final ImagePicker picker = ImagePicker();
                              final List<XFile> images = await picker.pickMultiImage(
                                limit: 10,
                                requestFullMetadata: false
                              );
                              
                              if (images.isNotEmpty) {
                                // 记录相册上传事件
                                await EventLogService.logPhotoUpload(count: images.length);
                                debugPrint('Selected ${images.length} images');
                                // 调用DraftController处理图片
                                final draftController = DraftController();
                                // 等待DraftController初始化完成
                                await draftController.initialized;
                                await draftController.handleSelection(images);
                                Util.navigatorPush(const DraftView());
                              }
                            },
                          ),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return snapshot.data!;
                            }
                            return Container(
                              width: _width / 3 - 24,
                              height: 100,
                            );
                          },
                        ),
                        // 相机按钮
                        FutureBuilder<Widget>(
                          future: _buildFeatureButton(
                            icon: Image.asset('images/icons/btn_camera.png'),
                            label: AppLocalizations.of(context)!.camera,
                            onTap: () async {
                              // 直接打开相机拍照
                              try {
                                final ImagePicker picker = ImagePicker();
                                final XFile? photo = await picker.pickImage(source: ImageSource.camera);
                                
                                // 记录拍照事件
                                await EventLogService.logPhotoCapture(success: photo != null);
                                if (photo != null) {
                                  debugPrint('Captured image: ${photo.path}');
                                  // 调用DraftController处理拍照结果
                                  final draftController = DraftController();
                                  // 等待DraftController初始化完成
                                  await draftController.initialized;
                                  await draftController.handleSelection([photo]);
                                  Util.navigatorPush(const DraftView()).then((_) {
                                    _loadRecentProducts();  // 从草稿箱返回时刷新列表
                                  });
                                }
                              } catch (e) {
                                debugPrint('相机拍照出错: $e');
                                // 记录拍照失败事件
                                await EventLogService.logPhotoCapture(success: false);
                                // 显示错误提示
                                // DialogManager.instance.showToast('相机启动失败，请检查相机权限');
                              }
                            },
                          ),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return snapshot.data!;
                            }
                            return Container(
                              width: _width / 3 - 24,
                              height: 100,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 10),
                
                // 从网页导入区域 
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20.0),
                  decoration: BoxDecoration(
                    color: themeColors.borderAreaBgColor,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: .05),
                        // spreadRadius: 4,
                        blurRadius: 4,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 从网页导入区域标题
                      Center(
                        child: Text(
                          AppLocalizations.of(context)!.translateWebImages,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: themeColors.textColor, 
                            fontFamily: 'Poppins-medium',
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 18),
                      
                      // 搜索框
                      Container(
                        height: 44,
                        decoration: BoxDecoration(
                          color: themeColors.inputBoxBgColor,
                          borderRadius: BorderRadius.circular(18),
                          border: Border.all(
                            color: _isFocused ? themeColors.textColor : const Color(0xffd9d9d9),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Theme(
                                data: Theme.of(context).copyWith(
                                  textSelectionTheme: TextSelectionThemeData(
                                    cursorColor: themeColors.textColor,
                                    selectionColor: themeColors.textColor.withValues(alpha: 0.3),
                                    selectionHandleColor: themeColors.textColor,
                                  ),
                                ),
                                child: TextField(
                                  controller: _searchController,
                                  focusNode: _searchFocusNode,  
                                  cursorColor: themeColors.textColor,
                                  style: TextStyle(
                                    color: themeColors.textColor,
                                    fontSize: 12,
                                  ),
                                  decoration: InputDecoration(
                                    hintText: AppLocalizations.of(context)!.enterUrl,
                                    hintStyle: TextStyle(
                                      color: themeColors.textColor.withValues(alpha: .5),
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.only(left: 16, top: -12, bottom: -12),
                                  ),
                                  onSubmitted: (value) {
                                    // 允许空值提交进入浏览器
                                    final searchText = value.trim();
                                    _openBrowser(searchText);
                                    if (searchText.isNotEmpty) {
                                      WidgetsBinding.instance.addPostFrameCallback((_) {
                                        _searchController.clear();
                                      });
                                    }
                                  },
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // 允许空地址栏进入浏览器
                                final searchText = _searchController.text.trim();
                                _openBrowser(searchText);
                                if (searchText.isNotEmpty) {
                                  _searchController.clear();
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: Image.asset(
                                  ThemeManager.getImagePath('icon_search'),
                                  width: 24,
                                  height: 24,
                                  color: _isFocused ? themeColors.textColor : Colors.grey.shade400,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // 漫画网站选项
                      _buildMangaSiteOptions(),
                    ],
                  ),
                ),
                
                const SizedBox(height: 10),
                
                // Recent Files 导航区域
                if (!_recentProducts.isEmpty) Padding(
                  padding: const EdgeInsets.only(bottom: 12, top: 6), 
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.recentTranslations,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: themeColors.textColor,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          // 跳转到完整的历史记录页面
                          Util.navigatorPush(const ProductListPage());
                        },
                        child: Row(
                          children: [
                            Text(
                              AppLocalizations.of(context)!.seeAll,
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                                color: themeColors.textColor,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Image.asset(
                              ThemeManager.getImagePath('icon_next'),
                              width: 16,
                              height: 16,
                              color: themeColors.textColor,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // 最近文件列表
                _isLoading
                  ? Center(
                      child: Container(
                        padding: EdgeInsets.only(top: 80),
                        child: CircularProgressIndicator(),
                      )
                    )
                  : _recentProducts.isEmpty
                    ? Center(
                        child: Material(
                          color: Colors.transparent,
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.only(top: 80),
                            alignment: Alignment.center,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Image.asset(
                                  ThemeManager.getImagePath('nodata'),
                                  width: 80,
                                  fit: BoxFit.fitWidth,
                                ),
                                const SizedBox(height: 20),
                                Text(
                                  AppLocalizations.of(context)!.noTranslationHistory,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: themeColors.textColor,
                                    fontWeight: FontWeight.w700
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Image.asset(
                                  'images/nodata_line.png',
                                  width: 200,
                                  height: 14,
                                  fit: BoxFit.fill,
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    : GridView.builder(
                        padding: const EdgeInsets.only(bottom: 20),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.85,
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 10,
                        ),
                        itemCount: _recentProducts.length,
                        itemBuilder: (context, index) {
                          // 根据产品类型选择不同的构建方法
                          return _recentProducts[index].images.length > 1
                            ? _buildRecentFolderItem(_recentProducts[index], index)
                            : _buildRecentFileItem(_recentProducts[index], index);
                        },
                      ),
              ],
            ),
          ),
          // 添加顶部遮罩
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).padding.top,
            child: Container(
              color: themeColors.backgroundColor,
            ),
          ),
        ],
      ),
    );
  }

  // 首页站点选项部分（书签优先，否则推荐站点）
  Widget _buildMangaSiteOptions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: _homePageSites.asMap().entries.map((entry) {
        HomePageSite site = entry.value;

        return Expanded(
          child: GestureDetector(
            onTap: () => _openBrowser(site.url),
            child: SizedBox(
              height: 90, // 设置固定高度以确保对齐
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: themeColors.recommendedSitesBackgroundColor,
                  shape: BoxShape.circle,
                  // border: Border.all(
                  //   color: Color(0xffeeeeee),
                  //   width: 1,
                  // )
                ),
                child: Center(
                  child: ClipRRect(
                    child: SizedBox(
                      width: 26,
                      height: 26,
                      child: _buildSiteIcon(site),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                site.name,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: themeColors.textColor,
                ),
              ),
            ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  // 构建站点图标
  Widget _buildSiteIcon(HomePageSite site) {
    if (site.isBookmark) {
      // 对于书签，使用BookmarkService的统一图标构建逻辑
      return BookmarkService.instance.buildBookmarkIcon(
        site.name,
        site.url,
        site.icon,
        26,
        faviconCached: site.faviconCached,
      );
    }

    // 对于推荐站点，使用原有逻辑
    final hasIcon = site.icon != null && site.icon!.isNotEmpty;

    if (hasIcon) {
      // 有图标时显示图标
      if (site.icon!.startsWith('http')) {
        // 网络图标
        return Image.network(
          site.icon!,
          width: 26,
          height: 26,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(site.name),
        );
      } else {
        // 本地资源图标
        return Image.asset(
          site.icon!,
          width: 26,
          height: 26,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(site.name),
        );
      }
    } else {
      // 没有图标时显示回退图标
      return _buildFallbackIcon(site.name);
    }
  }

  // 构建回退图标（彩色背景 + 首字母）
  Widget _buildFallbackIcon(String name) {
    final firstLetter = name.isNotEmpty ? name[0].toUpperCase() : '?';
    final color = _getColorForSite(name);

    return Container(
      width: 26,
      height: 26,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
      ),
      child: Center(
        child: Text(
          firstLetter,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  // 根据网站名称首字母获取颜色
  Color _getColorForSite(String siteName) {
    if (siteName.isEmpty) return const Color(0xFFFFDE00);

    final firstLetter = siteName[0].toUpperCase();

    switch (firstLetter) {
      case 'A':
      case 'B':
      case 'C':
        return const Color(0xFFFFDE00); // 黄色
      case 'D':
      case 'E':
      case 'F':
        return const Color(0xFFC0E700); // 柠檬绿
      case 'G':
      case 'H':
      case 'I':
        return const Color(0xFF2FC27E); // 绿色
      case 'J':
      case 'K':
      case 'L':
        return const Color(0xFF728FFE); // 蓝色
      case 'M':
      case 'N':
      case 'O':
        return const Color(0xFFFFB500); // 橙色
      case 'P':
      case 'Q':
      case 'R':
        return const Color(0xFFFF7100); // 红橙色
      case 'S':
      case 'T':
      case 'U':
        return const Color(0xFF00DFE7); // 青色
      case 'V':
      case 'W':
      case 'X':
        return const Color(0xFFED72AA); // 粉色
      case 'Y':
      case 'Z':
        return const Color(0xFF8668FF); // 紫色
      default:
        return const Color(0xFFFFDE00); // 默认黄色
    }
  }

  // 功能按钮构建方法
  Future<Widget> _buildFeatureButton({
    required Widget icon,
    required String label,
    required VoidCallback onTap,
    bool requireVip = false,
  }) async {
    final account = await Account.instance;
    final bool isVip = account.isVip;
    final bool isEnabled = !requireVip || isVip;

    return GestureDetector(
      onTap: isEnabled ? onTap : () {
        // 如果功能未启用,跳转到VIP页面
        Util.navigatorTransparentPush(const Vip());
      },
      child: Stack(
        children: [
          Container(
            width: _width / 3 - 24,
            height: 92,
            padding: const EdgeInsets.only(top: 14, left: 12, right: 12, bottom: 0),
            decoration: BoxDecoration(
              color: themeColors.mineButtonBackgroundColor,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: .05),
                  blurRadius: 4,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  child: icon,
                ),
                const SizedBox(height: 6),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12, 
                    fontWeight: FontWeight.w500,
                    height: 12/12,
                    color: themeColors.textColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          if (!isEnabled) ...[
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: ThemeManager.currentTheme.backgroundColor.withValues(alpha: .6),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                child: Opacity(
                  opacity: 0.7,
                  child: ImageWidget.proIconS(),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 打开浏览器方法
  Future<void> _openBrowser(String inputText) async {
    final trimmedInput = inputText.trim();

    debugPrint('打开浏览器: ${trimmedInput.isEmpty ? "无URL（恢复保存的tab）" : "URL: $trimmedInput"}');

    // 使用 Util 封装的导航方法替代直接使用 Navigator
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MultiBrowserPage(
          // 当输入为空时传递null，让浏览器恢复保存的tab
          // 当输入不为空时传递URL，创建新tab或重用空白tab
          initialUrl: trimmedInput.isNotEmpty ? trimmedInput : null,
        ),
      ),
    );

    // 如果有返回URL，则更新搜索框
    if (result != null && result is String && result.isNotEmpty) {
      setState(() {
        _searchController.text = result;
      });
    }
  }

  // 构建最近文件夹项
  Widget _buildRecentFolderItem(Product product, int index) {
    double w = (_width - 48) / 2; // 考虑到GridView的crossAxisSpacing
    double h = 220;
    double imageWidth = w - 20; // 左右各留10px边距
    double imageHeight = h * 0.7;
    
    return GestureDetector(
      onTap: () {
        Util.navigatorPush(FolderViewer(
          product: product,
          currentIndex: 0,
        ));
      },
      child: Container(
        width: w,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          color: themeColors.mineButtonBackgroundColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: ThemeManager.currentTheme.itemBorderColor,
              // spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // 文件夹标题部分
            Container(
              width: w,
              padding: const EdgeInsets.only(top: 8, left: 8, right: 8, bottom: 4),
              child: BreakAllText(
                product.name ?? "",
                style: TextStyle(
                  fontSize: 14,
                  color: themeColors.textColor,
                  height: 1.2,
                ),
                maxLines: 2,
              ),
            ),
            
            // 图片部分 - 参考list.dart的布局
            Expanded(
              child: Container(
                alignment: Alignment.center,
                width: w,
                child: Container(
                  width: imageWidth,
                  height: imageHeight,
                  clipBehavior: Clip.hardEdge,
                  alignment: Alignment.center,
                  margin: const EdgeInsets.only(bottom: 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Hero(
                    tag: 'index_home_folder_${product.productId}_$index',
                    child: product.coverUrl != null || product.images.isNotEmpty
                      ? CachedNetworkImage(
                          fadeInDuration: Duration.zero,
                          imageUrl: product.coverUrl ?? product.images[0].url ?? "",
                          width: imageWidth,
                          height: imageHeight,
                          fit: BoxFit.cover,
                          memCacheWidth: (imageWidth * MediaQuery.of(context).devicePixelRatio).round(),
                          memCacheHeight: (imageHeight * MediaQuery.of(context).devicePixelRatio).round(),
                          placeholder: (context, url) => ImageWidget.placeholder(imageWidth, imageHeight),
                          errorWidget: (context, url, obj) => ImageWidget.placeholder(imageWidth, imageHeight),
                        )
                      : ImageWidget.placeholder(imageWidth, imageHeight),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // 构建最近文件项
  Widget _buildRecentFileItem(Product product, int index) {
    return GestureDetector(
      onTap: () {
        Util.navigatorPush(ImageViewer(
          currentIndex: index,
          productController: _productController,
        ));
      },
      child: Container(
        decoration: BoxDecoration(
          color: themeColors.mineButtonBackgroundColor, 
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withValues(alpha: .02),
              // spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        clipBehavior: Clip.hardEdge,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片部分
            Expanded(
              child: Container(
                width: double.infinity,
                color: Colors.grey.shade100,
                child: Hero(
                  tag: 'index_home_file_${product.productId}_$index',
                  child: product.coverUrl != null || product.images.isNotEmpty
                    ? CachedNetworkImage(
                        fadeInDuration: Duration.zero,
                        imageUrl: product.coverUrl ?? product.images[0].url ?? "",
                        fit: BoxFit.cover,
                        placeholder: (context, url) => ImageWidget.placeholder(double.infinity, double.infinity),
                        errorWidget: (context, url, obj) => ImageWidget.placeholder(double.infinity, double.infinity),
                      )
                    : ImageWidget.placeholder(double.infinity, double.infinity),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}