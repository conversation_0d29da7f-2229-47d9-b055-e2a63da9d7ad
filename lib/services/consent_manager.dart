import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'event_log.dart';

/// 用户同意管理器
/// 
/// 管理用户对Firebase Analytics数据收集的同意状态
class ConsentManager extends ChangeNotifier {
  static ConsentManager? _instance;
  static ConsentManager get instance => _instance ??= ConsentManager._();
  
  ConsentManager._();

  static const String _consentKey = 'firebase_analytics_consent';
  static const String _consentAskedKey = 'firebase_analytics_consent_asked';
  
  bool _hasConsent = false;
  bool _hasAskedForConsent = false;
  
  /// 用户是否已同意数据收集
  bool get hasConsent => _hasConsent;
  
  /// 是否已询问过用户同意
  bool get hasAskedForConsent => _hasAskedForConsent;
  
  /// 初始化同意管理器
  static Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final instance = ConsentManager.instance;
    
    instance._hasConsent = prefs.getBool(_consentKey) ?? false;
    instance._hasAskedForConsent = prefs.getBool(_consentAskedKey) ?? false;
    
    // 如果用户已经做出选择，应用设置
    if (instance._hasAskedForConsent) {
      await EventLogService.setAnalyticsConsent(isGranted: instance._hasConsent);
    }
    
    debugPrint('用户同意状态初始化: 已询问=${instance._hasAskedForConsent}, 已同意=${instance._hasConsent}');
  }
  
  /// 设置用户同意状态
  Future<void> setConsent({required bool isGranted}) async {
    final prefs = await SharedPreferences.getInstance();
    
    _hasConsent = isGranted;
    _hasAskedForConsent = true;
    
    await prefs.setBool(_consentKey, isGranted);
    await prefs.setBool(_consentAskedKey, true);
    
    // 应用到Firebase Analytics
    await EventLogService.setAnalyticsConsent(isGranted: isGranted);
    
    notifyListeners();
    debugPrint('用户同意状态已更新: ${isGranted ? '同意' : '拒绝'}');
  }
  
  /// 重置同意状态（用于测试或重新询问）
  Future<void> resetConsent() async {
    final prefs = await SharedPreferences.getInstance();
    
    _hasConsent = false;
    _hasAskedForConsent = false;
    
    await prefs.remove(_consentKey);
    await prefs.remove(_consentAskedKey);
    
    // 禁用Firebase Analytics
    await EventLogService.setAnalyticsConsent(isGranted: false);
    
    notifyListeners();
    debugPrint('用户同意状态已重置');
  }
} 