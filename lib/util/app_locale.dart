import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

/// 应用语言管理
/// 用于管理应用界面的语言设置
class AppLocale extends ChangeNotifier {
  // 单例模式
  static final AppLocale _instance = AppLocale._internal();
  factory AppLocale() => _instance;
  
  AppLocale._internal();
  
  // 应用界面语言相关属性
  static Locale? _currentLocale;
  static const String _prefKey = 'app_ui_locale'; // SharedPreferences键名
  static bool _initialized = false; // 是否已初始化
  static bool _followSystem = true; // 是否跟随系统语言
  static const String _followSystemKey = 'follow_system_locale'; // 是否跟随系统语言的键名

  // Getters
  static Locale get currentLocale => _currentLocale ?? _getDeviceLocale();
  static bool get initialized => _initialized;
  static bool get followSystem => _followSystem;
  
  // 获取设备语言
  static Locale _getDeviceLocale() {
    final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;
    // 检查设备语言是否在支持的语言列表中
    if (AppLocalizations.supportedLocales.any((locale) => locale.languageCode == deviceLocale.languageCode)) {
      return deviceLocale;
    }
    // 默认返回英语
    return const Locale('en');
  }
  
  // 设置新的语言
  static Future<void> setLocale(Locale locale, {bool notify = true}) async {
    if (_currentLocale == locale) return;
    
    _currentLocale = locale;
    await _saveLocale();
    
    if (notify) {
      _instance.notifyListeners();
    }
  }
  
  // 设置是否跟随系统语言
  static Future<void> setFollowSystem(bool follow) async {
    if (_followSystem == follow) return;
    
    _followSystem = follow;
    await _saveFollowSystemSetting();
    
    // 如果启用跟随系统，则立即更新为系统语言
    if (follow) {
      await setLocale(_getDeviceLocale());
    }
    
    _instance.notifyListeners();
  }
  
  // 保存语言设置到SharedPreferences
  static Future<void> _saveLocale() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_prefKey, _currentLocale?.languageCode ?? 'en');
  }
  
  // 保存是否跟随系统语言的设置
  static Future<void> _saveFollowSystemSetting() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_followSystemKey, _followSystem);
  }
  
  // 从SharedPreferences加载语言设置
  static Future<void> loadLocale() async {
    if (_initialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 加载是否跟随系统语言的设置
      _followSystem = prefs.getBool(_followSystemKey) ?? true;
      
      if (_followSystem) {
        // 如果跟随系统语言，则使用系统语言
        _currentLocale = _getDeviceLocale();
      } else {
        // 否则加载保存的语言设置
        final savedLocale = prefs.getString(_prefKey);
        if (savedLocale != null) {
          _currentLocale = Locale(savedLocale);
        } else {
          _currentLocale = const Locale('en');
        }
      }
    } catch (e) {
      debugPrint("Error loading locale setting: $e");
      _currentLocale = const Locale('en');
    }
    
    _initialized = true;
    _instance.notifyListeners();
  }
  
  // 初始化语言设置
  static Future<void> initialize() async {
    if (_initialized) return;
    await loadLocale();
  }
  
  // 获取当前语言的本地化字符串
  static AppLocalizations getLocalizedStrings(BuildContext context) {
    return AppLocalizations.of(context)!;
  }
}