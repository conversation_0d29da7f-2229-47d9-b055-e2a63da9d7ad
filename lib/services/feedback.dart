import 'server_request.dart';
import 'package:imtrans/util/loading_manager.dart';
import 'package:flutter/material.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'account.dart';
import '../widgets/toast_widget.dart';

// 向服务器发送Feedback
class FeedbackService {
  Future<void> sendFeedback(String feedback, {
    String email = "",
    required Function onSuccess,
    required BuildContext context, 
    Function(String)? onError,
  }) async {
    if (feedback.isEmpty) {
      ToastWidget.showWarning(AppLocalizations.of(context)!.feedbackEmpty);
      return;
    }

    try {
      LoadingManager.instance.show(context);

      String content = feedback;
      if (email.isNotEmpty) {
        content += "\n\nEmail: $email";
      }
      // 反馈包含用户信息
      Account account = await Account.instance;
      content += "\n\nUser: uid:${account.info["uid"]}, udid:${account.udid}";
      // 包含版本信息
      String version = await _getVersionInfo();
      if (version.isNotEmpty) {
        content += "\n\nVersion: $version";
      }
      var res = await ServerRequest.postData(
        ServerRequest.feedBack,
        {"content": content},
      );

      if (res["code"] == 0) {
        ToastWidget.showSuccess(AppLocalizations.of(context)!.feedbackSuccess);
        onSuccess();
      } else {
        String errorMsg = res["msg"] ?? AppLocalizations.of(context)!.feedbackSendError;
        ToastWidget.showError(errorMsg);
        if (onError != null) {
          onError(errorMsg);
        }
      }
    } catch (error) {
      String errorMsg = AppLocalizations.of(context)!.feedbackSendError;
      ToastWidget.showError(errorMsg);
      if (onError != null) {
        onError(errorMsg);
      }
    }
    finally {
      LoadingManager.instance.hide(context);
    } 
  }

  Future<String> _getVersionInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      String version = "V${packageInfo.version}";
      int buildNumber = int.tryParse(packageInfo.buildNumber) ?? 0;

      return "$version ($buildNumber)";
    } catch (e) {
      debugPrint("获取版本信息失败: $e");
      return "";
    }
  }
}
