import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:imtrans/services/local_ocr_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('LocalOcrService', () {
    late LocalOcrService ocrService;

    setUp(() {
      ocrService = LocalOcrService();
    });

    tearDown(() {
      ocrService.dispose();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        expect(ocrService.isInitialized, false);
        await ocrService.initialize();
        expect(ocrService.isInitialized, true);
      });

      test('should throw exception when using service before initialization', () async {
        final mockImageBytes = Uint8List.fromList([1, 2, 3, 4]);
        
        expect(
          () => ocrService.extractTextFromBytes(mockImageBytes),
          throwsA(isA<OcrException>()),
        );
      });
    });

    group('Text Extraction from Bytes', () {
      setUp(() async {
        await ocrService.initialize();
      });

      test('should handle empty image bytes', () async {
        final emptyBytes = Uint8List(0);
        
        expect(
          () => ocrService.extractTextFromBytes(emptyBytes),
          throwsA(isA<OcrException>()),
        );
      });

      test('should handle invalid image bytes', () async {
        final invalidBytes = Uint8List.fromList([1, 2, 3, 4]);
        
        expect(
          () => ocrService.extractTextFromBytes(invalidBytes),
          throwsA(isA<OcrException>()),
        );
      });

      test('should return empty list for image with no text', () async {
        // This would require a real image with no text
        // For now, we'll test the error handling path
        final mockImageBytes = Uint8List.fromList(List.generate(100, (i) => i % 256));
        
        try {
          final result = await ocrService.extractTextFromBytes(mockImageBytes);
          expect(result, isA<List<OcrTextElement>>());
        } catch (e) {
          expect(e, isA<OcrException>());
        }
      });
    });

    group('Text Extraction from Path', () {
      setUp(() async {
        await ocrService.initialize();
      });

      test('should handle non-existent file path', () async {
        const nonExistentPath = '/non/existent/path.jpg';
        
        expect(
          () => ocrService.extractTextFromPath(nonExistentPath),
          throwsA(isA<OcrException>()),
        );
      });

      test('should handle invalid file path', () async {
        const invalidPath = '';
        
        expect(
          () => ocrService.extractTextFromPath(invalidPath),
          throwsA(isA<OcrException>()),
        );
      });
    });

    group('Disposal', () {
      test('should dispose properly', () async {
        await ocrService.initialize();
        expect(ocrService.isInitialized, true);
        
        ocrService.dispose();
        expect(ocrService.isInitialized, false);
      });
    });
  });

  group('OcrTextElement', () {
    test('should create instance with required properties', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );
      
      const element = OcrTextElement(
        text: 'Hello World',
        boundingBox: boundingBox,
        confidence: 0.95,
      );

      expect(element.text, 'Hello World');
      expect(element.boundingBox, boundingBox);
      expect(element.confidence, 0.95);
    });

    test('should convert to JSON correctly', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );
      
      const element = OcrTextElement(
        text: 'Hello World',
        boundingBox: boundingBox,
        confidence: 0.95,
      );

      final json = element.toJson();
      expect(json['text'], 'Hello World');
      expect(json['confidence'], 0.95);
      expect(json['boundingBox'], isA<Map<String, dynamic>>());
    });

    test('should create from JSON correctly', () {
      final json = {
        'text': 'Hello World',
        'confidence': 0.95,
        'boundingBox': {
          'left': 10.0,
          'top': 20.0,
          'right': 100.0,
          'bottom': 50.0,
        },
      };

      final element = OcrTextElement.fromJson(json);
      expect(element.text, 'Hello World');
      expect(element.confidence, 0.95);
      expect(element.boundingBox.left, 10.0);
      expect(element.boundingBox.top, 20.0);
      expect(element.boundingBox.right, 100.0);
      expect(element.boundingBox.bottom, 50.0);
    });

    test('should have proper toString implementation', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );
      
      const element = OcrTextElement(
        text: 'Hello World',
        boundingBox: boundingBox,
        confidence: 0.95,
      );

      final string = element.toString();
      expect(string, contains('Hello World'));
      expect(string, contains('0.95'));
    });
  });

  group('OcrBoundingBox', () {
    test('should create instance with required properties', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );

      expect(boundingBox.left, 10.0);
      expect(boundingBox.top, 20.0);
      expect(boundingBox.right, 100.0);
      expect(boundingBox.bottom, 50.0);
    });

    test('should calculate width and height correctly', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );

      expect(boundingBox.width, 90.0);
      expect(boundingBox.height, 30.0);
    });

    test('should calculate center correctly', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );

      final center = boundingBox.center;
      expect(center.dx, 55.0);
      expect(center.dy, 35.0);
    });

    test('should convert to Rect correctly', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );

      final rect = boundingBox.toRect();
      expect(rect.left, 10.0);
      expect(rect.top, 20.0);
      expect(rect.right, 100.0);
      expect(rect.bottom, 50.0);
    });

    test('should convert to JSON correctly', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );

      final json = boundingBox.toJson();
      expect(json['left'], 10.0);
      expect(json['top'], 20.0);
      expect(json['right'], 100.0);
      expect(json['bottom'], 50.0);
    });

    test('should create from JSON correctly', () {
      final json = {
        'left': 10.0,
        'top': 20.0,
        'right': 100.0,
        'bottom': 50.0,
      };

      final boundingBox = OcrBoundingBox.fromJson(json);
      expect(boundingBox.left, 10.0);
      expect(boundingBox.top, 20.0);
      expect(boundingBox.right, 100.0);
      expect(boundingBox.bottom, 50.0);
    });

    test('should have proper toString implementation', () {
      const boundingBox = OcrBoundingBox(
        left: 10.0,
        top: 20.0,
        right: 100.0,
        bottom: 50.0,
      );

      final string = boundingBox.toString();
      expect(string, contains('10.0'));
      expect(string, contains('20.0'));
      expect(string, contains('100.0'));
      expect(string, contains('50.0'));
    });
  });

  group('OcrException', () {
    test('should create with message only', () {
      const exception = OcrException('Test error message');
      expect(exception.message, 'Test error message');
      expect(exception.originalError, null);
    });

    test('should create with message and original error', () {
      const originalError = 'Original error';
      const exception = OcrException('Test error message', originalError);
      expect(exception.message, 'Test error message');
      expect(exception.originalError, originalError);
    });

    test('should have proper toString implementation', () {
      const exception = OcrException('Test error message');
      final string = exception.toString();
      expect(string, contains('Test error message'));
    });

    test('should include original error in toString when present', () {
      const originalError = 'Original error';
      const exception = OcrException('Test error message', originalError);
      final string = exception.toString();
      expect(string, contains('Test error message'));
      expect(string, contains('Original error'));
    });
  });
}
