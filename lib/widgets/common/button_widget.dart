import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 通用按钮组件
class ButtonWidget {
  /// 创建图片按钮
  static Widget createImageButton(
    String imageUrl,
    double width,
    double? height, {
    void Function()? onPressed,
    BoxFit? fit,
  }) {
    fit ??= BoxFit.fill;
    if (height == null) {
      fit = BoxFit.fitWidth;
    }

    return IconButton(
      onPressed: () {
        if (onPressed != null) {
          onPressed();
        }
      },
      icon: Image(
        image: AssetImage(imageUrl),
        width: width,
        height: height,
        fit: fit,
      ),
      style: const ButtonStyle(
        padding: WidgetStatePropertyAll(EdgeInsets.all(0))
      ),
    );
  }

  /// 创建SVG按钮
  static Widget createSvgButton(
    String svgPath,
    double width,
    double? height, {
    void Function()? onPressed,
    Color? color,
    BoxFit fit = BoxFit.contain,
  }) {
    return IconButton(
      onPressed: () {
        if (onPressed != null) {
          onPressed();
        }
      },
      icon: SvgPicture.asset(
        svgPath,
        width: width,
        height: height ?? width,
        fit: fit,
        colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn)
          : null,
      ),
      style: const ButtonStyle(
        padding: WidgetStatePropertyAll(EdgeInsets.all(0))
      ),
    );
  }

  /// 创建通用按钮（自动检测文件类型）
  static Widget createButton(
    String assetPath,
    double width,
    double? height, {
    void Function()? onPressed,
    Color? color,
    BoxFit? fit,
  }) {
    // 检查文件扩展名
    if (assetPath.toLowerCase().endsWith('.svg')) {
      return createSvgButton(
        assetPath,
        width,
        height,
        onPressed: onPressed,
        color: color,
        fit: fit ?? BoxFit.contain,
      );
    } else {
      return createImageButton(
        assetPath,
        width,
        height,
        onPressed: onPressed,
        fit: fit,
      );
    }
  }
}