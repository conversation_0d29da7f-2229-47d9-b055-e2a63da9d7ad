import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;

/// 书签数据模型
class BookmarkItem {
  final String title;
  final String url;
  final String? favicon;
  final int timestamp;
  final bool faviconCached; // 标记favicon是否已缓存失败

  BookmarkItem({
    required this.title,
    required this.url,
    this.favicon,
    required this.timestamp,
    this.faviconCached = false,
  });

  factory BookmarkItem.fromJson(Map<String, dynamic> json) {
    return BookmarkItem(
      title: json['title'] ?? '',
      url: json['url'] ?? '',
      favicon: json['favicon'],
      timestamp: json['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
      faviconCached: json['faviconCached'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'url': url,
      'favicon': favicon,
      'timestamp': timestamp,
      'faviconCached': faviconCached,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BookmarkItem && other.url == url;
  }

  @override
  int get hashCode => url.hashCode;

  /// 创建副本并更新favicon信息
  BookmarkItem copyWithFavicon({String? favicon, bool? faviconCached}) {
    return BookmarkItem(
      title: title,
      url: url,
      favicon: favicon ?? this.favicon,
      timestamp: timestamp,
      faviconCached: faviconCached ?? this.faviconCached,
    );
  }
}

/// 集中式书签管理服务
/// 使用单例模式，统一管理应用中的所有书签操作
class BookmarkService {
  static final BookmarkService _instance = BookmarkService._internal();
  factory BookmarkService() => _instance;
  BookmarkService._internal();
  
  /// 获取单例实例
  static BookmarkService get instance => _instance;
  
  /// SharedPreferences 键名
  static const String _bookmarksKey = 'browser_bookmarks';
  static const String _faviconCacheKey = 'favicon_cache_failed_urls';
  
  /// 缓存失败的favicon URL，避免重复请求
  Set<String> _failedFaviconUrls = {};
  
  /// 初始化失败缓存
  Future<void> _initializeFailedCache() async {
    if (_failedFaviconUrls.isNotEmpty) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedUrlsJson = prefs.getString(_faviconCacheKey) ?? '[]';
      final List<dynamic> failedUrls = jsonDecode(failedUrlsJson);
      _failedFaviconUrls = failedUrls.cast<String>().toSet();
      debugPrint('加载失败的favicon缓存: ${_failedFaviconUrls.length} 个URL');
    } catch (e) {
      debugPrint('加载失败缓存出错: $e');
      _failedFaviconUrls = {};
    }
  }
  
  /// 保存失败的favicon URL到缓存
  Future<void> _saveFaviconFailedUrl(String url) async {
    try {
      await _initializeFailedCache();
      _failedFaviconUrls.add(url);
      
      final prefs = await SharedPreferences.getInstance();
      final failedUrlsJson = jsonEncode(_failedFaviconUrls.toList());
      await prefs.setString(_faviconCacheKey, failedUrlsJson);
      debugPrint('保存失败的favicon URL: $url');
    } catch (e) {
      debugPrint('保存失败缓存出错: $e');
    }
  }
  
  /// 公开方法：尝试获取网站的favicon
  /// [url] 网站URL
  /// 返回favicon的base64编码字符串，失败则返回null
  Future<String?> fetchFavicon(String url) async {
    return _fetchFavicon(url);
  }

  /// 尝试获取网站的favicon
  /// [url] 网站URL
  /// 返回favicon的base64编码字符串，失败则返回null
  Future<String?> _fetchFavicon(String url) async {
    try {
      await _initializeFailedCache();
      
      // 生成favicon URL
      final faviconUrl = _generateFaviconUrl(url);
      if (faviconUrl == null) {
        return null;
      }
      
      // 检查是否已知失败的URL
      if (_failedFaviconUrls.contains(faviconUrl)) {
        debugPrint('跳过已知失败的favicon URL: $faviconUrl');
        return null;
      }
      
      debugPrint('尝试获取favicon: $faviconUrl');
      
      // 设置超时和重试
      final client = http.Client();
      try {
        final response = await client.get(
          Uri.parse(faviconUrl),
          headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
          },
        ).timeout(const Duration(seconds: 5));
        
        if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
          // 验证是否是有效的图片数据
          if (_isValidImageData(response.bodyBytes)) {
            final base64Icon = base64Encode(response.bodyBytes);
            debugPrint('成功获取favicon: $faviconUrl (${response.bodyBytes.length} bytes)');
            return 'data:image/png;base64,$base64Icon';
          } else {
            debugPrint('获取的favicon数据无效: $faviconUrl');
            await _saveFaviconFailedUrl(faviconUrl);
            return null;
          }
        } else {
          debugPrint('Favicon请求失败: $faviconUrl (状态码: ${response.statusCode})');
          await _saveFaviconFailedUrl(faviconUrl);
          return null;
        }
      } finally {
        client.close();
      }
    } catch (e) {
      final faviconUrl = _generateFaviconUrl(url);
      if (faviconUrl != null) {
        await _saveFaviconFailedUrl(faviconUrl);
      }
      debugPrint('获取favicon异常: $url, 错误: $e');
      return null;
    }
  }
  
  /// 简单验证图片数据
  bool _isValidImageData(Uint8List data) {
    if (data.length < 8) return false;
    
    // 检查常见的图片文件头
    final bytes = data.take(8).toList();
    
    // PNG: 89 50 4E 47 0D 0A 1A 0A
    if (bytes.length >= 8 && 
        bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
      return true;
    }
    
    // JPEG: FF D8 FF
    if (bytes.length >= 3 && 
        bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
      return true;
    }
    
    // GIF: 47 49 46 38
    if (bytes.length >= 4 && 
        bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x38) {
      return true;
    }
    
    // ICO: 00 00 01 00
    if (bytes.length >= 4 && 
        bytes[0] == 0x00 && bytes[1] == 0x00 && bytes[2] == 0x01 && bytes[3] == 0x00) {
      return true;
    }
    
    // 简单检查是否包含HTML内容（有些服务器返回404页面而不是图片）
    final text = String.fromCharCodes(data.take(100));
    if (text.toLowerCase().contains('<html') || text.toLowerCase().contains('<!doctype')) {
      return false;
    }
    
    return true;
  }

  /// 加载所有书签
  /// 返回按时间戳排序的书签列表（最新的在前）
  Future<List<BookmarkItem>> loadBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarksJson = prefs.getString(_bookmarksKey) ?? '[]';
      final List<dynamic> bookmarksData = jsonDecode(bookmarksJson);

      final bookmarks = bookmarksData
          .map((data) => BookmarkItem.fromJson(data))
          .toList();

      // 按时间戳排序（最新的在前）
      bookmarks.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      debugPrint('加载书签成功: ${bookmarks.length} 个书签');
      return bookmarks;
    } catch (e) {
      debugPrint('加载书签失败: $e');
      return [];
    }
  }

  /// 获取最近的书签（限制数量）
  /// [limit] 返回的书签数量限制，默认为8
  Future<List<BookmarkItem>> getRecentBookmarks({int limit = 8}) async {
    try {
      final allBookmarks = await loadBookmarks();
      final recentBookmarks = allBookmarks.take(limit).toList();
      // debugPrint('获取最近书签: ${recentBookmarks.length} 个（限制: $limit）');
      return recentBookmarks;
    } catch (e) {
      debugPrint('获取最近书签失败: $e');
      return [];
    }
  }

  /// 保存书签
  /// [bookmark] 要保存的书签项
  Future<void> saveBookmark(BookmarkItem bookmark) async {
    try {
      final bookmarks = await loadBookmarks();
      
      // 检查是否已存在相同URL的书签
      final existingIndex = bookmarks.indexWhere((b) => b.url == bookmark.url);
      if (existingIndex != -1) {
        // 如果已存在，更新现有书签
        bookmarks[existingIndex] = bookmark;
        debugPrint('更新现有书签: ${bookmark.title}');
      } else {
        // 如果不存在，添加新书签
        bookmarks.insert(0, bookmark); // 插入到开头，保持最新的在前
        debugPrint('添加新书签: ${bookmark.title}');
      }

      await _saveBookmarksToStorage(bookmarks);
    } catch (e) {
      debugPrint('保存书签失败: $e');
      rethrow;
    }
  }

  /// 删除书签
  /// [url] 要删除的书签URL
  Future<void> deleteBookmark(String url) async {
    try {
      final bookmarks = await loadBookmarks();
      final initialCount = bookmarks.length;
      
      bookmarks.removeWhere((bookmark) => bookmark.url == url);
      
      if (bookmarks.length < initialCount) {
        await _saveBookmarksToStorage(bookmarks);
        debugPrint('删除书签成功: $url');
      } else {
        debugPrint('未找到要删除的书签: $url');
      }
    } catch (e) {
      debugPrint('删除书签失败: $e');
      rethrow;
    }
  }

  /// 切换书签状态（添加或删除）
  /// [url] 网站URL
  /// [title] 网站标题
  /// [favicon] 网站图标URL（可选）
  /// 返回操作后的书签状态：true表示已添加，false表示已删除
  Future<bool> toggleBookmark(String url, String title, {String? favicon}) async {
    try {
      final isCurrentlyBookmarked = await isBookmarked(url);
      
      if (isCurrentlyBookmarked) {
        await deleteBookmark(url);
        debugPrint('切换书签 - 已删除: $title');
        return false;
      } else {
        // 创建初始书签
        var bookmark = BookmarkItem(
          title: title.isNotEmpty ? title : url,
          url: url,
          favicon: favicon,
          timestamp: DateTime.now().millisecondsSinceEpoch,
          faviconCached: false,
        );
        
        // 如果没有提供favicon，尝试获取
        if (favicon == null || favicon.isEmpty) {
          debugPrint('尝试为新书签获取favicon: $url');
          final fetchedFavicon = await _fetchFavicon(url);
          if (fetchedFavicon != null) {
            bookmark = bookmark.copyWithFavicon(
              favicon: fetchedFavicon, 
              faviconCached: true
            );
            debugPrint('成功为书签获取favicon: $title');
          } else {
            bookmark = bookmark.copyWithFavicon(faviconCached: true);
            debugPrint('无法获取书签favicon，已标记: $title');
          }
        }
        
        await saveBookmark(bookmark);
        debugPrint('切换书签 - 已添加: $title');
        return true;
      }
    } catch (e) {
      debugPrint('切换书签失败: $e');
      rethrow;
    }
  }

  /// 检查URL是否已被收藏
  /// [url] 要检查的URL
  /// 返回true表示已收藏，false表示未收藏
  Future<bool> isBookmarked(String url) async {
    try {
      final bookmarks = await loadBookmarks();
      final isBookmarked = bookmarks.any((bookmark) => bookmark.url == url);
      debugPrint('检查书签状态: $url -> $isBookmarked');
      return isBookmarked;
    } catch (e) {
      debugPrint('检查书签状态失败: $e');
      return false;
    }
  }

  /// 将书签列表保存到SharedPreferences
  /// [bookmarks] 要保存的书签列表
  Future<void> _saveBookmarksToStorage(List<BookmarkItem> bookmarks) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarksJson = jsonEncode(bookmarks.map((b) => b.toJson()).toList());
      await prefs.setString(_bookmarksKey, bookmarksJson);
      debugPrint('书签保存到存储成功: ${bookmarks.length} 个书签');
    } catch (e) {
      debugPrint('保存书签到存储失败: $e');
      rethrow;
    }
  }

  /// 构建书签图标Widget
  /// [title] 网站标题
  /// [url] 网站URL
  /// [favicon] 网站图标URL（可选）
  /// [size] 图标大小，默认32
  /// [faviconCached] 是否已尝试缓存favicon
  Widget buildBookmarkIcon(String title, String url, String? favicon, double size, {bool faviconCached = false}) {
    // 首先尝试使用提供的favicon（base64或URL）
    if (favicon != null && favicon.isNotEmpty) {
      if (favicon.startsWith('data:image/')) {
        // Base64编码的图片
        return ClipOval(
          child: Image.memory(
            base64Decode(favicon.split(',').last),
            width: size,
            height: size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              debugPrint('Base64 favicon解码失败: $error');
              return _buildFallbackIcon(title, url, size);
            },
          ),
        );
      } else {
        // 网络URL
        return ClipOval(
          child: Image.network(
            favicon,
            width: size,
            height: size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              debugPrint('Favicon加载失败: $favicon, 错误: $error');
              return _buildFallbackIcon(title, url, size);
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              // 显示加载状态时使用回退图标
              return _buildFallbackIcon(title, url, size);
            },
          ),
        );
      }
    }

    // 如果已标记为缓存失败，直接使用回退图标，避免重复请求
    if (faviconCached) {
      return _buildFallbackIcon(title, url, size);
    }

    // 如果没有提供favicon且未缓存，检查是否为已知失败的URL
    final generatedFaviconUrl = _generateFaviconUrl(url);
    if (generatedFaviconUrl != null && _failedFaviconUrls.contains(generatedFaviconUrl)) {
      return _buildFallbackIcon(title, url, size);
    }

    // 最后回退到域名首字母图标（避免网络请求）
    return _buildFallbackIcon(title, url, size);
  }

  /// 生成标准favicon URL
  /// [url] 网站URL
  /// 返回生成的favicon URL，如果无法生成则返回null
  String? _generateFaviconUrl(String url) {
    try {
      final uri = Uri.parse(url);
      if (uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https') && uri.host.isNotEmpty) {
        // 优先尝试常见的favicon路径
        return '${uri.scheme}://${uri.host}/favicon.ico';
      }
    } catch (e) {
      debugPrint('无法从URL生成favicon: $url, 错误: $e');
    }
    return null;
  }

  /// 构建回退图标（使用域名首字母和颜色编码）
  /// [title] 网站标题
  /// [url] 网站URL
  /// [size] 图标大小
  Widget _buildFallbackIcon(String title, String url, double size) {
    String letterSource = title;

    // 优先使用域名
    try {
      final uri = Uri.parse(url);
      String host = uri.host;

      // 移除 www. 前缀
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      if (host.isNotEmpty) {
        letterSource = host;
      }
    } catch (e) {
      // URL解析失败，使用标题
      debugPrint('URL解析失败，使用标题作为字母来源: $e');
    }

    final firstLetter = letterSource.isNotEmpty ? letterSource[0].toUpperCase() : '?';
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _getColorForSite(letterSource),
      ),
      child: Center(
        child: Text(
          firstLetter,
          style: TextStyle(
            fontSize: size * 0.45, // 字体大小相对于图标大小
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// 根据网站名称首字母获取颜色
  /// 使用与现有实现相同的颜色编码系统
  /// [siteName] 网站名称或域名
  Color _getColorForSite(String siteName) {
    if (siteName.isEmpty) return const Color(0xFFFFDE00);

    final firstLetter = siteName[0].toUpperCase();

    switch (firstLetter) {
      case 'A':
      case 'B':
      case 'C':
        return const Color(0xFFFFDE00); // 黄色
      case 'D':
      case 'E':
      case 'F':
        return const Color(0xFFC0E700); // 柠檬绿
      case 'G':
      case 'H':
      case 'I':
        return const Color(0xFF2FC27E); // 绿色
      case 'J':
      case 'K':
      case 'L':
        return const Color(0xFF728FFE); // 蓝色
      case 'M':
      case 'N':
      case 'O':
        return const Color(0xFFFFB500); // 橙色
      case 'P':
      case 'Q':
      case 'R':
        return const Color(0xFFFF7100); // 红橙色
      case 'S':
      case 'T':
      case 'U':
        return const Color(0xFF00DFE7); // 青色
      case 'V':
      case 'W':
      case 'X':
        return const Color(0xFFED72AA); // 粉色
      case 'Y':
      case 'Z':
        return const Color(0xFF8668FF); // 紫色
      default:
        return const Color(0xFFFFDE00); // 默认黄色
    }
  }
}
