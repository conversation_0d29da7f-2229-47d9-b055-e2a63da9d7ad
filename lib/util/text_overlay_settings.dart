import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui' as ui;

/// 文本覆盖层设置管理器
/// 管理用户对文本覆盖层的字体、样式、滤镜等设置
class TextOverlaySettings extends ChangeNotifier {
  // 单例模式
  static final TextOverlaySettings _instance = TextOverlaySettings._internal();
  factory TextOverlaySettings() => _instance;
  
  TextOverlaySettings._internal();
  
  // 设置键名
  static const String _fontSizeKey = 'text_overlay_font_size';
  static const String _fontWeightKey = 'text_overlay_font_weight';
  static const String _fontFamilyKey = 'text_overlay_font_family';
  static const String _textColorKey = 'text_overlay_text_color';
  static const String _strokeColorKey = 'text_overlay_stroke_color';
  static const String _strokeWidthKey = 'text_overlay_stroke_width';
  static const String _backgroundOpacityKey = 'text_overlay_background_opacity';
  static const String _blurSigmaXKey = 'text_overlay_blur_sigma_x';
  static const String _blurSigmaYKey = 'text_overlay_blur_sigma_y';
  static const String _shadowOpacityKey = 'text_overlay_shadow_opacity';
  static const String _shadowOffsetXKey = 'text_overlay_shadow_offset_x';
  static const String _shadowOffsetYKey = 'text_overlay_shadow_offset_y';
  static const String _shadowBlurRadiusKey = 'text_overlay_shadow_blur_radius';
  
  // 默认设置值
  static const double _defaultFontSize = 1.0; // 相对大小倍数
  static const int _defaultFontWeight = 700; // FontWeight.bold
  static const String _defaultFontFamily = 'Inter'; // 默认字体
  static const int _defaultTextColor = 0xFF000000; // Colors.black
  static const int _defaultStrokeColor = 0xFFFFFFFF; // Colors.white
  static const double _defaultStrokeWidth = 3.0;
  static const double _defaultBackgroundOpacity = 0.0;
  static const double _defaultBlurSigmaX = 8.0;
  static const double _defaultBlurSigmaY = 8.0;
  static const double _defaultShadowOpacity = 0.5;
  static const double _defaultShadowOffsetX = 1.0;
  static const double _defaultShadowOffsetY = 1.0;
  static const double _defaultShadowBlurRadius = 3.0;
  
  // 当前设置值
  double _fontSize = _defaultFontSize;
  int _fontWeight = _defaultFontWeight;
  String _fontFamily = _defaultFontFamily;
  int _textColor = _defaultTextColor;
  int _strokeColor = _defaultStrokeColor;
  double _strokeWidth = _defaultStrokeWidth;
  double _backgroundOpacity = _defaultBackgroundOpacity;
  double _blurSigmaX = _defaultBlurSigmaX;
  double _blurSigmaY = _defaultBlurSigmaY;
  double _shadowOpacity = _defaultShadowOpacity;
  double _shadowOffsetX = _defaultShadowOffsetX;
  double _shadowOffsetY = _defaultShadowOffsetY;
  double _shadowBlurRadius = _defaultShadowBlurRadius;
  
  bool _initialized = false;
  
  // Getters
  double get fontSize => _fontSize;
  FontWeight get fontWeight {
    switch (_fontWeight) {
      case 300:
        return FontWeight.w300;
      case 400:
        return FontWeight.w400;
      case 500:
        return FontWeight.w500;
      case 600:
        return FontWeight.w600;
      case 700:
        return FontWeight.w700;
      case 800:
        return FontWeight.w800;
      case 900:
        return FontWeight.w900;
      default:
        return FontWeight.w700; // 默认为bold
    }
  }
  String get fontFamily => _fontFamily;
  Color get textColor => Color(_textColor);
  Color get strokeColor => Color(_strokeColor);
  double get strokeWidth => _strokeWidth;
  double get backgroundOpacity => _backgroundOpacity;
  double get blurSigmaX => _blurSigmaX;
  double get blurSigmaY => _blurSigmaY;
  double get shadowOpacity => _shadowOpacity;
  Offset get shadowOffset => Offset(_shadowOffsetX, _shadowOffsetY);
  double get shadowBlurRadius => _shadowBlurRadius;
  bool get initialized => _initialized;
  
  // 获取阴影颜色
  Color get shadowColor => Colors.black.withValues(alpha: _shadowOpacity);
  
  // 获取背景滤镜
  ui.ImageFilter get backgroundFilter => ui.ImageFilter.blur(
    sigmaX: _blurSigmaX,
    sigmaY: _blurSigmaY,
  );
  
  // Setters
  Future<void> setFontSize(double size) async {
    if (_fontSize == size) return;
    _fontSize = size;
    await _saveSetting(_fontSizeKey, size);
    notifyListeners();
  }
  
  Future<void> setFontWeight(FontWeight weight) async {
    final weightValue = weight.value;
    debugPrint('Setting font weight: ${weight.value} (from ${weight.toString()})');
    debugPrint('Current font weight: $_fontWeight');
    if (_fontWeight == weightValue) {
      debugPrint('Font weight unchanged, returning');
      return;
    }
    _fontWeight = weightValue;
    debugPrint('Font weight updated to: $_fontWeight');
    await _saveSetting(_fontWeightKey, weightValue);
    debugPrint('Font weight saved to preferences');
    notifyListeners();
    debugPrint('Listeners notified');
  }
  
  Future<void> setFontFamily(String family) async {
    if (_fontFamily == family) return;
    _fontFamily = family;
    await _saveStringSetting(_fontFamilyKey, family);
    notifyListeners();
  }
  
  Future<void> setTextColor(Color color) async {
    final colorValue = color.toARGB32();
    if (_textColor == colorValue) return;
    _textColor = colorValue;
    await _saveSetting(_textColorKey, colorValue);
    notifyListeners();
  }
  
  Future<void> setStrokeColor(Color color) async {
    final colorValue = color.toARGB32();
    if (_strokeColor == colorValue) return;
    _strokeColor = colorValue;
    await _saveSetting(_strokeColorKey, colorValue);
    notifyListeners();
  }
  
  Future<void> setStrokeWidth(double width) async {
    if (_strokeWidth == width) return;
    _strokeWidth = width;
    await _saveSetting(_strokeWidthKey, width);
    notifyListeners();
  }
  
  Future<void> setBackgroundOpacity(double opacity) async {
    if (_backgroundOpacity == opacity) return;
    _backgroundOpacity = opacity;
    await _saveSetting(_backgroundOpacityKey, opacity);
    notifyListeners();
  }
  
  Future<void> setBlurSigma(double sigmaX, double sigmaY) async {
    if (_blurSigmaX == sigmaX && _blurSigmaY == sigmaY) return;
    _blurSigmaX = sigmaX;
    _blurSigmaY = sigmaY;
    await _saveSetting(_blurSigmaXKey, sigmaX);
    await _saveSetting(_blurSigmaYKey, sigmaY);
    notifyListeners();
  }
  
  Future<void> setShadowSettings({
    double? opacity,
    double? offsetX,
    double? offsetY,
    double? blurRadius,
  }) async {
    bool changed = false;
    
    if (opacity != null && _shadowOpacity != opacity) {
      _shadowOpacity = opacity;
      await _saveSetting(_shadowOpacityKey, opacity);
      changed = true;
    }
    
    if (offsetX != null && _shadowOffsetX != offsetX) {
      _shadowOffsetX = offsetX;
      await _saveSetting(_shadowOffsetXKey, offsetX);
      changed = true;
    }
    
    if (offsetY != null && _shadowOffsetY != offsetY) {
      _shadowOffsetY = offsetY;
      await _saveSetting(_shadowOffsetYKey, offsetY);
      changed = true;
    }
    
    if (blurRadius != null && _shadowBlurRadius != blurRadius) {
      _shadowBlurRadius = blurRadius;
      await _saveSetting(_shadowBlurRadiusKey, blurRadius);
      changed = true;
    }
    
    if (changed) {
      notifyListeners();
    }
  }
  
  // 重置为默认设置
  Future<void> resetToDefaults() async {
    _fontSize = _defaultFontSize;
    _fontWeight = _defaultFontWeight;
    _fontFamily = _defaultFontFamily;
    _textColor = _defaultTextColor;
    _strokeColor = _defaultStrokeColor;
    _strokeWidth = _defaultStrokeWidth;
    _backgroundOpacity = _defaultBackgroundOpacity;
    _blurSigmaX = _defaultBlurSigmaX;
    _blurSigmaY = _defaultBlurSigmaY;
    _shadowOpacity = _defaultShadowOpacity;
    _shadowOffsetX = _defaultShadowOffsetX;
    _shadowOffsetY = _defaultShadowOffsetY;
    _shadowBlurRadius = _defaultShadowBlurRadius;
    
    await _saveAllSettings();
    notifyListeners();
  }
  
  // 保存单个设置
  Future<void> _saveSetting(String key, dynamic value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value is double) {
      await prefs.setDouble(key, value);
    } else if (value is int) {
      await prefs.setInt(key, value);
    }
  }
  
  // 保存字符串设置
  Future<void> _saveStringSetting(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }
  
  // 保存所有设置
  Future<void> _saveAllSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(_fontSizeKey, _fontSize);
    await prefs.setInt(_fontWeightKey, _fontWeight);
    await prefs.setString(_fontFamilyKey, _fontFamily);
    await prefs.setInt(_textColorKey, _textColor);
    await prefs.setInt(_strokeColorKey, _strokeColor);
    await prefs.setDouble(_strokeWidthKey, _strokeWidth);
    await prefs.setDouble(_backgroundOpacityKey, _backgroundOpacity);
    await prefs.setDouble(_blurSigmaXKey, _blurSigmaX);
    await prefs.setDouble(_blurSigmaYKey, _blurSigmaY);
    await prefs.setDouble(_shadowOpacityKey, _shadowOpacity);
    await prefs.setDouble(_shadowOffsetXKey, _shadowOffsetX);
    await prefs.setDouble(_shadowOffsetYKey, _shadowOffsetY);
    await prefs.setDouble(_shadowBlurRadiusKey, _shadowBlurRadius);
  }
  
  // 从SharedPreferences加载设置
  Future<void> loadSettings() async {
    if (_initialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _fontSize = prefs.getDouble(_fontSizeKey) ?? _defaultFontSize;
      _fontWeight = prefs.getInt(_fontWeightKey) ?? _defaultFontWeight;
      _fontFamily = prefs.getString(_fontFamilyKey) ?? _defaultFontFamily;
      _textColor = prefs.getInt(_textColorKey) ?? _defaultTextColor;
      _strokeColor = prefs.getInt(_strokeColorKey) ?? _defaultStrokeColor;
      _strokeWidth = prefs.getDouble(_strokeWidthKey) ?? _defaultStrokeWidth;
      _backgroundOpacity = prefs.getDouble(_backgroundOpacityKey) ?? _defaultBackgroundOpacity;
      _blurSigmaX = prefs.getDouble(_blurSigmaXKey) ?? _defaultBlurSigmaX;
      _blurSigmaY = prefs.getDouble(_blurSigmaYKey) ?? _defaultBlurSigmaY;
      _shadowOpacity = prefs.getDouble(_shadowOpacityKey) ?? _defaultShadowOpacity;
      _shadowOffsetX = prefs.getDouble(_shadowOffsetXKey) ?? _defaultShadowOffsetX;
      _shadowOffsetY = prefs.getDouble(_shadowOffsetYKey) ?? _defaultShadowOffsetY;
      _shadowBlurRadius = prefs.getDouble(_shadowBlurRadiusKey) ?? _defaultShadowBlurRadius;
      
    } catch (e) {
      debugPrint("Error loading text overlay settings: $e");
    }
    
    _initialized = true;
    notifyListeners();
  }
  
  // 初始化设置
  static Future<void> initialize() async {
    await _instance.loadSettings();
  }
} 