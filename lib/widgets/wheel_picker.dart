import 'package:flutter/material.dart';
import '../util/theme_manager.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

class WheelPicker extends StatefulWidget {
  final String selectedValue;
  final Map<String, String> options;
  final Function(String) onValueChanged;
  final VoidCallback onCancel;
  final VoidCallback onConfirm;
  final String? cancelText;
  final String? confirmText;

  const WheelPicker({
    Key? key,
    required this.selectedValue,
    required this.options,
    required this.onValueChanged,
    required this.onCancel,
    required this.onConfirm,
    this.cancelText,
    this.confirmText,
  }) : super(key: key);

  @override
  State<WheelPicker> createState() => _WheelPickerState();
}

class _WheelPickerState extends State<WheelPicker> {
  late String _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.selectedValue;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部按钮栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 取消按钮
                GestureDetector(
                  onTap: widget.onCancel,
                  child: Text(
                    widget.cancelText ?? AppLocalizations.of(context)!.cancel,
                    style: TextStyle(
                      color: Color(0xffb2b6c2),
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                // 确认按钮
                GestureDetector(
                  onTap: () {
                    widget.onValueChanged(_currentValue);
                    widget.onConfirm();
                  },
                  child: Text(
                    widget.confirmText ?? AppLocalizations.of(context)!.ok,
                    style: TextStyle(
                      color: ThemeManager.currentTheme.textColor,
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 滚轮选择器
          Container(
            height: 280,
            child: Stack(
              children: [
                // 中间选中区域的灰色分隔线
                Positioned(
                  left: 0,
                  right: 0,
                  top: 120,
                  child: Container(
                    height: 1,
                    color: Colors.grey[300],
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  top: 160,
                  child: Container(
                    height: 1,
                    color: Colors.grey[300],
                  ),
                ),
                // 滚轮选择器
                ListWheelScrollView.useDelegate(
                  itemExtent: 40,
                  diameterRatio: 1.5,
                  squeeze: 1.0,
                  useMagnifier: true,
                  magnification: 1.2,
                  physics: const FixedExtentScrollPhysics(),
                  onSelectedItemChanged: (index) {
                    setState(() {
                      _currentValue = widget.options.keys.elementAt(index);
                    });
                  },
                  controller: FixedExtentScrollController(
                    initialItem: widget.options.keys.toList().indexOf(widget.selectedValue),
                  ),
                  childDelegate: ListWheelChildBuilderDelegate(
                    childCount: widget.options.length,
                    builder: (context, index) {
                      final value = widget.options.keys.elementAt(index);
                      final isSelected = _currentValue == value;
                      return Container(
                        alignment: Alignment.center,
                        child: Text(
                          widget.options[value] ?? value,
                          style: TextStyle(
                            color: isSelected ? ThemeManager.currentTheme.textColor : Color(0xffb2b6c2),
                            fontSize: 13,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}