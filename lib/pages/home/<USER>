import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/pages/home/<USER>';
import 'package:imtrans/widgets/toast_widget.dart';
import 'package:imtrans/util/dialog_manager.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/util/loading_manager.dart';
import 'package:imtrans/util/text_overlay_settings.dart';
import '../../util/util.dart';
import '../../widgets/text_overlay.dart';
import '../../widgets/style_settings_widget.dart';
import '../../controllers/product_controller.dart';
import '../../models/product_model.dart';
import '../../services/image_save_service.dart';
import 'package:imtrans/widgets/common/common_widgets.dart';

/// 单个图片文件的产品查看方法
class ImageViewer extends StatefulWidget {
  final int currentIndex;
  final ProductController productController;

  const ImageViewer(
      {required this.currentIndex, required this.productController, super.key});

  @override
  State<ImageViewer> createState() {
    return _ImageViewerState();
  }
}

class _ImageViewerState extends State<ImageViewer> with TickerProviderStateMixin {
  static const int _animationDuration = 200;
  static const double _minScale = 0.8; // Increased for better usability
  static const double _maxScale = 4.0; // Slightly reduced for performance

  double _width = 0;
  double _height = 0;
  bool _showOrigin = false;
  int _index = 0;
  bool _showAppBar = false;
  bool _showBottomBar = false;
  bool _showPageButtonsVisually = true; // 控制翻页按钮的视觉显示
  bool _enablePageButtons = true; // 控制翻页按钮是否响应点击

  late final AnimationController _inController;
  late Animation<Offset> _animation;

  late final AnimationController _inControllerLeft;
  // late Animation<Offset> _animationLeft;

  late final AnimationController _settingsController;
  late Animation<Offset> _settingsSlideAnimation;
  late Animation<double> _settingsBackdropAnimation;

  String? currentSrc;
  bool _showCatalog = false;
  bool _showSettings = false;
  dynamic _imageItem;
  Widget? _originImage;
  double _scale = 1.0;
  late TransformationController _transformationController;

  @override
  void initState() {
    super.initState();

    // Initialize transformation controller for zoom tracking
    _transformationController = TransformationController();

    // 确保索引在有效范围内
    if (widget.currentIndex >= 0 && widget.currentIndex < widget.productController.products.length) {
      _index = widget.currentIndex;
    } else {
      // 如果索引无效，设置为0或最后一个有效索引
      _index = widget.productController.products.isNotEmpty ? 0 : -1;
      debugPrint('Invalid currentIndex corrected: ${widget.currentIndex} -> $_index');
    }
    
    _inController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: _animationDuration));
    _animation = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(0, -1),
    ).animate(_inController);

    _inControllerLeft = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: _animationDuration));
    // _animationLeft = Tween<Offset>(
    //   begin: const Offset(0, 0),
    //   end: const Offset(-1, 0),
    // ).animate(_inControllerLeft);

    _settingsController = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 300));
    _settingsSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from bottom
      end: const Offset(0, 0),   // End at normal position
    ).animate(CurvedAnimation(
      parent: _settingsController,
      curve: Curves.easeInOut,
    ));
    _settingsBackdropAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _settingsController,
      curve: Curves.easeInOut,
    ));
    
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // 初始化文本覆盖层设置
      await TextOverlaySettings.initialize();
      
      if (_index >= 0 && _index < widget.productController.products.length) {
        createResult(widget.productController.products[_index], _index);
      } else if (widget.productController.products.isNotEmpty) {
        // 如果列表不为空但索引无效，尝试显示第一个产品
        _index = 0;
        createResult(widget.productController.products[0], 0);
      } else {
        // 如果列表为空，关闭视图
        debugPrint('Product list is empty, closing view');
        Util.closeView(widget);
      }
    });
    
    showBar();
    // widget.productController.markAsRead(widget.productController.products[_index].productId!);
  }

  @override
  void dispose() {
    Util.popNull(widget);
    _inController.dispose();
    _inControllerLeft.dispose();
    _settingsController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  Widget _buildMainImageView() {
    return Positioned.fill(
      child: _buildInteractiveImageViewer(),
    );
  }

  // Separate method for cleaner gesture handling
  Widget _buildInteractiveImageViewer() {
    // 计算topbar高度（状态栏 + appbar）
    final double topBarHeight = MediaQuery.of(context).padding.top + 60;
    // 计算底部控制栏高度
    final double bottomBarHeight = 60 + MediaQuery.of(context).padding.bottom;
    
    return InteractiveViewer(
      transformationController: _transformationController,
      boundaryMargin: const EdgeInsets.all(0.0),
      minScale: _minScale,
      maxScale: _maxScale,
      // 设置对齐方式为顶部对齐，适合长图显示
      alignment: Alignment.topCenter,
      // 允许无限制平移，让用户可以查看长图的所有部分
      panEnabled: true,
      scaleEnabled: true,
      // 关键：允许子组件超出视窗边界，这对长图滚动至关重要
      constrained: false,
      // Optimize zoom sensitivity
      scaleFactor: 200.0, // Lower value = more sensitive zoom
      onInteractionStart: (ScaleStartDetails details) {
        // Track interaction start for better gesture coordination
        debugPrint('Zoom interaction started');
      },
      onInteractionUpdate: (ScaleUpdateDetails details) {
        // Get actual scale from transformation matrix
        final Matrix4 matrix = _transformationController.value;
        final double currentScale = matrix.getMaxScaleOnAxis();

        // Update scale with smaller threshold for better responsiveness
        if ((currentScale - _scale).abs() > 0.005) {
          setState(() {
            _scale = currentScale;
          });
        }
      },
      onInteractionEnd: (ScaleEndDetails details) {
        // Final scale update and gesture cleanup
        final Matrix4 matrix = _transformationController.value;
        final double finalScale = matrix.getMaxScaleOnAxis();

        setState(() {
          _scale = finalScale;
        });

        debugPrint('Zoom interaction ended, final scale: $_scale');
      },
      child: Container(
        width: _width,
        // 移除高度约束，让长图能够完全展示
        // 添加顶部和底部padding，确保图片不被UI元素遮挡
        padding: EdgeInsets.only(
          top: topBarHeight,
          bottom: bottomBarHeight,
        ),
        child: GestureDetector(
          // 只处理点击事件，不处理拖拽事件以避免与InteractiveViewer冲突
          behavior: HitTestBehavior.translucent,
          onTap: () {
            _handleTapGesture();
          },
          child: Container(
            // 确保容器有足够的空间来容纳长图
            width: _width,
            child: RepaintBoundary(
              key: Util.globalKey,
              child: _showOrigin ? _originImage : _imageItem,
            ),
          ),
        ),
      ),
    );
  }

  // Separate method for tap gesture handling
  void _handleTapGesture() {
    // 简化点击处理逻辑，主要用于显示/隐藏工具栏
    if (!_showAppBar) {
      showBar();
    } else {
      hideBar();
    }
  }

  // Reset zoom and position when changing images for better UX
  void _resetZoom() {
    // 重置变换矩阵到初始状态，因为我们在Container中添加了padding，所以这里直接使用identity即可
    _transformationController.value = Matrix4.identity();
    setState(() {
      _scale = 1.0;
    });
  }

  Widget _buildBottomControls() {
    double iconW = 26;
    return Positioned(
      left: 0,
      bottom: 0,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0),
          end: const Offset(0, 1), // 向下滑出屏幕
        ).animate(_inControllerLeft),
        child: Material(
          color: ThemeManager.currentTheme.bottomBarBackgroundColor.withValues(alpha: .9),
          child: Container(
            width: _width,
            padding: EdgeInsets.only(
              top: 10, 
              bottom: 0 + MediaQuery.of(context).padding.bottom, // 增加底部安全区域的padding
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                /// 样式设置按钮 (moved from top-right)
                ButtonWidget.createButton(
                    ThemeManager.getImagePath('icon_style'), iconW, iconW,
                    onPressed: () => _showSettingsPanel()),
                /// 目录按钮
                ButtonWidget.createImageButton(
                    ThemeManager.getImagePath('icon_contents'), iconW, iconW,
                    onPressed: () => showCatalog(true)),
                /// 上一页按钮
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_prevpage'), iconW, iconW, 
                  onPressed: () => actionEvent(-1)),
                
                /// 下一页按钮
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_nextpage'), iconW, iconW, 
                  onPressed: () => actionEvent(1)),
                /// 下载按钮
                ButtonWidget.createImageButton(ThemeManager.getImagePath('icon_download'), iconW, iconW,
                  onPressed: () async {
                    LoadingManager.instance.show(context);
                    try {
                      if (_index < 0 || _index >= widget.productController.products.length) {
                        throw Exception('Invalid image index');
                      }

                      final product = widget.productController.products[_index];
                      
                      if (_showOrigin) {
                        // 显示原图时，保存原图
                        await ImageSaveService.instance.saveProductImage(
                          product,
                          saveType: ImageSaveType.original,
                          context: context,
                        );
                      } else {
                        // 显示翻译图时，通过RepaintBoundary保存当前显示内容
                        // 使用更高的像素比例以提高图片质量
                        await ImageSaveService.instance.saveCurrentDisplayImage(
                          Util.globalKey,
                          context: context,
                          pixelRatio: 5.0, // 提高像素比例获得更高质量
                        );
                      }
                    } catch (e) {
                      debugPrint('Error saving image: $e');
                      ToastWidget.showError('${AppLocalizations.of(context)!.failedToSaveImage}: $e');
                    } finally {
                      LoadingManager.instance.hide(context); // 确保无论成功或失败都关闭loading
                    }
                  }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    try {
      var size = MediaQuery.of(context).size;
      _width = size.width;
      _height = size.height;
      return PopScope(
        canPop: true,
        child: Scaffold(
          backgroundColor: ThemeManager.currentTheme.backgroundColor,
          // Disable system swipe-back gesture while preserving zoom and swipe functionality
          body: Stack(
            children: [
                _buildMainImageView(),
                if (_showAppBar) createAppBar(),
                _buildBottomControls(),
                _buildCatalogView(),
                _buildSettingsPanel(),
                // 添加悬浮的查看原图按钮，仅在不显示目录和设置时显示
                if (!_showCatalog && !_showSettings) Positioned(
                  right: 25,
                  bottom: 125, // 位于底部按钮栏上方
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent, 
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _showOrigin = !_showOrigin;
                        });
                      },
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xffd9d9d9),
                            width: 1,
                          ),
                          shape: BoxShape.circle,
                          color: _showOrigin ?
                            ThemeManager.currentTheme.logoColor
                            : Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: .25),
                              // spreadRadius: 1,
                              blurRadius: 15,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Image.asset(
                            _showOrigin
                                ? ThemeManager.getImagePath('icon_show_result')
                                : ThemeManager.getImagePath('icon_show_orginal'),
                            width: 20,
                            height: 20,
                          ),
                        ),
                      ),
                    ),
                  )
                ),
                                // 左侧翻页按钮 - 只在底部工具栏隐藏时显示
                if (!_showCatalog && !_showSettings && !_showBottomBar && _enablePageButtons) Positioned(
                  left: 0, // 紧贴左侧屏幕边缘
                  top: 0, // 从顶部开始
                  child: GestureDetector(
                    onTap: () {
                      actionEvent(-1); // 上一页
                      setState(() {
                        _showPageButtonsVisually = false; // 点击后隐藏翻页按钮的视觉效果，但保持可点击
                      });
                    },
                    child: Container(
                      width: 80, // 增加宽度以提供更大的点击区域
                      height: _height, // 全屏高度
                      color: Colors.transparent, // 透明的可点击区域
                      child: Stack(
                        children: [
                          // 半椭圆形的视觉按钮 - 根据_showPageButtonsVisually控制可见性
                          if (_showPageButtonsVisually) Positioned(
                            left: 0,
                            top: _height / 2 - 50,
                            child: Container(
                              width: 40,
                              height: 100,
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: .3),
                                borderRadius: const BorderRadius.only(
                                  topRight: Radius.circular(50),
                                  bottomRight: Radius.circular(50),
                                ), // 右半椭圆形
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: .2),
                                    blurRadius: 10,
                                    offset: const Offset(2, 0),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.chevron_left,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                                // 右侧翻页按钮 - 只在底部工具栏隐藏时显示
                if (!_showCatalog && !_showSettings && !_showBottomBar && _enablePageButtons) Positioned(
                  right: 0, // 紧贴右侧屏幕边缘
                  top: 0, // 从顶部开始
                  child: GestureDetector(
                    onTap: () {
                      actionEvent(1); // 下一页
                      setState(() {
                        _showPageButtonsVisually = false; // 点击后隐藏翻页按钮的视觉效果，但保持可点击
                      });
                    },
                    child: Container(
                      width: 80, // 增加宽度以提供更大的点击区域
                      height: _height - 200, // 高度截止在查看原图按钮上方，保持安全距离
                      color: Colors.transparent, // 透明的可点击区域
                      child: Stack(
                        children: [
                          // 半椭圆形的视觉按钮 - 根据_showPageButtonsVisually控制可见性
                          if (_showPageButtonsVisually) Positioned(
                            right: 0,
                            top: _height / 2 - 50,
                            child: Container(
                              width: 40,
                              height: 100,
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: .3),
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(50),
                                  bottomLeft: Radius.circular(50),
                                ), // 左半椭圆形
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: .2),
                                    blurRadius: 10,
                                    offset: const Offset(-2, 0),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.chevron_right,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ), // Close Stack
        ), // Close Scaffold
      ); // Close PopScope
    } catch (e) {
      debugPrint('Error in build: $e');
      return Scaffold(
        body: Center(
          child: Text(AppLocalizations.of(context)!.errorLoadingImage),
        ),
      );
    }
  }

  Widget _buildCatalogView() {
    if (!_showCatalog) return Container();
    
    return ImageCatalog(
      dart: this,
      list: widget.productController.products,
      currentIndex: _index,
      productController: widget.productController,
    );
  }

  Widget _buildSettingsPanel() {
    if (!_showSettings) return Container();

    return Positioned.fill(
      child: GestureDetector(
        onTap: () {
          _hideSettingsPanel();
        },
        child: AnimatedBuilder(
          animation: _settingsBackdropAnimation,
          builder: (context, child) {
            return Container(
              color: Colors.black.withValues(alpha: _settingsBackdropAnimation.value), // Animated backdrop
              child: Column(
            children: [
              // Spacer to push content to bottom
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    _hideSettingsPanel();
                  },
                  child: Container(color: Colors.transparent),
                ),
              ),
              // Bottom sheet panel with slide animation
              SlideTransition(
                position: _settingsSlideAnimation,
                child: Container(
                  width: MediaQuery.of(context).size.width, // Full screen width
                  height: 380, // Increased height for better spacing
                  decoration: BoxDecoration(
                    color: ThemeManager.currentTheme.bottomBarBackgroundColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: .2),
                        blurRadius: 20,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: GestureDetector(
                    onTap: () {}, // Prevent tap from bubbling up
                    child: _BottomSheetSettingsPanel(
                      onSettingsChanged: () {
                        setState(() {});
                      },
                    ),
                  ),
                ),
              ),
              ],
            ),
          );
          },
        ),
      ),
    );
  }

  deleteImage() async {
    if (_index < 0 || _index >= widget.productController.products.length) {
      debugPrint('Invalid index in deleteImage: $_index');
      return;
    }

    Product item = widget.productController.products[_index];
    LoadingManager.instance.show(context);
    final success = await widget.productController.delete(item.productId!);
    LoadingManager.instance.hide(context);

    if (success) {
      setState(() {
        widget.productController.products.removeAt(_index);
      });
      
      // 检查删除后的产品列表状态
      if (widget.productController.products.isEmpty) {
        // 如果列表为空，关闭视图
        debugPrint('No more products after deletion, closing view');
        Util.closeView(widget);
        return;
      } else if (_index >= widget.productController.products.length) {
        // 如果当前索引超出范围，调整为最后一个有效索引
        _index = widget.productController.products.length - 1;
        debugPrint('Adjusted index after deletion: $_index');
      }
      
      actionEvent(0);
    } else {
      ToastWidget.showError(AppLocalizations.of(context)!.failedToDeleteImage);
    }
  }

  createAppBar() {
    // 添加边界检查
    if (_index < 0 || _index >= widget.productController.products.length) {
      debugPrint('Invalid index in createAppBar: $_index');
      return Container(); // 返回空容器避免崩溃
    }
    
    String createdAt = widget.productController.products[_index].createdAt ?? "";
    if (createdAt.isNotEmpty && createdAt.contains(':')) {
      createdAt = createdAt.substring(0, createdAt.lastIndexOf(':'));
    }
    return SlideTransition(
      position: _animation,
      child: Container(
          height: 60 + MediaQuery.of(context).padding.top, // 添加状态栏高度
          padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top), // 使用系统状态栏高度
          decoration: BoxDecoration(
            color: ThemeManager.currentTheme.backgroundColor.withValues(alpha: .9),
          ),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      ButtonWidget.createImageButton(ThemeManager.getImagePath('btn_back'), 40, 40, onPressed: () {
                        Util.closeView(widget);
                      }),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          child: Text(
                            AppLocalizations.of(context)!.completedTranslationAt(createdAt),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: ThemeManager.currentTheme.textColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 删除按钮 
              Container(
                padding: const EdgeInsets.only(right: 10),
                child: ButtonWidget.createImageButton(
                  ThemeManager.getImagePath('icon_delete'),
                  24,
                  24,
                  onPressed: () {
                    DialogManager.instance.showDeleteDialog(
                        message: AppLocalizations.of(context)!.deleteThisItem,
                        onConfirm: (){
                          deleteImage();
                        }
                    );
                  }
                ),
              ),
            ],
          )),
    );
  }

  Widget createCacheImage(Product item) {
    // 如果有缓存文件且允许使用本地文件
    if (item.images.isNotEmpty && item.images[0].localFileName != null && item.images[0].localFileName!.isNotEmpty) {
      final imagePath = ProductImage.localCacheDir.path + '/' + item.images[0].localFileName!;
      debugPrint('Using cached image: $imagePath');
      return Image.file(
        File(imagePath),
        // 改为 fitWidth，保持图片宽度适应屏幕，高度自然展开
        fit: BoxFit.fitWidth,
        width: MediaQuery.of(context).size.width,
        // 添加对齐方式，从顶部开始显示
        alignment: Alignment.topCenter,
      );
    }

    debugPrint('Using network image: ${item.images[0].url}');
    return Hero(
      tag: 'viewer_original_${item.productId}_$_index',
      child: CachedNetworkImage(
        imageUrl: item.images[0].url!,
        // 改为 fitWidth，保持图片宽度适应屏幕，高度自然展开
        fit: BoxFit.fitWidth,
        // 添加对齐方式，从顶部开始显示
        alignment: Alignment.topCenter,
        fadeInDuration: Duration.zero,
        memCacheWidth: (MediaQuery.of(context).size.width * MediaQuery.of(context).devicePixelRatio).round(),
        errorWidget: (context, url, error) {
          debugPrint('Error loading image: $error');
          return Container(
            width: MediaQuery.of(context).size.width,
            color: Colors.grey[200],
            child: const Center(
              child: Icon(Icons.error_outline, size: 48, color: Colors.grey),
            ),
          );
        },
      ),
    );
  }

  createResult(Product item, index) async {
    if (index < 0 || index >= widget.productController.products.length) {
      debugPrint('Invalid index in createResult: $index');
      return;
    }
    
    // 重置图片位置和缩放
    _resetZoom();
    
    // 检查是否有翻译结果
    if (item.images.isEmpty || item.status != Product.STATUS_TRANSLATED) {
      debugPrint('CreateResult - No translation available, showing original image');
      _imageItem = createCacheImage(item);
    } else {
      debugPrint('CreateResult - Translation available, creating text overlay');
      final firstImage = item.images[0];
      Map<String, dynamic> caches = {};
      if (firstImage.localFileName != null) {
        caches["imagePath"] = firstImage.localFileName!;
        debugPrint('CreateResult - Using cached image: ${firstImage.localFileName}');
      }

      _imageItem = Hero(
        tag: 'viewer_translated_${item.productId}_$_index',
        child: TextOverlay(firstImage.url!, firstImage.translateResult!, caches: caches),
      );
      _originImage = createCacheImage(item);

      setState(() {
        _scale = 1.0;
      });
    }
  }

  showCatalog(bool catalog) {
    setState(() {
      _showCatalog = catalog;
      if (catalog) {
        _hideSettingsPanel(); // 显示目录时关闭设置面板
      }
    });
  }

  void _showSettingsPanel() {
    setState(() {
      _showSettings = true;
      _showCatalog = false; // 确保目录面板关闭
    });
    _settingsController.forward();
  }

  void _hideSettingsPanel() {
    _settingsController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showSettings = false;
        });
      }
    });
  }

  selectIndex(index) {
    if (index < 0 || index >= widget.productController.products.length) {
      debugPrint('Invalid index: $index');
      return;
    }

    setState(() {
      _index = index;
    });

    var item = widget.productController.products[_index];
    createResult(item, _index);
  }

  showBar() {
    setState(() {
      _showAppBar = true;
      _showBottomBar = true;
      _enablePageButtons = false; // 打开操作区后翻页按钮消失
    });
    _inController.reverse();
    _inControllerLeft.reverse();
  }

  hideBar() {
    _inControllerLeft.forward();
    _inController.forward().then((value) {
      setState(() {
        _showAppBar = false;
        _showBottomBar = false;
        _enablePageButtons = true; // 关闭操作区后重新启用翻页按钮
        _showPageButtonsVisually = true; // 重新显示翻页按钮的视觉效果
      });
    });
  }

  isAllFolder(List<Product> list) {
    for (var i = 0; i < list.length; i++) {
      if (list[i].images.length == 1) {
        return false;
      }
    }
    return true;
  }

  actionEvent(int offset) {
    List<Product> list = widget.productController.products;
    if (list.isEmpty || isAllFolder(list)) {
      Util.closeView(widget);
      return;
    }

    // 计算新索引前确保当前索引有效
    if (_index < 0 || _index >= list.length) {
      // 如果当前索引无效，尝试重置为有效值
      _index = list.isNotEmpty ? 0 : -1;
      if (_index < 0) {
        debugPrint('Cannot navigate: product list is empty');
        Util.closeView(widget);
        return;
      }
    }

    int newIndex = (_index + offset + list.length) % list.length;
    if (newIndex < 0 || newIndex >= list.length) {
      debugPrint('Invalid index calculation: $newIndex');
      return;
    }

    _index = newIndex;
    if (_index < list.length && list[_index].images.length > 1) {
      actionEvent(offset == 0 ? 1 : offset);
      return;
    }

    // 再次检查索引有效性
    if (_index < 0 || _index >= list.length) {
      debugPrint('Invalid index after calculation: $_index');
      return;
    }

    var item = list[_index];
    if (!item.isRead) {
      widget.productController.markAsRead(item.productId!);
      setState(() {
        // 确保索引仍然有效
        if (_index < widget.productController.products.length) {
          widget.productController.products[_index] =
              widget.productController.products[_index];
        }
      });
    }

    setState(() {});
    createResult(item, _index);
  }
}

/// Bottom sheet settings panel with tab navigation
class _BottomSheetSettingsPanel extends StatefulWidget {
  final VoidCallback? onSettingsChanged;

  const _BottomSheetSettingsPanel({
    this.onSettingsChanged,
  });

  @override
  State<_BottomSheetSettingsPanel> createState() => _BottomSheetSettingsPanelState();
}

class _BottomSheetSettingsPanelState extends State<_BottomSheetSettingsPanel> {
  final TextOverlaySettings _settings = TextOverlaySettings();

  @override
  void initState() {
    super.initState();
    _settings.addListener(_onSettingsChanged);

    // Ensure settings are initialized
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!_settings.initialized) {
        await _settings.loadSettings();
      }
    });
  }

  @override
  void dispose() {
    _settings.removeListener(_onSettingsChanged);
    super.dispose();
  }

  void _onSettingsChanged() {
    if (mounted) {
      setState(() {});
      widget.onSettingsChanged?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Handle bar
        // Container(
        //   width: 40,
        //   height: 4,
        //   margin: const EdgeInsets.only(top: 8, bottom: 8),
        //   decoration: BoxDecoration(
        //     color: ThemeManager.currentTheme.textColor.withValues(alpha: .3),
        //     borderRadius: BorderRadius.circular(2),
        //   ),
        // ),
        const SizedBox(height: 6),
        // StyleSettingsWidget contains its own tab system
        Expanded(
          child: StyleSettingsWidget(
            settings: _settings,
            onChanged: () {
              setState(() {});
            },
          ),
        ),
      ],
    );
  }
}