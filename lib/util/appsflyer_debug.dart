import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:imtrans/services/appsflyer_service.dart';
import 'package:imtrans/services/event_log.dart';

/// AppsFlyer Debug and Testing Utilities
/// 
/// Provides debugging tools and test functions for AppsFlyer integration
class AppsFlyerDebug {
  
  /// Show debug information about AppsFlyer status
  static Future<void> showDebugInfo(BuildContext context) async {
    if (!kDebugMode) {
      debugPrint('AppsFlyer Debug: Only available in debug mode');
      return;
    }
    
    final isInitialized = AppsFlyerService.isInitialized;
    final appsFlyerUID = await AppsFlyerService.getAppsFlyerUID();
    
    if (context.mounted) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('AppsFlyer Debug Info'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Initialized: ${isInitialized ? "✅" : "❌"}'),
                const SizedBox(height: 8),
                Text('AppsFlyer UID: ${appsFlyerUID ?? "Not available"}'),
                const SizedBox(height: 8),
                Text('Debug Mode: ${kDebugMode ? "✅" : "❌"}'),
                const SizedBox(height: 16),
                const Text('Test Events:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => testAppLaunch(),
                  child: const Text('Test App Launch'),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  onPressed: () => testRegistration(),
                  child: const Text('Test Registration'),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  onPressed: () => testPurchase(),
                  child: const Text('Test Purchase'),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  onPressed: () => testContentView(),
                  child: const Text('Test Content View'),
                ),
                const SizedBox(height: 4),
                ElevatedButton(
                  onPressed: () => checkConversionData(),
                  child: const Text('Check Conversion Data'),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          );
        },
      );
    }
  }
  
  /// Test app launch event
  static Future<void> testAppLaunch() async {
    debugPrint('🧪 Testing AppsFlyer app launch event');
    await AppsFlyerService.logEvent('test_app_launch', {
      'test_mode': 'true',
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
    debugPrint('✅ App launch test event sent');
  }
  
  /// Test user registration event
  static Future<void> testRegistration() async {
    debugPrint('🧪 Testing AppsFlyer registration event');
    await EventLogService.logSignUp(signUpMethod: 'test_method');
    debugPrint('✅ Registration test event sent');
  }
  
  /// Test purchase event
  static Future<void> testPurchase() async {
    debugPrint('🧪 Testing AppsFlyer purchase event');
    await EventLogService.logPurchase(
      value: 9.99,
      currency: 'USD',
      itemId: 'test_product_001',
      itemName: 'Test Premium Subscription',
    );
    debugPrint('✅ Purchase test event sent');
  }
  
  /// Test content view event
  static Future<void> testContentView() async {
    debugPrint('🧪 Testing AppsFlyer content view event');
    await EventLogService.logContentView(
      contentType: 'manga',
      itemId: 'test_manga_123',
      itemName: 'Test Manga Chapter',
    );
    debugPrint('✅ Content view test event sent');
  }
  
  /// Test login event
  static Future<void> testLogin() async {
    debugPrint('🧪 Testing AppsFlyer login event');
    await EventLogService.logLogin(loginMethod: 'test_login');
    debugPrint('✅ Login test event sent');
  }
  
  /// Test search event
  static Future<void> testSearch() async {
    debugPrint('🧪 Testing AppsFlyer search event');
    await EventLogService.logSearch(searchTerm: 'test search query');
    debugPrint('✅ Search test event sent');
  }
  
  /// Test share event
  static Future<void> testShare() async {
    debugPrint('🧪 Testing AppsFlyer share event');
    await EventLogService.logShare(
      contentType: 'manga',
      itemId: 'test_manga_456',
      method: 'test_share',
    );
    debugPrint('✅ Share test event sent');
  }
  
  /// Test custom event
  static Future<void> testCustomEvent() async {
    debugPrint('🧪 Testing AppsFlyer custom event');
    await AppsFlyerService.logEvent('test_custom_event', {
      'custom_param_1': 'test_value_1',
      'custom_param_2': 'test_value_2',
      'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    });
    debugPrint('✅ Custom test event sent');
  }

  /// Check conversion data and Install UID
  static Future<void> checkConversionData() async {
    debugPrint('🔍 Checking AppsFlyer conversion data...');

    if (!AppsFlyerService.isInitialized) {
      debugPrint('❌ AppsFlyer is not initialized');
      return;
    }

    // Get AppsFlyer UID
    final uid = await AppsFlyerService.getAppsFlyerUID();
    debugPrint('🔍 conversions.appsflyersdk - Install UID: $uid');

    // Request conversion data
    await AppsFlyerService.requestConversionData();

    debugPrint('📋 Conversion Data Check Complete');
    debugPrint('   - Look for "conversions.appsflyersdk" in logs');
    debugPrint('   - Install UID should appear above');
    debugPrint('   - If no conversion data appears, this might be a returning user');
  }
  
  /// Run all test events in sequence
  static Future<void> runAllTests() async {
    if (!kDebugMode) {
      debugPrint('AppsFlyer Debug: Tests only available in debug mode');
      return;
    }
    
    debugPrint('🧪 Running all AppsFlyer test events...');
    
    await testAppLaunch();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testRegistration();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testLogin();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testPurchase();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testContentView();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testSearch();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testShare();
    await Future.delayed(const Duration(milliseconds: 500));
    
    await testCustomEvent();
    
    debugPrint('✅ All AppsFlyer test events completed');
  }
  
  /// Validate AppsFlyer configuration
  static Future<bool> validateConfiguration() async {
    debugPrint('🔍 Validating AppsFlyer configuration...');
    
    // Check if AppsFlyer is initialized
    if (!AppsFlyerService.isInitialized) {
      debugPrint('❌ AppsFlyer is not initialized');
      return false;
    }
    
    // Check if we can get AppsFlyer UID
    final uid = await AppsFlyerService.getAppsFlyerUID();
    if (uid == null || uid.isEmpty) {
      debugPrint('❌ Cannot retrieve AppsFlyer UID');
      return false;
    }
    
    debugPrint('✅ AppsFlyer configuration is valid');
    debugPrint('   - Initialized: ✅');
    debugPrint('   - UID: $uid');
    
    return true;
  }
  
  /// Print AppsFlyer integration checklist
  static void printIntegrationChecklist() {
    if (!kDebugMode) return;
    
    debugPrint('📋 AppsFlyer Integration Checklist:');
    debugPrint('   1. ✅ SDK dependency added to pubspec.yaml');
    debugPrint('   2. ⚠️  Configure dev key in AppsFlyerService');
    debugPrint('   3. ⚠️  Configure iOS app ID in AppsFlyerService');
    debugPrint('   4. ✅ iOS Info.plist configured');
    debugPrint('   5. ✅ Android manifest configured');
    debugPrint('   6. ✅ Service initialized in main.dart');
    debugPrint('   7. ✅ Event tracking integrated');
    debugPrint('   8. ✅ Debug utilities available');
    debugPrint('');
    debugPrint('🔧 Next steps:');
    debugPrint('   - Replace YOUR_APPSFLYER_DEV_KEY with actual dev key');
    debugPrint('   - Replace YOUR_IOS_APP_ID with actual iOS app ID');
    debugPrint('   - Test in development environment');
    debugPrint('   - Verify events in AppsFlyer dashboard');
  }
  
  /// Show integration status widget
  static Widget buildStatusWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'AppsFlyer Status',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 8),
          FutureBuilder<bool>(
            future: validateConfiguration(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              }
              
              final isValid = snapshot.data ?? false;
              return Row(
                children: [
                  Icon(
                    isValid ? Icons.check_circle : Icons.error,
                    color: isValid ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text(isValid ? 'Configuration Valid' : 'Configuration Invalid'),
                ],
              );
            },
          ),
          const SizedBox(height: 16),
          if (kDebugMode) ...[
            ElevatedButton(
              onPressed: () => runAllTests(),
              child: const Text('Run All Tests'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => printIntegrationChecklist(),
              child: const Text('Print Checklist'),
            ),
          ],
        ],
      ),
    );
  }
}
