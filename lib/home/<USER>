// import 'dart:typed_data';
// import 'package:flutter/material.dart';
// import 'package:camera/camera.dart';
// // import 'package:imtrans/util/product.dart';
// import '../models/product_model.dart';
// import '../util/global.dart';
// import '../util/util.dart';
// import '../pages/draft/draft.dart';
// import '../controllers/product_controller.dart';

// class CameraView extends StatefulWidget {
//   final Function(bool have) finished;
//   const CameraView({required this.finished, super.key});

//   @override
//   State<CameraView> createState() => _CameraViewState();
// }

// class _CameraViewState extends State<CameraView> {
//   late CameraController _controller;
//   final List<CameraDescription> _cameras=Util.cameras;
//   late Future<void> _initializeControllerFuture;
//   double _height=0.0;
//   double _width=0.0;
//   List<Product> _files=[];
//   bool _openFlash=true;
//   bool initFinished=false;
//   double bottomBarHeight=140;
//   // ProductList _productList = ProductList.instance();

//   @override
//   void initState() {
//     super.initState();
//     getCameras();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     _controller.dispose();
//   }

//   Future<void> turnFlash() async {
//     setState(() {
//       _openFlash=!_openFlash;
//     });
//     await _controller.setFlashMode(_openFlash?FlashMode.torch:FlashMode.off);
//   }

//   removeAllList(){
//     if(_files.isNotEmpty){
//       _files.removeRange(0, _files.length);
//       setState(() {
//         _files=_files;
//       });
//     }
//     if(_productList.drafts.isNotEmpty){
//       _productList.drafts.removeRange(0, _productList.drafts.length);
//       setState(() {
//         _productList=_productList;
//       });
//     }
//   }

//   removeFilesProduct(Product item){
//     _files.removeWhere((element) => item==element);
//     setState(() {
//       _files=_files;
//     });
//   }

//   getCameras() async {
//     _controller = CameraController(
//       // 获取默认相机
//       _cameras[0],
//       ResolutionPreset.medium,
//       enableAudio: false
//     );
//     _initializeControllerFuture=_controller.initialize();
//     await _initializeControllerFuture;

//     turnFlash();
//     initFinished=true;
//   }

//   bool haveSaveList(){
//     for(var i=0;i<_files.length;i++){
//       Product item=_files[i];
//       if(!item.save){
//         return true;
//       }
//     }
//     return false;
//   }

//   flush(){
//     setState(() {
//       _productList=_productList;
//     });
//   }

//   onFinishedEvent(){
//     Util.closeView(widget);
//     widget.finished(haveSaveList());
//   }

//   gotoDraft(){
//     Util.navigatorPush(DraftView(cameraDart: this,));
//   }

//   createImageList(){
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.end,
//       crossAxisAlignment: CrossAxisAlignment.end,
//       children: [
//         _files.isNotEmpty?SizedBox(
//           width: 70,
//           height: 70,
//           child: Stack(
//             children: [
//               GestureDetector(
//                 onTapUp: (e){
//                   gotoDraft();
//                 },
//                 child: Container(
//                   margin: const EdgeInsets.only(top: 5),
//                   clipBehavior: Clip.hardEdge,
//                   decoration: BoxDecoration(
//                     color: Colors.transparent,
//                     borderRadius: BorderRadius.circular(5),
//                   ),
//                   child: Image.memory(_files[0].fileData!,width: 60,height: 60,fit: BoxFit.fitWidth,),
//                 ),
//               ),
//               Positioned(
//                   right: 5,
//                   top: 0,
//                   child: ClipOval(
//                     child: Container(
//                       width: 22,
//                       height: 22,
//                       color: Colors.red,
//                       alignment: Alignment.center,
//                       child: Text("${_files.length}",textAlign: TextAlign.center,style: const TextStyle(
//                           fontSize: 12,
//                           color: Colors.white
//                       ),),
//                     ),
//                   )
//               )
//             ],
//           ),
//         ):Container(),
//         _files.isNotEmpty?Container(
//           height: 70,
//           margin: const EdgeInsets.only(right: 10,bottom: 5),
//           child: Center(
//             child: GestureDetector(
//               onTapUp: (e){
//                 gotoDraft();
//                 // setState(() {
//                 //   _cuurentIndex=(_cuurentIndex+1)%(_files.length);
//                 // });
//               },
//               child: ClipOval(
//                 child: Container(
//                   color: const Color(0x7f1b1c1e),
//                   padding: const EdgeInsets.all(4),
//                   child: const Icon(Icons.keyboard_arrow_right,color: Colors.white,size: 24,),
//                 ),
//               ),
//             ),
//           ),
//         ):Container()
//       ],
//     );
//   }

//   createCamera(){
//     double imageHeight=_height-Global().barHeight-Global().top-MediaQuery.of(context).padding.bottom-140;
//     final deviceRatio = _width / imageHeight;
//     return FutureBuilder<void>(
//       future: _initializeControllerFuture,
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.done) {
//           return Column(
//             children: [
//               createBar(),
//               SizedBox(
//                 height: imageHeight,
//                 child: Stack(
//                   children: [
//                     AspectRatio(
//                       aspectRatio: deviceRatio,
//                       child: CameraPreview(_controller),
//                     ),
//                     Container(
//                       alignment: Alignment.bottomRight,
//                       padding: const EdgeInsets.only(bottom: 5),
//                       child: createImageList(),
//                     )
//                   ],
//                 ),
//               ),
//               Expanded(
//                   child: createBottom(),
//               )
//             ],
//           );
//         } else {
//           // Otherwise, display a loading indicator.
//           return const Center(child: CircularProgressIndicator());
//         }
//       },
//     );
//   }

//   createBar(){
//     return Container(
//       color: Colors.black,
//       width: _width,
//       height: Global().barHeight,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           IconButton(
//             onPressed: (){
//               onFinishedEvent();
//             },
//             icon: Row(
//               children: [
//                 const SizedBox(width: 10,),
//                 Image(image: AssetImage(Global().image_camera_back),height: 14,fit: BoxFit.fitHeight,),
//                 const SizedBox(width: 10,),
//                 const Text("Back",style: TextStyle(
//                     color: Colors.white,
//                     fontWeight: FontWeight.w600,
//                     fontSize: 14
//                 ),)
//               ],
//             ),
//           ),
//           IconButton(
//             onPressed: (){
//               turnFlash();
//             },
//             icon: Row(
//               children: [
//                 Image(image: AssetImage(_openFlash?Global().image_light:Global().image_no_light),height: 20,fit: BoxFit.fitHeight,),
//                 const SizedBox(width: 20,),
//               ],
//             )
//           )
//         ],
//       ),
//     );
//   }

//   createBottom(){
//     return Container(
//       width: _width,
//       height: bottomBarHeight,
//       color: Colors.black,
//       alignment: Alignment.center,
//       padding: const EdgeInsets.only(left: 20,right: 20),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceAround,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Container(
//             width: (_width-40)/3,
//           ),
//           Container(
//             width: (_width-40)/3,
//             alignment: Alignment.center,
//             child: IconButton(
//                 onPressed: () async {
//                   //if(_account!.isVip() || Util.freeLimit>0){
//                     await _initializeControllerFuture;
//                     XFile xFile = await _controller.takePicture();

//                     //File file = File(xFile.path);
//                     var name = ProductController.generateImageName(xFile.name);
//                     name=Util.getFolderName(name);
//                     Product product = await _productList.addProductDraftFromMap({
//                       "product_name": name,
//                       "target_lang": "en",
//                       "cover_img":  "cover_$name",
//                       "product_images": [{"image_name": name, "image_url": name,"translate_status": 0}]
//                     });
//                     Uint8List data=await xFile.readAsBytes();
//                     product.setFileData(data);
//                     product.setCreateAt(Util.getCurrentTimeNormal());
//                     //Util.setFreeLimit(Util.freeLimit-1);
//                     setState(() {
//                       _files.add(product);
//                     });
//                   // }else{
//                   //   Util.showDialog(
//                   //       "Tip",
//                   //       "You only have ${Util.freeLimit} uses left, Purchasing VIP allows unlimited usage",
//                   //       submit: (){
//                   //         Util.navigatorPush(const Vip());
//                   //       },
//                   //       submitTitle: "OK",
//                   //       cancelTitle: "Cancel",
//                   //       cancel: (){}
//                   //   );
//                   // }
//                 },
//                 icon: Image(image: AssetImage(Global().image_takeCamera),width: 72,height: 72,fit: BoxFit.fill,)
//             ),
//           ),
//           Container(
//             width: (_width-40)/3,
//             alignment: Alignment.center,
//             child: GestureDetector(
//                 onTapUp: (e){
//                   onFinishedEvent();
//                 },
//                 child: Container(
//                   width: 82,
//                   height: 38,
//                   alignment: Alignment.center,
//                   decoration: BoxDecoration(
//                       color: Global().greenColor,
//                       borderRadius: BorderRadius.circular(10)
//                   ),
//                   child: const Text("  Confirm  ",style: TextStyle(
//                       color: Colors.black,
//                       fontSize: 14,
//                       fontWeight: FontWeight.w600
//                   ),),
//                 )
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     var size=MediaQuery.of(context).size;
//     _height = size.height;
//     _width = size.width;
//     return Scaffold(
//       backgroundColor: Colors.black,
//       appBar: AppBar(
//         backgroundColor: Colors.black,
//         toolbarHeight: 0,
//       ),
//       body: SizedBox(
//         width: _width,
//         height: _height,
//         child: initFinished?createCamera():Container(),
//       ),
//     );
//   }
// }