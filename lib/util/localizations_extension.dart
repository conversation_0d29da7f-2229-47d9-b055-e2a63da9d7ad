import 'package:imtrans/l10n/generated/app_localizations.dart';

extension AppLocalizationsExtension on AppLocalizations {
  String getLanguageName(String code) {
    switch (code.toLowerCase()) {
      case 'en': return english;
      case 'ja': return japanese;
      case 'ko': return korean;
      case 'fr': return french;
      case 'de': return german;
      case 'es': return spanish;
      case 'it': return italian;
      case 'th': return thai;
      case 'vi': return vietnamese;
      case 'id': return indonesian;
      case 'ms': return malay;
      case 'zh': return chinese;
      default: return code;
    }
  }
  
  Map<String, String> getLanguageMap() {
    return {
      'en': english,
      'ja': japanese,
      'ko': korean,
      'fr': french,
      'de': german,
      'es': spanish,
      'it': italian,
      'th': thai,
      'vi': vietnamese,
      'id': indonesian,
      'ms': malay,
      'zh': chinese,
    };
  }
  
  String translateTo(String code) {
    return this.toLanguage(getLanguageName(code));
  }

  bool isLongText(String languageCode) {
    return isLanguageIn(languageCode, longText);
  }
  
  // 检查当前语言是否为指定的语言之一
  bool isLanguageIn(String languageCode, List<String> languageCodes) {
    return languageCodes.contains(languageCode.toLowerCase());
  }
  
  // 检查当前语言是否不是指定的语言之一
  bool isNotLanguageIn(String languageCode, List<String> languageCodes) {
    return !isLanguageIn(languageCode, languageCodes);
  }
  
  // 预定义的语言组
  static List<String> get longText => ['de', 'fr', 'es', 'it', 'pt'];
  static List<String> get latinLanguages => ['fr', 'es', 'it', 'pt'];
  static List<String> get asianLanguages => ['zh', 'ja', 'ko', 'th', 'vi', 'id', 'ms'];
  
  /// 根据产品名称返回对应的多语言字符串
  String getProductNameLocalized(String name) {
    name = name.toLowerCase().replaceAll(" ", "");
    if (name.contains("freetrial")) {
      return freeTrial;
    } else if (name.contains("weekly") || name.contains("week")) {
      return weekly;
    } else if (name.contains("annual") || name.contains("year")) {
      return annual;
    } else {
      return name; // 如果没有匹配的翻译，返回原始名称
    }
  }
}