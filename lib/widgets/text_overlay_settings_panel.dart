import 'package:flutter/material.dart';
import 'package:imtrans/util/text_overlay_settings.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:google_fonts/google_fonts.dart';

/// 文本覆盖层设置面板
/// 提供用户界面来调整文本样式、颜色、滤镜等设置
class TextOverlaySettingsPanel extends StatefulWidget {
  final VoidCallback? onSettingsChanged;
  
  const TextOverlaySettingsPanel({
    super.key,
    this.onSettingsChanged,
  });

  @override
  State<TextOverlaySettingsPanel> createState() => _TextOverlaySettingsPanelState();
}

class _TextOverlaySettingsPanelState extends State<TextOverlaySettingsPanel> {
  final TextOverlaySettings _settings = TextOverlaySettings();
  
  @override
  void initState() {
    super.initState();
    _settings.addListener(_onSettingsChanged);
    // 确保设置已经初始化
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!_settings.initialized) {
        await _settings.loadSettings();
      }
    });
  }
  
  @override
  void dispose() {
    _settings.removeListener(_onSettingsChanged);
    super.dispose();
  }
  
  void _onSettingsChanged() {
    if (mounted) {
      setState(() {});
      widget.onSettingsChanged?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320,
      height: double.infinity,
      decoration: BoxDecoration(
        color: ThemeManager.currentTheme.backgroundColor.withValues(alpha: .9),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: ThemeManager.currentTheme.backgroundColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)!.textStyleSettings,
                    style: TextStyle(
                      color: ThemeManager.currentTheme.textColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: .2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () => _settings.resetToDefaults(),
                    icon: Icon(Icons.restart_alt, color: ThemeManager.currentTheme.textColor, size: 20),
                    tooltip: AppLocalizations.of(context)!.resetToDefaults,
                  ),
                ),
              ],
            ),
          ),
          
          // 设置内容
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildFontSection(),
                  const SizedBox(height: 24),
                  _buildStrokeSection(),
                  const SizedBox(height: 24),
                  _buildShadowSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSectionTitle(String title) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [ThemeManager.currentTheme.textColor, ThemeManager.currentTheme.textColor.withValues(alpha: .5)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              color: ThemeManager.currentTheme.textColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFontSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(AppLocalizations.of(context)!.fontSettings),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: .08),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF6366F1).withValues(alpha: .3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 字体选择
              _buildFontFamilySelector(),
              const SizedBox(height: 20),
              // 字体大小
              _buildFontSizeSlider(),
              const SizedBox(height: 20),
              // 字体粗细
              _buildFontWeightSlider(),
              const SizedBox(height: 20),
              // 字体颜色
              _buildFontColorPicker(),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildFontFamilySelector() {
    final fontMap = {
      'Inter': 'Inter',
      'Comic Sans MS': 'Comic Sans MS',
      'Arial': 'Arial',
      'Helvetica': 'Helvetica',
      'Comic Neue': 'Comic Neue', // Google Font - 漫画风格
      'Bangers': 'Bangers', // Google Font - 漫画/卡通风格
      'Luckiest Guy': 'Luckiest Guy', // Google Font - 卡通风格
      'Creepster': 'Creepster', // Google Font - 恐怖漫画
      'Fredoka One': 'Fredoka One', // Google Font - 友好卡通
      'Bungee': 'Bungee', // Google Font - 现代漫画
      'Righteous': 'Righteous', // Google Font - 现代风格
      'Kalam': 'Kalam', // Google Font - 手写风格
      'Architects Daughter': 'Architects Daughter', // Google Font - 手绘风格
      'Caveat': 'Caveat', // Google Font - 随意手写
    };
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
          decoration: BoxDecoration(
            color: const Color(0xFF6366F1).withValues(alpha: .1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFF6366F1).withValues(alpha: .3),
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _settings.fontFamily,
              dropdownColor: ThemeManager.currentTheme.backgroundColor,
              style: TextStyle(
                color: ThemeManager.currentTheme.textColor,
                fontSize: 14,
              ),
              items: fontMap.entries.map((entry) {
                return DropdownMenuItem<String>(
                  value: entry.key,
                  child: Text(
                    entry.value,
                    style: _getFontStyle(entry.key).copyWith(
                      color: ThemeManager.currentTheme.textColor,
                      fontSize: 14,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  _settings.setFontFamily(newValue);
                }
              },
            ),
          ),
        ),
      ],
    );
  }
  
  // 获取字体样式的辅助方法
  TextStyle _getFontStyle(String fontFamily) {
    switch (fontFamily) {
      case 'Comic Neue':
        return GoogleFonts.comicNeue();
      case 'Bangers':
        return GoogleFonts.bangers();
      case 'Luckiest Guy':
        return GoogleFonts.luckiestGuy();
      case 'Creepster':
        return GoogleFonts.creepster();
      case 'Fredoka One':
        return GoogleFonts.fredoka();
      case 'Bungee':
        return GoogleFonts.bungee();
      case 'Righteous':
        return GoogleFonts.righteous();
      case 'Kalam':
        return GoogleFonts.kalam();
      case 'Architects Daughter':
        return GoogleFonts.architectsDaughter();
      case 'Caveat':
        return GoogleFonts.caveat();
      case 'Inter':
        return GoogleFonts.inter();
      default:
        return TextStyle(fontFamily: fontFamily);
    }
  }
  
  Widget _buildFontSizeSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.fontSize,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: const Color(0xFF6366F1),
                  inactiveTrackColor: const Color(0xFF6366F1).withValues(alpha: .2),
                  thumbColor: const Color(0xFF6366F1),
                  overlayColor: const Color(0xFF6366F1).withValues(alpha: .2),
                  trackHeight: 4,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: _settings.fontSize,
                  min: 0.5,
                  max: 2.0,
                  divisions: 15,
                  label: '${(_settings.fontSize * 100).round()}%',
                  onChanged: (value) => _settings.setFontSize(value),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              width: 60,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withValues(alpha: .1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${(_settings.fontSize * 100).round()}%',
                style: const TextStyle(
                  color: Color(0xFF6366F1),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildFontWeightSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.fontWeight,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: const Color(0xFF6366F1),
                  inactiveTrackColor: const Color(0xFF6366F1).withValues(alpha: .2),
                  thumbColor: const Color(0xFF6366F1),
                  overlayColor: const Color(0xFF6366F1).withValues(alpha: .2),
                  trackHeight: 4,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: _settings.fontWeight.value.toDouble(),
                  min: 100.0,
                  max: 900.0,
                  divisions: 8,
                  label: _settings.fontWeight.value.toString(),
                  onChanged: (value) {
                    final weightValue = value.round();
                    FontWeight newWeight;
                    if (weightValue <= 200) {
                      newWeight = FontWeight.w100;
                    } else if (weightValue <= 300) {
                      newWeight = FontWeight.w200;
                    } else if (weightValue <= 400) {
                      newWeight = FontWeight.w300;
                    } else if (weightValue <= 500) {
                      newWeight = FontWeight.w400;
                    } else if (weightValue <= 600) {
                      newWeight = FontWeight.w500;
                    } else if (weightValue <= 700) {
                      newWeight = FontWeight.w600;
                    } else if (weightValue <= 800) {
                      newWeight = FontWeight.w700;
                    } else if (weightValue <= 900) {
                      newWeight = FontWeight.w800;
                    } else {
                      newWeight = FontWeight.w900;
                    }
                    _settings.setFontWeight(newWeight);
                  },
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              width: 60,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withValues(alpha: .1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _settings.fontWeight.value.toString(),
                style: const TextStyle(
                  color: Color(0xFF6366F1),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildFontColorPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.textColor,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        _buildColorPicker(_settings.textColor, (color) => _settings.setTextColor(color)),
      ],
    );
  }
  
  Widget _buildStrokeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(AppLocalizations.of(context)!.strokeSettings),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: .08),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF6366F1).withValues(alpha: .3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 描边颜色
              _buildStrokeColorPicker(),
              const SizedBox(height: 20),
              // 描边宽度
              _buildStrokeWidthSlider(),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildStrokeColorPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.strokeLabel,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        _buildColorPicker(_settings.strokeColor, (color) => _settings.setStrokeColor(color)),
      ],
    );
  }
  
  Widget _buildStrokeWidthSlider() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.strokeWidth,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: const Color(0xFF6366F1),
                  inactiveTrackColor: const Color(0xFF6366F1).withValues(alpha: .2),
                  thumbColor: const Color(0xFF6366F1),
                  overlayColor: const Color(0xFF6366F1).withValues(alpha: .2),
                  trackHeight: 4,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: _settings.strokeWidth,
                  min: 0.0,
                  max: 10.0,
                  divisions: 20,
                  label: _settings.strokeWidth.toStringAsFixed(1),
                  onChanged: (value) => _settings.setStrokeWidth(value),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              width: 60,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withValues(alpha: .1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _settings.strokeWidth.toStringAsFixed(1),
                style: const TextStyle(
                  color: Color(0xFF6366F1),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  Widget _buildColorPicker(Color currentColor, Function(Color) onColorChanged) {
    final colors = [
      Colors.black,
      Colors.white,
      Colors.red,
      Colors.blue,
      const Color(0xFF6366F1), // 紫色
      Colors.yellow,
      Colors.orange,
      const Color(0xFF10B981), // 现代绿色
    ];
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        final isSelected = currentColor.toARGB32() == color.toARGB32();
        return GestureDetector(
          onTap: () => onColorChanged(color),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected 
                  ? const Color(0xFF6366F1)
                  : Colors.grey.withValues(alpha: .3),
                width: isSelected ? 3 : 1,
              ),
              boxShadow: isSelected ? [
                BoxShadow(
                  color: const Color(0xFF6366F1).withValues(alpha: .3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ] : null,
            ),
            child: isSelected ? const Icon(
              Icons.check,
              color: Colors.white,
              size: 16,
            ) : null,
          ),
        );
      }).toList(),
    );
  }
  
  Widget _buildShadowSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(AppLocalizations.of(context)!.shadowEffect),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: .08),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF6366F1).withValues(alpha: .3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              // 阴影透明度
              _buildSliderRow(
                '${AppLocalizations.of(context)!.opacity}: ${(_settings.shadowOpacity * 100).round()}%',
                _settings.shadowOpacity,
                0.0,
                1.0,
                20,
                (value) => _settings.setShadowSettings(opacity: value),
              ),
              const SizedBox(height: 16),
              // 阴影偏移
              _buildSliderRow(
                '${AppLocalizations.of(context)!.horizontalOffset}: ${_settings.shadowOffset.dx.toStringAsFixed(1)}',
                _settings.shadowOffset.dx,
                -5.0,
                5.0,
                20,
                (value) => _settings.setShadowSettings(offsetX: value),
              ),
              const SizedBox(height: 16),
              _buildSliderRow(
                '${AppLocalizations.of(context)!.verticalOffset}: ${_settings.shadowOffset.dy.toStringAsFixed(1)}',
                _settings.shadowOffset.dy,
                -5.0,
                5.0,
                20,
                (value) => _settings.setShadowSettings(offsetY: value),
              ),
              const SizedBox(height: 16),
              // 阴影模糊半径
              _buildSliderRow(
                '${AppLocalizations.of(context)!.blurRadius}: ${_settings.shadowBlurRadius.toStringAsFixed(1)}',
                _settings.shadowBlurRadius,
                0.0,
                10.0,
                20,
                (value) => _settings.setShadowSettings(blurRadius: value),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildSliderRow(String label, double value, double min, double max, int divisions, Function(double) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: ThemeManager.currentTheme.textColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF6366F1),
            inactiveTrackColor: const Color(0xFF6366F1).withValues(alpha: .2),
            thumbColor: const Color(0xFF6366F1),
            overlayColor: const Color(0xFF6366F1).withValues(alpha: .2),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }
} 