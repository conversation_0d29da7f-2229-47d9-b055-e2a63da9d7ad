import 'package:flutter/material.dart';
import 'package:imtrans/widgets/common/common_widgets.dart';
import '../../services/iap_purchases.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

class PurchaseViewV3 extends StatefulWidget {
  /// 获取该视图的背景颜色
  static Color getBackgroundColor() {
    return const Color(0xffeef6ff); // 浅蓝色背景
  }
  
  final List products;
  final String? userUuid;
  final Function(String?) onBuyFailed;
  final Function() onBuySucceed;
  
  const PurchaseViewV3({
    super.key,
    required this.products,
    required this.userUuid,
    required this.onBuyFailed,
    required this.onBuySucceed,
  });
  
  @override
  State<PurchaseViewV3> createState() => _PurchaseViewV3State();
}

class _PurchaseViewV3State extends State<PurchaseViewV3> {
  var _index = 0;
  bool _isLoading = false;
  late String descText;
  
  @override
  void initState() {
    super.initState();
    // 默认选择周订阅
    _index = 1;
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    descText = AppLocalizations.of(context)!.subscriptionDescription;
  }
  
  Widget _buildSubscriptionOption(int index, double width) {
    if (index >= widget.products.length) {
      return Container();
    }
    
    var item = widget.products[index];
    var isPopular = item.id.toLowerCase().contains("week");
    String name = IAPPurchases.getProductName(item.id);
    // String price = item.price;
    // String currencySymbol = item.currencySymbol;
    // String pricePerDay = currencySymbol + (item.rawPrice / (isMonth ? 30 : 365)).toStringAsFixed(2);
    
    // 是否为选中状态
    bool isSelected = index == _index;
    
    // 月度订阅显示原价和折扣价
    // String displayText = isMonth ? 
    //     "First month $discountPrice then $price" : 
    //     "Only $pricePerDay Per Day";
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _index = index;
        });
      },
      child: Container(
        width: width,
        height: 120,
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFFFFF) : const Color(0xfff8f8f8),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isSelected ? const Color(0xFFffffff) : const Color(0xffd9d9d9),
            width: isSelected ? 0 : 0,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.black.withValues(alpha: .15),
              blurRadius: 15,
              offset: const Offset(0, 4),
            ),
          ] : [],
        ),
        child: Stack(
          children: [
            // 主要内容
            Padding(
              padding: const EdgeInsets.fromLTRB(17.0, 26.0, 6.0, 14.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 订阅类型
                  Text(
                    name,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? const Color(0xFF1B1C1E) : const Color(0xff707070),
                    ),
                  ),
                  const SizedBox(height: 6),
                  // 价格信息
                  Row(
                    children: [
                      Text(
                        item.discountPrice,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Color(0xFF1B1C1E) : const Color(0xff707070),
                        ),
                      ),
                      // const SizedBox(width: 8),
                      // if (price != item.discountPrice && item.discountPrice != "$currencySymbol"+'0') 
                      //   Text(
                      //     price,
                      //     style: const TextStyle(
                      //       fontSize: 20,
                      //       fontWeight: FontWeight.normal,
                      //       color: Color(0xFF939393), // 修改为灰色
                      //       decoration: TextDecoration.lineThrough,
                      //     ),
                      //   ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  // 每日价格或首月折扣信息
                  Text(
                    "", //displayText,
                    style: TextStyle(
                      fontSize: 10,
                      color: isSelected ? Color(0xFF939393) : const Color(0xff707070),
                    ),
                  ),
                ],
              ),
            ),
            
            // Popular 标签 (周订阅上显示)
            if (isPopular) Positioned(
              top: -1,
              right: -1,
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(10) ,
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: const BoxDecoration(
                    color: Color(0xFFcdee2d),
                    borderRadius: BorderRadius.only(
                      // topRight: Radius.circular(4),
                      // bottomRight: Radius.circular(4),
                      // topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(100),
                    ),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.popular,
                    style: const TextStyle(
                      color: Color(0xff1b1c1e),
                      fontSize: 10,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 0.05,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        children: [
          Container(
            width: 11,
            height: 11,
            child: Image.asset("images/pay/greentick.png"),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF1B1C1E),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFreeTrialButton(double width) {
    // 获取当前选中产品的折扣价格
    String buttonText = AppLocalizations.of(context)!.subscribe;
    
    if (widget.products.isNotEmpty && _index < widget.products.length) {
      var item = widget.products[_index];
      String price = item.price;
      
      // 使用抽象方法获取折扣价格
      String discountPrice = item.discountPrice;
      
      // 如果折扣价格为0或包含"0"，则显示"Free Trial"
      if (discountPrice == "0" || discountPrice == "0.00" || discountPrice.contains("0.00") || discountPrice == "${item.currencySymbol}0") {
        buttonText = AppLocalizations.of(context)?.freeTrial ?? "Free Trial";
        setState(() {
          descText = AppLocalizations.of(context)!.freeTrialDescription(price);
        });
      }
      else {
        setState(() {
          descText = AppLocalizations.of(context)!.subscriptionDescription;
        });
      }
    }
    
    return GestureDetector(
      onTap: _isLoading ? null : () async {
        if (_isLoading) return;
        
        setState(() {
          _isLoading = true;
        });

        try {
          if (widget.userUuid == null) {
            throw Exception(AppLocalizations.of(context)?.accountError ?? 'Account error');
          }

          if (widget.products.isEmpty || _index >= widget.products.length) {
            throw Exception(AppLocalizations.of(context)?.noProductsError ?? 'No products available');
          }
        
          final selectedProductId = widget.products[_index].id;
          await IAPPurchases().buyProduct(
            selectedProductId,
            uuid: widget.userUuid!,
            onSuccess: () {
              setState(() {
                _isLoading = false;
              });
              widget.onBuySucceed();
            },
            onError: (error) {
              setState(() {
                _isLoading = false;
              });
              widget.onBuyFailed(error);
            },
            onCancel: () {
              setState(() {
                _isLoading = false;
              });
              // 用户取消购买，不显示错误提示，只重置loading状态
            },
          );
        } catch (e) {
          setState(() {
            _isLoading = false;
          });
          widget.onBuyFailed(e.toString());
        }
      },
      child: Container(
        width: width,
        height: 56,
        decoration: BoxDecoration(
          color: const Color(0xFF1B1C1E),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (!_isLoading) Text(
              buttonText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_isLoading) const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Widget _buildFooterLinks() {
  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.center,
  //     children: [
  //       TextButton(
  //         onPressed: () {
  //           Util.navigatorTransparentPush(const Privacy());
  //         },
  //         child: Text(
  //           AppLocalizations.of(context)!.privacy,
  //           style: const TextStyle(
  //             color: Color(0xFF4589ff),
  //             fontSize: 11,
  //           ),
  //         ),
  //       ),
  //       const Text(
  //         "|",
  //         style: TextStyle(
  //           color: Colors.black26,
  //           fontSize: 14,
  //         ),
  //       ),
  //       TextButton(
  //         onPressed: () {
  //           Util.navigatorTransparentPush(const UA());
  //         },
  //         child: Text(
  //           AppLocalizations.of(context)!.terms,
  //           style: const TextStyle(
  //             color: Color(0xFF4589ff),
  //             fontSize: 11,
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final width = MediaQuery.of(context).size.width;
    // final height = MediaQuery.of(context).size.height;
    final contentWidth = width -40;
    final paddingTop = MediaQuery.of(context).padding.top; // 获取状态栏高度
    
    return Material(
      color: Colors.transparent,
      child: Stack(
        fit: StackFit.expand,
        clipBehavior: Clip.none,
        children: [
          // 使用Positioned.fill确保背景图完全铺满整个屏幕，包括状态栏
          Positioned.fill(
            child: Image.asset(
              "images/pay/bg03.png",
              fit: BoxFit.cover,
            ),
          ),
          // 内容区域
          SafeArea(
            top: false,
            child: SingleChildScrollView(
              child: Container(
                width: width,
                // constraints: BoxConstraints(
                  // minHeight: height,
                // ),
                child: Column(
                  children: [
                    // 顶部Logo和标题
                    Container(
                      width: width,
                      padding: EdgeInsets.only(top: 60 + paddingTop, bottom: 0),
                      child: Column(
                        children: [
                          // Logo
                          Image.asset(
                            "images/logo.png",
                            width: 80,
                            height: 80,
                          ),
                          // const SizedBox(height: 8),
                          // PRO 标签
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                            // decoration: BoxDecoration(
                            //   // color: const Color(0xFF1B1C1E),
                            //   borderRadius: BorderRadius.circular(16),
                            // ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "Imtrans",
                                  style: const TextStyle(
                                    color: const Color(0xff1b1c1e),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w800,
                                    letterSpacing: 0.05,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                ImageWidget.proIcon(),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    // const SizedBox(height: 16),
                    // 功能列表
                    Container(
                      width: contentWidth,
                      padding: const EdgeInsets.only(left: 16, top: 20,bottom: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildFeatureItem(l10n.fileUploadFeature),
                          _buildFeatureItem(l10n.higherResolutionFeature),
                          _buildFeatureItem(l10n.accurateTranslationFeature),
                          _buildFeatureItem(l10n.unlimitedTranslationsFeature),
                          _buildFeatureItem(l10n.adFreeFeature),
                        ],
                      ),
                    ),
                    
                    // 订阅选项
                    Container(
                      width: contentWidth,
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: Row( 
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 使用循环创建订阅选项，并设置宽度为contentWidth的一半减去间距
                          ...widget.products.asMap().entries.map((entry) {
                            int idx = entry.key;
                            return SizedBox(
                              // height: 120,
                              width: (contentWidth / 3) - 6,  // 每个选项宽度为总宽度的一半减去间距
                              child: _buildSubscriptionOption(idx, (contentWidth / 3) - 10),
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    
                    // 免费试用按钮
                    Container(
                      width: contentWidth,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: _buildFreeTrialButton(contentWidth),
                    ),
                    const SizedBox(height: 6),
                    
                    // 自动续订提示
                    Container(
                      width: contentWidth,
                      padding: const EdgeInsets.only(bottom: 20),
                      child: Text(
                        descText,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xFF939393),
                          fontSize: 12,
                          fontWeight: FontWeight.w500
                        ),
                      ),
                    ),
                    
                    // // 底部链接
                    // Container(
                    //   width: contentWidth,
                    //   // padding: const EdgeInsets.only(bottom: 30),
                    //   child: Vip._buildLegalLinks(),
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
