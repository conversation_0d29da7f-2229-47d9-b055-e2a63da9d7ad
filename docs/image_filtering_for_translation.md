# 图片过滤功能文档

## 概述

为了提高翻译功能的效果和性能，我们实现了智能图片过滤系统，专门针对漫画和网页内容，过滤掉小图标和装饰性图片，只处理可能包含文本内容的主要图片。

## 问题背景

在之前的实现中，翻译功能会处理页面上的所有图片，包括：
- 小图标和装饰性图片
- 广告图片
- 导航按钮
- 社交媒体图标
- 其他不包含文本的图片

这导致了以下问题：
1. **性能浪费**: 处理大量无用图片消耗资源
2. **OCR 效果差**: 小图标很难识别出有意义的文本
3. **用户体验差**: 处理时间长但结果不理想

## 解决方案

### 多层过滤策略

我们实现了一个多层过滤系统，从多个维度判断图片是否值得处理：

#### 1. URL 特征过滤
基于图片 URL 的特征进行初步过滤：

```dart
bool _isLikelyIconOrSmallImage(String imageUrl) {
  // 检查是否包含图标关键词
  final iconKeywords = [
    'icon', 'logo', 'favicon', 'avatar', 'thumb', 'thumbnail',
    'button', 'arrow', 'star', 'heart', 'like', 'share',
    'comment', 'menu', 'nav', 'header', 'footer', 'banner',
    'ad', 'ads', 'advertisement', 'sponsor', 'widget',
    'ui', 'interface', 'control', 'social', 'badge'
  ];
}
```

#### 2. 内容图片识别
识别可能包含文本内容的主要图片：

```dart
bool _isLikelyMainContentImage(String imageUrl) {
  // 漫画/内容相关关键词
  final contentKeywords = [
    'manga', 'comic', 'chapter', 'page', 'content', 'main',
    'story', 'episode', 'part', 'vol', 'volume', 'data',
    'webtoon', 'manhwa', 'manhua'
  ];
}
```

#### 3. 尺寸验证
通过 JavaScript 获取图片实际尺寸进行验证：

```dart
Future<Size?> _getImageSize(String imageUrl, dynamic extractor) async {
  // 通过 JavaScript 获取图片的 naturalWidth 和 naturalHeight
  final result = await controller.evaluateJavascript(source: '''
    var imgs = document.querySelectorAll('img');
    for (var i = 0; i < imgs.length; i++) {
      if (imgs[i].src === '$imageUrl') {
        return {
          width: imgs[i].naturalWidth || imgs[i].width,
          height: imgs[i].naturalHeight || imgs[i].height
        };
      }
    }
    return null;
  ''');
}
```

### 过滤规则

#### 尺寸过滤
- **最小尺寸**: 宽度或高度小于 80px 的图片被过滤
- **长宽比**: 长宽比大于 8:1 或小于 1:8 的图片被过滤（可能是装饰性条纹）

#### URL 模式过滤
- **文件名尺寸**: 如 `icon_24x24.png` 这样包含小尺寸的文件名
- **小图片模式**: 包含 `small`, `mini`, `tiny` 等关键词
- **数字模式**: 文件名中只有1-2位数字的图片（如 `image_1.jpg`）

#### 内容优先级
- **长数字串**: 包含10位以上数字的 URL（通常是内容图片的时间戳）
- **内容关键词**: 包含漫画、内容相关关键词的 URL
- **数据目录**: 包含 `data` 目录的 URL（如您提供的示例）

## 实现细节

### 过滤流程

```dart
Future<List<String>> _filterMainContentImages(List<String> imageUrls, dynamic extractor) async {
  final filteredUrls = <String>[];
  
  for (final imageUrl in imageUrls) {
    // 1. 基于 URL 过滤图标
    if (_isLikelyIconOrSmallImage(imageUrl)) {
      continue;
    }
    
    // 2. 检查是否为主要内容
    if (_isLikelyMainContentImage(imageUrl)) {
      // 3. 尺寸验证
      final imageSize = await _getImageSize(imageUrl, extractor);
      if (imageSize != null) {
        // 应用尺寸和长宽比过滤
        if (imageSize.width >= 80 && imageSize.height >= 80) {
          final aspectRatio = imageSize.width / imageSize.height;
          if (aspectRatio <= 8 && aspectRatio >= 0.125) {
            filteredUrls.add(imageUrl);
          }
        }
      } else {
        // 无法获取尺寸时，基于 URL 判断
        filteredUrls.add(imageUrl);
      }
    }
  }
  
  return filteredUrls;
}
```

### 排序优化

过滤后的图片按 URL 中的数字进行排序，确保按正确顺序处理：

```dart
filteredUrls.sort((a, b) {
  final aNum = _extractNumberFromUrl(a);
  final bNum = _extractNumberFromUrl(b);
  return aNum.compareTo(bNum);
});
```

## 针对漫画网站的优化

### URL 模式识别

基于您提供的示例 URL：
```
https://aws-cloud-no4.site/data/135005c/17526475234851_0805.jpeg
```

我们的过滤器能够识别：
1. **数据目录**: 包含 `/data/` 路径
2. **长时间戳**: `17526475234851` 这样的长数字串
3. **序号**: `_0805` 这样的页面序号
4. **图片格式**: `.jpeg` 等标准图片格式

### 特殊处理

- **韩文网站**: 支持 `webtoon`, `manhwa` 等韩文漫画关键词
- **中文网站**: 支持 `manhua` 等中文漫画关键词
- **日文网站**: 支持 `manga` 等日文漫画关键词

## 性能优化

### 处理限制
- **图片数量**: 过滤后最多处理 5 张图片
- **并发控制**: 按顺序处理，避免并发过载
- **超时处理**: JavaScript 执行有超时保护

### 缓存策略
- **尺寸信息**: 可以考虑缓存图片尺寸信息（未实现）
- **过滤结果**: 可以考虑缓存过滤结果（未实现）

## 调试信息

过滤过程会输出详细的调试信息：

```
flutter: Starting image filtering for 49 images
flutter: Skipping likely icon/small image: https://example.com/icon.png
flutter: URL indicates main content image: https://aws-cloud-no4.site/data/135005c/17526475234851_0805.jpeg
flutter: Added verified content image (800x1200): https://aws-cloud-no4.site/data/135005c/17526475234851_0805.jpeg
flutter: Filtered 49 images to 5 main content images
```

## 测试建议

### 功能测试
- [ ] 测试各种类型的网站（漫画、新闻、博客等）
- [ ] 验证图标和装饰图片被正确过滤
- [ ] 确认主要内容图片被保留
- [ ] 测试边界情况（全是图标、全是内容图片等）

### 性能测试
- [ ] 测试大量图片页面的处理时间
- [ ] 验证内存使用情况
- [ ] 测试 JavaScript 执行性能

### 兼容性测试
- [ ] 测试不同的漫画网站
- [ ] 验证不同语言的网站
- [ ] 测试各种图片格式和命名规则

## 未来改进

### 机器学习优化
- 使用图片内容分析判断是否包含文本
- 训练模型识别漫画页面特征
- 自动学习网站特定的图片模式

### 用户自定义
- 允许用户自定义过滤规则
- 提供白名单/黑名单功能
- 支持网站特定的过滤配置

### 智能缓存
- 缓存图片尺寸信息
- 缓存过滤结果
- 预测性加载和过滤

## 维护指南

### 关键方法
- `_filterMainContentImages()`: 主过滤逻辑
- `_isLikelyIconOrSmallImage()`: 图标识别
- `_isLikelyMainContentImage()`: 内容图片识别
- `_getImageSize()`: 尺寸获取

### 配置调整
- 修改 `iconKeywords` 数组添加新的图标关键词
- 修改 `contentKeywords` 数组添加新的内容关键词
- 调整尺寸阈值（当前为 80px）
- 调整长宽比阈值（当前为 8:1）

### 调试技巧
1. 启用详细日志查看过滤过程
2. 检查特定网站的图片 URL 模式
3. 验证 JavaScript 尺寸获取是否正常工作
4. 监控过滤前后的图片数量变化
