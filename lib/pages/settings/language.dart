import 'package:flutter/material.dart';
import 'package:imtrans/util/localizations_extension.dart';
import 'package:imtrans/widgets/wheel_picker.dart';
import 'package:provider/provider.dart';
import '../../util/app_locale.dart';
import '../../util/theme_manager.dart';
import '../../util/language_manager.dart';
import '../../models/product_model.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

class AppLanguagePage extends StatefulWidget {
  const AppLanguagePage({super.key});

  @override
  State<AppLanguagePage> createState() => _AppLanguagePageState();
}

class _AppLanguagePageState extends State<AppLanguagePage> {
  late Locale _selectedLocale;
  late bool _followSystem;
  late String _selectedLanguage;
  final Map<String, String> _languages = Product.targetLanguages;
  late Map<String, String> _localeNames;
  
  @override
  void initState() {
    super.initState();
    _selectedLocale = AppLocale.currentLocale;
    _followSystem = AppLocale.followSystem;
    _selectedLanguage = LanguageManager.currentLanguage;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final l10n = AppLocalizations.of(context)!;
    _localeNames = {
      'system': l10n.followSystem,
      'en': 'English',
      'zh': '简体中文',
      'zh_Hant': '繁體中文',
      'ja': '日本語',
      'ko': '한국어', 
      'fr': 'Français',
      'de': 'Deutsch',
      'es': 'Español',
      'it': 'Italiano',
      'pt': 'Português',
      'th': 'ไทย',
      'vi': 'Tiếng Việt',
      'id': 'Bahasa Indonesia',
      'ms': 'Bahasa Melayu',
    };
  }
  
  String _getLocaleKey(Locale locale) {
    if (locale.scriptCode != null) {
      return '${locale.languageCode}_${locale.scriptCode}';
    }
    return locale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Theme(
      data: Theme.of(context).copyWith(
        dropdownMenuTheme: DropdownMenuThemeData(
          inputDecorationTheme: InputDecorationTheme(
            border: InputBorder.none,
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ),
      child: Scaffold(
        backgroundColor: ThemeManager.currentTheme.backgroundColor,
        appBar: AppBar(
          backgroundColor: ThemeManager.currentTheme.backgroundColor,
          automaticallyImplyLeading: false,
          centerTitle: true,
          toolbarHeight: 60,
          titleSpacing: 0,
          title: Container(
            height: 60,
            width: double.infinity,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  l10n.language,
                  style: TextStyle(
                    color: ThemeManager.currentTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Positioned(
                  left: 10,
                  top: 0,
                  bottom: 0,
                  child: IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: Image.asset(
                      ThemeManager.getImagePath('btn_back'),
                      width: 40,
                      height: 40,
                    ),
                  ),
                ),
              ]),
          ),
        ),

        body: ChangeNotifierProvider.value(
          value: AppLocale(),
          child: Consumer<AppLocale>(
            builder: (context, appLocale, child) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 应用语言设置
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                      child: Container(
                        child: Column(
                          children: [
                            // 应用语言选择下拉框
                            Container(
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: ThemeManager.currentTheme.borderAreaBgColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      l10n.language,
                                      style: TextStyle(
                                        color: ThemeManager.currentTheme.textColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  // 将DropdownButton替换为InkWell和WheelPicker
                                  InkWell(
                                    onTap: () {
                                      // 临时存储选择的语言，以便在确认时应用
                                      String tempSelectedLocale = _followSystem ? 'system' : _getLocaleKey(_selectedLocale);
                                      showModalBottomSheet(
                                        context: context,
                                        backgroundColor: ThemeManager.currentTheme.backgroundColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                                        ),
                                        // 使用通用滚轮选择器组件
                                        builder: (context) => WheelPicker(
                                          selectedValue: tempSelectedLocale,
                                          options: _localeNames,
                                          onValueChanged: (newLocale) {
                                            tempSelectedLocale = newLocale;
                                          },
                                          onCancel: () { Navigator.pop(context); },
                                          onConfirm: () async {
                                            Navigator.pop(context);
                                            if (tempSelectedLocale == 'system') {
                                              setState(() {
                                                _followSystem = true;
                                              });
                                              AppLocale.setFollowSystem(true);
                                            } else {
                                              final selectedLocale = AppLocalizations.supportedLocales
                                                  .firstWhere((locale) => _getLocaleKey(locale) == tempSelectedLocale);
                                              setState(() {
                                                _followSystem = false;
                                                _selectedLocale = selectedLocale;
                                              });
                                              AppLocale.setFollowSystem(false);
                                              AppLocale.setLocale(selectedLocale);
                                            }
                                          },
                                        ),
                                      );
                                    },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min, // Add this to prevent expansion
                                      children: [
                                        Text(
                                          _followSystem ? _localeNames['system']! : (_localeNames[_getLocaleKey(_selectedLocale)] ?? _getLocaleKey(_selectedLocale)),
                                          style: TextStyle(
                                            color: ThemeManager.currentTheme.textColor,
                                            fontSize: 14,
                                          ),
                                        ),
                                        Icon(
                                          Icons.arrow_drop_down,
                                          color: ThemeManager.currentTheme.textColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    SizedBox(height: 20),
                    
                    // 翻译目标语言设置
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 说明文本
                          Padding(
                            padding: const EdgeInsets.only(left: 10, right: 16, bottom: 10),
                            child: Text(
                              AppLocalizations.of(context)!.targetLanguageDescription,
                              style: TextStyle(
                                color: const Color(0xFF797979),
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          // 目标语言选择下拉框
                          Container(
                            height: 48,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            decoration: BoxDecoration(
                              color: ThemeManager.currentTheme.borderAreaBgColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: InkWell(
                              onTap: () {
                                // 临时存储选择的语言，以便在确认时应用
                                String tempSelectedLang = _selectedLanguage;
                                showModalBottomSheet(
                                  context: context,
                                  backgroundColor: ThemeManager.currentTheme.backgroundColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
                                  ),
                                  // 使用通用滚轮选择器组件
                                  builder: (context) => WheelPicker(
                                    selectedValue: tempSelectedLang,
                                    options: Map.fromEntries(
                                      _languages.keys.map(
                                        (key) {
                                          final languageName = AppLocalizations.of(context)!.getLanguageName(key);
                                          return MapEntry(
                                            key, 
                                            languageName
                                          );
                                        },
                                      ),
                                    ),
                                    onValueChanged: (newLang) {
                                      tempSelectedLang = newLang;
                                    },
                                    onCancel: () { Navigator.pop(context); },
                                    onConfirm: () async {
                                      Navigator.pop(context);
                                      setState(() {
                                        _selectedLanguage = tempSelectedLang;
                                      });
                                      await LanguageManager.setLanguage(tempSelectedLang);
                                    },
                                  ),
                                );
                              },
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      l10n.selectTargetLanguage,
                                      style: TextStyle(
                                        color: ThemeManager.currentTheme.textColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    AppLocalizations.of(context)!.getLanguageName(_selectedLanguage),
                                    style: TextStyle(
                                      color: ThemeManager.currentTheme.textColor,
                                      fontSize: 14,
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_drop_down,
                                    color: ThemeManager.currentTheme.textColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(height: 16),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}