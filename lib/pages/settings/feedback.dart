import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart';
import '../../util/util.dart';
import '../../services/feedback.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

// 我的 - Contact Us 页面
class FeedbackPage extends StatefulWidget {
  const FeedbackPage({super.key});

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
  final TextEditingController _controllerContent = TextEditingController();
  final TextEditingController _controllerEmail = TextEditingController();
  final FocusNode focusNode1 = FocusNode();
  final FocusNode focusNode2 = FocusNode();
  // final Global _global = Global();

  @override
  void dispose() {
    focusNode1.unfocus();
    focusNode2.unfocus();
    focusNode1.dispose();
    focusNode2.dispose();
    super.dispose();
  }

  AppBar _buildAppBar(Widget widget, String title, BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      automaticallyImplyLeading: false, // 禁用默认的返回按钮
      title: Row(
        children: [
          IconButton(
            onPressed: () {
              Util.closeView(widget);
            },
            icon: Image.asset(
              ThemeManager.getImagePath('btn_back'),
              width: 40,
              height: 40,
            ),
          ),
          const SizedBox(width: 10), // 按钮和标题之间的间距
          Text(
            title,
            style: TextStyle(
              color: ThemeManager.currentTheme.textColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              // fontFamily: Config.fontFamily,
            ),
          ),
        ],
      ),
      // toolbarHeight: _global.barHeight,
    );
  }

  Future<String> getVersionInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      String version = "V${packageInfo.version}";
      int buildNumber = int.tryParse(packageInfo.buildNumber) ?? 0;

      return "$version ($buildNumber)";
    } catch (e) {
      debugPrint("获取版本信息失败: $e");
      return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.mineColor,
      appBar: _buildAppBar(widget, AppLocalizations.of(context)!.contactUs, context),
      body: GestureDetector(
        onTap: () {
          focusNode1.unfocus();
          focusNode2.unfocus();
        },
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 30),
              Container(
                width: 90,
                height: 90,
                child: const Image(
                    image: AssetImage("images/logo.png"),
                    fit: BoxFit.contain,
                  ),
              ),
              // const SizedBox(height: 10),
              Container(
                alignment: Alignment.center,
                child: Text(
                  "Imtrans",
                  style: TextStyle(
                    fontSize: 18,
                    color: ThemeManager.currentTheme.textColor,
                    fontWeight: FontWeight.w700,
                    fontFamily: "Poppins-medium",
                  ),
                ),
              ),
              // 使用FutureBuilder替代直接await
              FutureBuilder<String>(
                future: getVersionInfo(),
                builder: (context, snapshot) {
                  return Container(
                    alignment: Alignment.center,
                    child: Text(
                      snapshot.hasData ? snapshot.data! : "",
                      style: TextStyle(
                        fontSize: 12,
                        color: ThemeManager.currentTheme.textColor,
                        fontFamily: "inter",
                      ),
                    ),
                  );
                },
              ),
              Container(
                width: width * 0.9,
                alignment: Alignment.center,
                child: Text(
                  AppLocalizations.of(context)!.appDescription,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeManager.currentTheme.textColor,
                  ),
                ),
              ),
              const SizedBox(height: 40),
              Container(
                width: width * 0.9,
                padding: const EdgeInsets.only(left: 10, right: 10),
                decoration: BoxDecoration(
                  color: ThemeManager.currentTheme.inputBoxBgColor,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: ThemeManager.currentTheme.mineBorderColor,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _controllerContent,
                  focusNode: focusNode2,
                  cursorColor: ThemeManager.currentTheme.textColor,
                  maxLines: 7,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: AppLocalizations.of(context)!.feedbackHint,
                    hintStyle: TextStyle(
                      color: ThemeManager.currentTheme.hintTextColor,
                      fontSize: 14,
                    ),
                  ),
                  style: TextStyle(color: ThemeManager.currentTheme.textColor),
                ),
              ),
              const SizedBox(height: 20),
              Container(
                width: width * 0.9,
                padding: const EdgeInsets.only(left: 10, right: 10),
                decoration: BoxDecoration(
                  color: ThemeManager.currentTheme.inputBoxBgColor,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: ThemeManager.currentTheme.mineBorderColor,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: _controllerEmail,
                  focusNode: focusNode1,
                  cursorColor: ThemeManager.currentTheme.textColor,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: AppLocalizations.of(context)!.emailHint,
                    hintStyle: TextStyle(
                      color: ThemeManager.currentTheme.hintTextColor,
                      fontSize: 14,
                    ),
                  ),
                  style: TextStyle(color: ThemeManager.currentTheme.textColor),
                ),
              ),
              const SizedBox(height: 30),
              GestureDetector(
                onTap: () async {
                  final fbService = FeedbackService(); 
                  await fbService.sendFeedback(
                    _controllerContent.text,
                    context: context,
                    email: _controllerEmail.text,
                    onSuccess: () {
                      setState(() {
                        _controllerContent.text = "";
                        _controllerEmail.text = "";
                      });
                    },
                  );
                },
                child: Container(
                  width: width * 0.9,
                  height: 50,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: ThemeManager.currentTheme.logoColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    AppLocalizations.of(context)!.send,
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
