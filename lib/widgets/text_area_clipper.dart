import 'dart:math';
import 'package:flutter/material.dart';

class TextAreaClipper extends CustomClipper<Path> {
  final List<dynamic> points;
  final double scale;
  final double padding = 2.0; // 添加边界padding

  TextAreaClipper(this.points, this.scale);

  @override
  Path getClip(Size size) {
    final path = Path();
    
    // 获取边界
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;
    
    // 计算边界框
    for (var point in points) {
      double x = point["x"] * scale;
      double y = point["y"] * scale;
      minX = min(minX, x);
      minY = min(minY, y);
      maxX = max(maxX, x);
      maxY = max(maxY, y);
    }
    
    // 扩大边界
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;
    
    // 创建扩大后的路径
    path.addRect(Rect.fromLTRB(minX, minY, maxX, maxY));
    
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
} 