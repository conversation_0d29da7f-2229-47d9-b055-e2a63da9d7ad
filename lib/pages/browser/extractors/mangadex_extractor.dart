import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:http/http.dart' as http;
import 'base_extractor.dart';

/// MangaDex网站图片提取器
/// 专门处理MangaDex网站的图片提取和下载
class MangaDexExtractor extends BaseImageExtractor {
  MangaDexExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) : super(
          controller: controller,
          currentUrl: currentUrl,
        );

  @override
  Future<List<String>> extractImageUrls() async {
    try {
      // 提取 chapterId（URL 最后的 UUID）
      final uri = Uri.parse(currentUrl);
      final segments = uri.pathSegments;
      final chapterId = segments.isNotEmpty ? segments.last : null;

      if (chapterId == null || chapterId.isEmpty) {
        debugPrint("MangaDex: 无法从 URL 中提取 chapterId");
        return [];
      }
      debugPrint("https://api.mangadex.org/at-home/server/$chapterId");
      // 请求 MangaDex API 获取图片信息
      final response = await http.get(
        Uri.parse("https://api.mangadex.org/at-home/server/$chapterId"),
      );

      if (response.statusCode != 200) {
        debugPrint("MangaDex API 请求失败，状态码: ${response.statusCode}");
        return [];
      }

      // 解析返回的 JSON 数据
      final Map<String, dynamic> data = json.decode(response.body);
      final baseUrl = data['baseUrl'];
      final hash = data['chapter']['hash'];
      final pageData = List<String>.from(data['chapter']['data']);

      // 构建图片的完整 URL
      final List<String> imageUrls = pageData.map((filename) {
        return '$baseUrl/data/$hash/$filename';
      }).toList();

      debugPrint("从MangaDex API提取到 ${imageUrls.length} 个图片URL");
      for (int i = 0; i < imageUrls.length; i++) {
        debugPrint("MangaDex图片[$i]: ${imageUrls[i]}");
      }

      return imageUrls;
    } catch (e) {
      debugPrint("MangaDex图片提取失败: $e");
      return [];
    }
  }
}
