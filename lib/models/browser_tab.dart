import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class BrowserTab {
  final String id;
  String title;
  String url;
  bool isLoading;
  InAppWebViewController? controller;
  final UniqueKey webViewKey;
  String? favicon;
  bool _isDisposed = false; // 添加dispose状态跟踪

  BrowserTab({
    required this.id,
    this.title = 'New Tab',
    this.url = '',
    this.isLoading = false,
    this.controller,
    this.favicon,
    UniqueKey? webViewKey,
  }) : webViewKey = webViewKey ?? UniqueKey();
  
  // 检查是否已dispose
  bool get isDisposed => _isDisposed;
  
  // 标记为已dispose
  void markAsDisposed() {
    _isDisposed = true;
  }
  
  // 复制tab并更新特定字段
  BrowserTab copyWith({
    String? title,
    String? url,
    bool? isLoading,
    InAppWebViewController? controller,
    String? favicon,
    UniqueKey? webViewKey,
  }) {
    final newTab = BrowserTab(
      id: id,
      title: title ?? this.title,
      url: url ?? this.url,
      isLoading: isLoading ?? this.isLoading,
      controller: controller ?? this.controller,
      favicon: favicon ?? this.favicon,
      webViewKey: webViewKey ?? this.webViewKey,
    );
    // 保持dispose状态
    if (_isDisposed) {
      newTab._isDisposed = true;
    }
    return newTab;
  }

  // 检查是否为空tab
  bool get isEmpty {
    return url.isEmpty ||
           url == 'about:blank' ||
           url.startsWith('data:text/html') ||
           url == 'chrome://newtab/' ||
           url == 'edge://newtab/';
  }

  // 检查是否有实际内容
  bool get hasContent {
    return !isEmpty && url.isNotEmpty;
  }
  
  // 获取显示标题（限制长度）
  String get displayTitle {
    if (title.isEmpty || title == 'New Tab') {
      if (url.isNotEmpty) {
        try {
          final uri = Uri.parse(url);
          return uri.host.isNotEmpty ? uri.host : 'New Tab';
        } catch (e) {
          return 'New Tab';
        }
      }
      return 'New Tab';
    }
    return title.length > 20 ? '${title.substring(0, 20)}...' : title;
  }
} 