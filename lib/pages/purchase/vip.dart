import 'package:flutter/material.dart';
import 'package:imtrans/pages/purchase/purchase_manager.dart';
import 'package:imtrans/pages/settings/privacy.dart';
import 'package:imtrans/pages/settings/ua.dart';
import 'package:imtrans/util/dialog_manager.dart';
import 'package:imtrans/util/loading_manager.dart';
import '../../services/account.dart';
import '../../services/event_log.dart';
import '../../util/util.dart';
import '../../services/iap_purchases.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

class Vip extends StatefulWidget {
  const Vip({super.key});
  
  @override
  State<Vip> createState() => _VipState();
}

class _VipState extends State<Vip> {
  var _products=[];
  // var _index=0;
  bool _showClose=false;
  bool _isLoading = true; // 添加加载状态标志
  String? _userUuid;

  @override
  void initState() {
    super.initState();
    
    //延迟显示关闭按钮
    Future.delayed(const Duration(seconds: 2),(){
      if(mounted){
        setState(() {
          _showClose=true;
        });
      }
    });
    // 设置使用第5版购买视图
    PurchaseManager.payFlag = 5;
    
    // 预加载数据，避免UI渲染时的卡顿
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }
  
  // 统一初始化数据，减少状态更新次数
  Future<void> _initializeData() async {
    try {
      // 先加载用户信息，然后并行加载产品和记录日志
      await _loadUserInfo();
      
      // 并行加载产品和上报进入购买页面
      await Future.wait([
        _loadProducts(),
        if (_userUuid != null) EventLogService.logEnterPurchase(userId: _userUuid!)
      ]);
    } catch (e) {
      debugPrint('Error initializing VIP data: $e');
    }
  }

  Future<void> _loadProducts() async {
    await IAPPurchases().clearTransactions();
    final products = await IAPPurchases().fetchProducts();
    if (products.isEmpty) {
      if (mounted) {
        setState(() {
          _isLoading = false; // 即使加载失败也更新加载状态
        });
      }
      DialogManager.instance.showDialog(
        title: AppLocalizations.of(context)!.error,
        message: AppLocalizations.of(context)!.loadFailed,
        onConfirm: () {
          if (mounted) {
            setState(() {
              _isLoading = true; // 重新加载时恢复加载状态
            });
            _loadProducts();
          }
        },
        onCancel: () {
          Util.closeView(widget);
        },
        cancelText: AppLocalizations.of(context)!.back,
        confirmText: AppLocalizations.of(context)!.retry,
      );
    } else if (mounted) {
      setState(() {
        _products = products;
        _isLoading = false; // 加载完成后更新状态
      });
    }
  }

  Future<void> _loadUserInfo() async {
    try {
      Account account = await Account.instance;
      _userUuid = account.info["uuid"];
    } catch (e) {
      debugPrint('Error loading user info: $e');
    } finally {
      if (mounted) {
        setState(() {
          // 即使用户信息加载失败，也允许页面显示
          // 产品加载完成后会更新_isLoading状态
        });
      }
    }
  }

  @override
  dispose() {
    Util.popNull(widget);
    super.dispose();
  }

  Future<void> _showMsgDialog(BuildContext context, String message) {
    return DialogManager.instance.showDialog(
      title: "",
      message: message,
      onConfirm: () {
        DialogManager.instance.closeDialog(null, name: "DialogView");
      },
      confirmText: AppLocalizations.of(context)!.confirm,
    );
  }

  Widget _buildRestoreButton(BuildContext context) {
    return TextButton(
      onPressed: () async {
        if (_userUuid == null) {
          // 处理未登录情况
          return;
        }
        LoadingManager.instance.show(context);
        
        await IAPPurchases().restorePurchases(
          uuid: _userUuid!,
          onSuccess: () async{
            LoadingManager.instance.hide(context);
            DialogManager.instance.showConfirmDialog(
              message: AppLocalizations.of(context)!.purchaseRestored,
            );
            Account account = await Account.instance;
            account.reloadInfo();
            Util.closeView(widget);
          },
          onError: (error) {
            LoadingManager.instance.hide(context);
            DialogManager.instance.showConfirmDialog(
              message: AppLocalizations.of(context)!.restoreFailed(error),
            );
            // _showMsgDialog(context, AppLocalizations.of(context)!.restoreFailed(error));
          },
        );
      },
      child: Text(AppLocalizations.of(context)!.restore, 
        style: const TextStyle(fontSize:14, color: Colors.black54)),
    );
  }

  Widget _buildLegalLinks(String text_pp, String text_ua) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: () {
            Util.navigatorTransparentPush(const Privacy());
          },
          child: Text(
            text_pp,
            style: const TextStyle(
              color: Color(0xFF4589ff),
              fontSize: 12,
            ),
          ),
        ),
        const Text(
          "|",
          style: TextStyle(
            color: Colors.black26,
            fontSize: 12,
          ),
        ),
        TextButton(
          onPressed: () {
            Util.navigatorTransparentPush(const UA());
          },
          child: Text(
            text_ua,
            style: const TextStyle(
              color: Color(0xFF4589ff),
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;
    
    // 根据payFlag获取对应的背景颜色
    Color backgroundColor = PurchaseManager.getBackgroundColor(PurchaseManager.payFlag);
    
    return Scaffold(
      backgroundColor: backgroundColor,
      body: _isLoading || _products.isEmpty ? Center(
          child: CircularProgressIndicator(
            color: Colors.black45,
          ),
        ) : Stack(
          children: [
            Positioned(
              top: 0,
              height: height,
              width: width,
              child: PurchaseManager.getPurchaseView(
                payFlag: PurchaseManager.payFlag,
                products: _products,
                userUuid: _userUuid,
                onBuyFailed: (msg){
                  setState(() {
                    // _isLoading = false;
                  });
                  _showMsgDialog(context, msg!);
                },
                onBuySucceed: () async {
                  setState(() {
                    // _isLoading = false;
                  });
                  LoadingManager.instance.show(context);
                  Account account = await Account.instance;
                  await account.reloadInfo();
                  LoadingManager.instance.hide(context);

                  DialogManager.instance.showConfirmDialog(
                    message: l10n.purchaseSuccessful,
                    onConfirm: () async {
                      Util.closeView(widget);
                  });
                },
                context: context,
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).padding.top, // 添加状态栏高度作为顶部偏移
              height: 60,
              width: width,
              child: Container(
                color: Colors.transparent,
                child: Row(
                  children: [
                    Container(
                      width: width/2,
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.only(left: 20),
                      child:_showClose?IconButton(
                        onPressed: (){
                          Util.closeView(widget);
                        },
                        icon: const Icon(Icons.close,size: 30,color: Colors.black26,),
                      ):Container()
                    ),
                    Container(
                      width: width/2,
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(right: 20),
                      child: _buildRestoreButton(context),
                    ),
                  ],
                ),
              )
            ),
          
          // 底部法律链接
          Positioned(
              bottom: MediaQuery.of(context).padding.bottom, 
              height: 60,
              width: width,
              child: Container(
                color: Colors.transparent,
                child: _buildLegalLinks(l10n.privacyPolicy, l10n.termsOfService)
              )
            )
          ],
        ),
      );
  }
}
