import 'package:flutter/material.dart';
import 'autofittext.dart';

class StrokeText extends StatelessWidget {
  final String text;
  final double strokeWidth;
  final Color strokeColor;
  final Color textColor;
  final TextStyle? style;
  final Size size;

  const StrokeText(
    this.text, {
    super.key,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.white,
    this.textColor = Colors.black,
    this.style,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    return AutoFitText(
      text: text,
      size: size,
      style: style,
      builder: (context, style) => Stack(
        children: [
          // 白色描边（放在底层）
          Text(
            text,
            style: style?.copyWith(
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = strokeWidth
                ..color = strokeColor,
              shadows: [], // 描边层不需要阴影
            ),
          ),
          // 黑色文字（放在上层）
          Text(
            text,
            style: style?.copyWith(
              color: textColor,
              // 保持原有的shadows，如果没有则添加默认阴影
              shadows: style.shadows ?? [
                Shadow(
                  offset: const Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Colors.black.withAlpha(128),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
} 