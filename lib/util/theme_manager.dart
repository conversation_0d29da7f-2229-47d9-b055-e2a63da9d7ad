import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/scheduler.dart';

// 添加基础颜色常量类
class BaseColors {
  // 主要颜色
  static const Color primaryDark = Color(0xff1b1c1e);
  static const Color primaryLight = Color(0xfffaf8f5);
  static const Color accentColor = Color(0xffcdee2d);
  
  // 基础颜色
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color transparent = Colors.transparent;
  
  // 半透明颜色
  static const Color blackShadow = Color(0x40000000); // 25% 透明度
  static const Color blackOverlay = Color(0x7F000000); // 50% 透明度
  static const Color blackStrongOverlay = Color(0x33000000); // 80% 透明度
  
  // 功能颜色
  static const Color grayText = Color(0xff707070);
  static const Color lightGray = Color(0xfff0f0f0);
  static const Color closeGray = Color(0xffb2b6c2);
}

class ThemeColors {
  final Color blackOverlayColor;
  final Color textColor;
  final Color borderAreaBgColor;
  final Color inputBoxBgColor;
  final Color drawerBackgroundColor;
  final Color backgroundColor;
  final Color logoColor;
  final Color hintTextColor;
  final Color mineColor;
  final Color selectSourceColor;
  final Color borderColor;
  final Color shadowColor;
  final Color renameBgColor;
  final Color closeColor;
  final Color nameColor;
  final Color lightTextColor;
  final Color mineBorderColor;
  final Color vipLeftColor;
  final Color itemBorderColor;
  final Color butttonbgColor;
  final Color bottomBarShadowBackgroundColor;
  final Color bottomBarBackgroundColor;
  final Color mineButtonBackgroundColor;
  final Color mineButtonItemColor;
  final Color mineButtonBorderItemColor;
  final Color selectedItemColor;
  final Color signOutButtonBgColor;
  final Color slideBarActiveColor;
  final Color slideBarInactiveColor;
  final Color recommendedSitesBackgroundColor;

  const ThemeColors({
    this.blackOverlayColor = BaseColors.blackOverlay,
    this.textColor = BaseColors.primaryDark,
    this.lightTextColor = BaseColors.grayText,
    this.borderAreaBgColor = BaseColors.white,
    this.inputBoxBgColor = BaseColors.white,
    this.drawerBackgroundColor = BaseColors.primaryLight,
    this.backgroundColor = BaseColors.primaryLight,
    this.logoColor = BaseColors.accentColor,
    this.hintTextColor = const Color(0x661b1c1e),
    this.mineColor = BaseColors.primaryLight,
    this.selectSourceColor = BaseColors.primaryDark,
    this.borderColor = BaseColors.primaryDark,
    this.shadowColor = BaseColors.blackShadow,
    this.renameBgColor = BaseColors.lightGray,
    this.closeColor = BaseColors.closeGray,
    this.nameColor = const Color(0xff151624),
    this.mineBorderColor = const Color(0x331b1c1e),
    this.vipLeftColor = const Color(0x882a2d37),
    this.itemBorderColor = BaseColors.blackShadow,
    this.butttonbgColor = BaseColors.transparent,
    this.bottomBarShadowBackgroundColor = const Color(0x39000000),
    this.bottomBarBackgroundColor = BaseColors.white,
    this.mineButtonBackgroundColor = BaseColors.white,
    this.mineButtonItemColor = BaseColors.white,
    this.mineButtonBorderItemColor = const Color(0x0c10093a),
    this.selectedItemColor = BaseColors.accentColor,
    this.signOutButtonBgColor = BaseColors.white,
    this.slideBarActiveColor = const Color(0xFFDBE1EC),
    this.slideBarInactiveColor = const Color(0xFFF6F6F6),
    this.recommendedSitesBackgroundColor = const Color(0xfff0f0f0),
  });

  factory ThemeColors.light() {
    return const ThemeColors();
  }

  factory ThemeColors.dark() {
    return const ThemeColors(
      blackOverlayColor: BaseColors.blackStrongOverlay,
      textColor: BaseColors.white,
      lightTextColor: BaseColors.white,
      borderAreaBgColor: Color(0x33FFFFFF),
      inputBoxBgColor: Color(0x1affffff),
      drawerBackgroundColor: Color(0xff2c2c2c),
      backgroundColor: BaseColors.primaryDark,
      hintTextColor: Color(0xff999999),
      mineColor: BaseColors.primaryDark,
      selectSourceColor: BaseColors.white,
      borderColor: BaseColors.primaryDark,
      nameColor: Color(0xffeae9db),
      mineBorderColor: Color(0x0d10093A),
      vipLeftColor: Color(0x88d5d2c8),
      itemBorderColor: Color(0x19ffffff),
      butttonbgColor: Color(0x30ffffff),
      bottomBarBackgroundColor: BaseColors.primaryDark,
      mineButtonBackgroundColor: Color(0x26ffffff),
      mineButtonItemColor: Color(0x26ffffff),
      signOutButtonBgColor: Color(0xffd9d9d9),
      slideBarActiveColor: const Color(0xFF979797),
      slideBarInactiveColor: const Color(0xFF717171),
      recommendedSitesBackgroundColor: const Color(0x33FFFFFF),
    );
  }
}

class ThemeManager extends ChangeNotifier with WidgetsBindingObserver {
  // 单例模式
  static final ThemeManager _instance = ThemeManager._internal();
  factory ThemeManager() => _instance;
  ThemeManager._internal() {
    // 注册系统主题变化监听器
    WidgetsBinding.instance.addObserver(this);
  }
  
  // 主题相关属性
  static ThemeColors _currentTheme = ThemeColors.light();
  static bool _isWhite = true; // 是否为浅色模式
  static final Map<String, String> _imageResources = {}; // 图片资源路径映射
  static ThemeMode _themeMode = ThemeMode.system; // 主题模式
  static bool _initialized = false; // 是否已初始化

  // Getters
  static ThemeColors get currentTheme => _currentTheme;
  static bool get isWhite => _isWhite;
  static ThemeMode get themeMode => _themeMode;
  static bool get initialized => _initialized;
  static bool get isDarkMode => _themeMode == ThemeMode.dark || 
      (_themeMode == ThemeMode.system && _isSystemDarkMode());
  
  // 获取图片资源路径
  static String getImagePath(String imageName) {
    return _imageResources[imageName] ?? '';
  }
  
  // 获取所有图片资源路径
  static Map<String, String> get imageResources => _imageResources;

  // 设置新的主题颜色
  // 用法：ThemeManager.setTheme(ThemeColors.dark());
  static void setTheme(ThemeColors theme, {bool isDark = false}) {
    _currentTheme = theme;
    _isWhite = !isDark;
    _updateImageResources();
    _instance.notifyListeners();
  }
  
  // 设置主题模式
  static void setThemeMode(ThemeMode mode) {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    _updateTheme();
    _instance.notifyListeners();
  }
  
  // 切换主题模式
  static void toggleThemeMode() {
    if (_themeMode == ThemeMode.light) {
      setThemeMode(ThemeMode.dark);
    } else if (_themeMode == ThemeMode.dark) {
      setThemeMode(ThemeMode.system);
    } else {
      setThemeMode(ThemeMode.light);
    }
  }
  
  // 更新主题
  static void _updateTheme() {
    bool isDark = _themeMode == ThemeMode.dark || 
        (_themeMode == ThemeMode.system && _isSystemDarkMode());
    
    _currentTheme = isDark ? ThemeColors.dark() : ThemeColors.light();
    _isWhite = !isDark;
    _updateImageResources();
    _instance.notifyListeners();
  }
  
  // 初始化系统主题颜色
  static Future<bool> initSystemThemeColor() async {
    if (_initialized) return _isWhite;

    try {
      if (Platform.isIOS) {
        // iOS平台使用原生方法获取系统主题
        MethodChannel methodChannel = const MethodChannel('ios_func');
        _isWhite = await methodChannel.invokeMethod('getSystemThemeColor');
      } else {
        // Android和其他平台使用Flutter的系统亮度检测
        var brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
        _isWhite = brightness == Brightness.light;
      }
    } catch (e) {
      debugPrint("Error getting system theme color: $e");
      // 默认使用系统亮度检测作为fallback
      var brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
      _isWhite = brightness == Brightness.light;
    }
    // debugPrint("_isWhite: $_isWhite");
    _updateImageResources();
    _initialized = true;
    _instance.notifyListeners();
    return _isWhite;
  }
  
  // 初始化主题
  static Future<void> initialize() async {
    if (_initialized) return;
    
    // 获取系统主题模式
    await initSystemThemeColor();
    
    // 更新当前主题颜色
    bool isSystemDarkMode = _isSystemDarkMode();
    if (_themeMode == ThemeMode.system) {
      _currentTheme = isSystemDarkMode ? ThemeColors.dark() : ThemeColors.light();
      _isWhite = !isSystemDarkMode;
      _updateImageResources();
    }
    
    _initialized = true;
    _instance.notifyListeners();
  }
  
  // 判断系统是否为深色模式
  static bool _isSystemDarkMode() {
    var brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
    return brightness == Brightness.dark;
  }
  
  // 系统主题变化监听
  @override
  void didChangePlatformBrightness() {
    // 当系统主题变化时，更新应用主题
    if (_themeMode == ThemeMode.system) {
      bool isSystemDarkMode = _isSystemDarkMode();
      _currentTheme = isSystemDarkMode ? ThemeColors.dark() : ThemeColors.light();
      _isWhite = !isSystemDarkMode;
      _updateImageResources();
      notifyListeners();
    }
    super.didChangePlatformBrightness();
  }
  
  @override
  void dispose() {
    // 移除系统主题变化监听器
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
  
  // 更新图片资源路径
  static void _updateImageResources() {
    final String themeFolder = _isWhite ? "white" : "black";
    
    _imageResources.clear();
    _imageResources.addAll({
      'btn_user': "images/$themeFolder/btn_user.png",
      'btn_draft': "images/$themeFolder/btn_draft.png",
      'btn_close': "images/$themeFolder/btn_close.png",
      'icon_search': "images/$themeFolder/icon_search.png",
      'icon_vip': "images/icons/icon_vip.png",
      'icon_notvip': "images/icons/icon_notvip.png",
      'icon_language': "images/icons/icon_language.png",
      'icon_contact': "images/icons/icon_contact.png",
      'icon_darkmode': "images/icons/icon_darkmode.png",
      'icon_next': "images/$themeFolder/icon_next.png",
      'icon_auth0': "images/$themeFolder/icon_auth0.png",
      'btn_select': "images/$themeFolder/btn_select.png",
      'album': "images/$themeFolder/album.png",
      'btn_back': "images/$themeFolder/back.png",
      'icon_nextpage': "images/$themeFolder/icon_nextpage.png",
      'icon_prevpage': "images/$themeFolder/icon_prevpage.png",
      'icon_prevpage_disable': "images/icons/icon_prevpage_disable.png",
      'icon_nextpage_disable': "images/icons/icon_nextpage_disable.png",
      'icon_download': "images/$themeFolder/icon_download.png",
      'icon_delete': "images/$themeFolder/icon_delete.png",
      'icon_delete_disable': "images/icons/icon_delete_disable.png",
      'icon_download_disable': "images/icons/icon_download_disable.png",
      'icon_edit': "images/$themeFolder/icon_edit.png",
      'icon_edit_disable': "images/icons/icon_edit_disable.png",
      'icon_contents': "images/$themeFolder/icon_contents.png",
      'icon_show_orginal': "images/icons/icon_show_original.png",
      'icon_show_result': "images/icons/icon_show_result.png",
      // 'icon_translate': "images/$themeFolder/icon_translate.png",
      'icon_style': "images/$themeFolder/icon_style.svg",
      'icon_view_list': "images/$themeFolder/icon_view_list.svg",
      'icon_view_sm': "images/$themeFolder/icon_view_sm.svg",
      'icon_view_l': "images/$themeFolder/icon_view_l.svg",
      'icon_home': "images/$themeFolder/icon_home.svg",
      'icon_favorite_on': "images/icons/icon_favorite_on.svg",
      'icon_favorite_off': "images/icons/icon_favorite_off.svg",
      'icon_go_back': "images/$themeFolder/icon_go_back.svg",
      'icon_go_forward': "images/$themeFolder/icon_go_forward.svg",
      'icon_translate': "images/icons/icon_translate.svg",
      'camera': "images/$themeFolder/camera.png",
      'catalog': "images/$themeFolder/catalog.png",
      'delete': "images/$themeFolder/delete.png",
      'down': "images/$themeFolder/down.png",
      'draft': "images/$themeFolder/btn_draft.png",
      'fail': "images/$themeFolder/fail.png",
      'img_close': "images/$themeFolder/img_close.png",
      'img_open': "images/$themeFolder/img_open.png",
      'nodata': "images/$themeFolder/nodata.png",
      'mine': "images/$themeFolder/mine.png",
      'zip': "images/$themeFolder/zip.png",
    });
  }
  
  // 获取当前主题的ThemeData
  static ThemeData getThemeData({bool isDark = false}) {
    ThemeColors theme = isDark ? ThemeColors.dark() : ThemeColors.light();
    return ThemeData(
      brightness: isDark ? Brightness.dark : Brightness.light,
      scaffoldBackgroundColor: theme.backgroundColor,
      fontFamily: 'Poppins-Regular', 
      appBarTheme: AppBarTheme(
        backgroundColor: theme.backgroundColor,
        iconTheme: IconThemeData(
          color: theme.textColor,
        ),
        // titleTextStyle: TextStyle(
        //   color: _currentTheme.labelBlack,
        //   fontFamily: 'Poppins-Medium', // 标题使用Medium字重
        // ),
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: theme.textColor, fontFamily: 'Poppins-Regular'),
        bodyMedium: TextStyle(color: theme.textColor, fontFamily: 'Poppins-Regular'),
        bodySmall: TextStyle(color: theme.textColor, fontFamily: 'Poppins-Regular'),
        titleLarge: TextStyle(color: theme.textColor, fontFamily: 'Poppins-Medium'),
        titleMedium: TextStyle(color: theme.textColor, fontFamily: 'Poppins-Medium'),
        titleSmall: TextStyle(color: theme.textColor, fontFamily: 'Poppins-Medium'),
      ),
      iconTheme: IconThemeData(
        color: theme.textColor,
      ),
      colorScheme: ColorScheme(
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: theme.backgroundColor,
        onPrimary: theme.textColor,
        secondary: theme.logoColor,
        onSecondary: Colors.white,
        error: theme.textColor,
        onError: theme.backgroundColor,
        // background: _currentTheme.backgroundColor,
        // onBackground: _currentTheme.labelBlack,
        surface: theme.backgroundColor,
        onSurface: theme.textColor,
      ),
      useMaterial3: true,
    );
  }

  static ThemeData get lightTheme => getThemeData(isDark: false);
  static ThemeData get darkTheme => getThemeData(isDark: true);
}
