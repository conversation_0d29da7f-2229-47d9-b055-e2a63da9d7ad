/// 漫画站点列表维护文件
import 'package:flutter/material.dart';
import 'server_request.dart';

class MangaSite {
  final String name;
  final String url;
  final String? icon; // 可选的图标字段，存储图标资源的名称或URL

  MangaSite(this.name, this.url, {this.icon});

  /// 获取图标Widget
  Widget getIconWidget({
    double width = 26,
    double height = 26,
    BoxFit fit = BoxFit.cover,
  }) {
    if (icon == null) {
      return Container(
        color: Colors.grey[200],
        child: Icon(
          Icons.public,
          size: width * 0.8,
          color: Colors.grey[400],
        ),
      );
    }

    // 判断是否为网络图片
    if (icon!.startsWith('http')) {
      return Image.network(
        icon!,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) => Icon(
          Icons.public,
          size: width * 0.8,
          color: Colors.grey[400],
        ),
      );
    }

    // 本地资源图片
    return Image.asset(
      icon!,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) => Icon(
        Icons.public,
        size: width * 0.8,
        color: Colors.grey[400],
      ),
    );
  }
}

/// 服务端返回的推荐链接数据模型
class RecommendLink {
  String? createdAt;
  String? icon;
  int? id;
  String? name;
  int? sort;
  bool? status;
  String? updatedAt;
  String? url;

  RecommendLink({
    this.createdAt,
    this.icon,
    this.id,
    this.name,
    this.sort,
    this.status,
    this.updatedAt,
    this.url,
  });

  factory RecommendLink.fromJson(Map<String, dynamic> json) {
    return RecommendLink(
      createdAt: json['created_at'] as String?,
      icon: json['icon'] as String?,
      id: json['id'] as int?,
      name: json['name'] as String?,
      sort: json['sort'] as int?,
      status: json['status'] as bool?,
      updatedAt: json['updated_at'] as String?,
      url: json['url'] as String?,
    );
  }

  /// 转换为MangaSite对象
  MangaSite toMangaSite() {
    return MangaSite(
      name ?? '',
      url ?? '',
      icon: icon,
    );
  }
}

class MangaSitesRepository {
  /// 默认漫画站点列表
  /// 当从服务端获取失败时，将使用此列表作为备选
  static final List<MangaSite> defaultSites = [
    MangaSite("MangaDex", "https://mangadex.org/", icon: "images/icons/logo_mangadex.png"),
    // MangaSite("Webtoon", "https://www.webtoons.com/"),
    MangaSite("Rawkuma", "https://rawkuma.com/", icon: "images/icons/logo_rawkuma.png"),
    // MangaSite("Manga18", "https://manga18.com/"),
    MangaSite("MangaFox", "https://mangafox.me/", icon: "images/icons/logo_mangafox.png"),
    MangaSite("MangaHere", "https://mangahere.cc/", icon: "images/icons/logo_mangahere.png"),
    MangaSite("MangaReader", "https://mangareader.to/", icon: "images/icons/logo_mangareader.png"),
    MangaSite("MangaPill", "https://mangapill.com/", icon: "images/icons/logo_mangapill.png"),
    // MangaSite("MangaKakalot", "https://mangakakalot.com/"),
  ];
  
  /// 缓存从服务端获取的站点列表
  static List<MangaSite>? _cachedSites;

  /// 从服务端获取推荐站点列表
  /// 如果获取失败，则返回默认站点列表
  static Future<List<MangaSite>> fetchSitesFromServer() async {
    try {
      final response = await ServerRequest.getData(ServerRequest.apiRecommendedSites, {});
      
      if (response['code'] == 0 && response.containsKey('recommend_links')) {
        final List<dynamic> recommendLinks = response['recommend_links'];
        final List<MangaSite> sites = recommendLinks
            .map((json) => RecommendLink.fromJson(json).toMangaSite())
            .where((site) => site.url.isNotEmpty && site.name.isNotEmpty)
            .toList();
        
        if (sites.isNotEmpty) {
          _cachedSites = sites;
          return sites;
        }
      }
      
      // 如果获取失败或返回为空，使用默认站点列表
      return defaultSites;
    } catch (e) {
      debugPrint("Error fetching manga sites: ${e.toString()}");
      return defaultSites;
    }
  }

  /// 获取所有站点列表
  /// 优先从服务端获取，如果已有缓存则使用缓存，获取失败则使用默认列表
  static Future<List<MangaSite>> getAllSites() async {
    // 如果已有缓存，直接返回缓存
    if (_cachedSites != null) {
      return _cachedSites!;
    }
    
    // 尝试从服务端获取
    return await fetchSitesFromServer();
  }
  
  /// 获取所有站点列表（同步方法，用于兼容现有代码）
  /// 如果有缓存则返回缓存，否则返回默认列表
  static List<MangaSite> getAllSitesSync() {
    return _cachedSites ?? defaultSites;
  }

  /// 根据名称查找站点
  static MangaSite? findSiteByName(String name) {
    final sites = _cachedSites ?? defaultSites;
    try {
      return sites.firstWhere((site) => site.name == name);
    } catch (e) {
      return null;
    }
  }
}