import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart';
import '../../widgets/webviewbase.dart';

// 用户协议页  User Agreement
class UA extends StatefulWidget {
  const UA({super.key});

  @override
  State<UA> createState() => _UAState();
}

class _UAState extends State<UA> {
  bool isLoading = true; // 加载状态控制

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      appBar: AppBar(
        // backgroundColor: Colors.white,
        automaticallyImplyLeading: false, // 禁用默认的返回按钮
        title: Row(
          children: [
            IconButton(
              onPressed: () {
                Navigator.pop(context); 
              },
              icon: Image.asset(
                  ThemeManager.getImagePath('btn_back'),
                  width: 40,
                  height: 40,
                ),
            ),
            const SizedBox(width: 10), // 按钮和标题之间的间距
            Text(
              "User Agreement", 
              style: TextStyle(fontSize: 20, color: ThemeManager.currentTheme.textColor, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20.0, right: 20.0),
            child: WebViewBase(
              "https://littlegrass.cc/app/imtrans/apple_eula.html",
              onPageStarted: () {
                setState(() {
                  isLoading = true; // 开始加载时显示加载指示器
                });
              },
              onPageFinished: () {
                setState(() {
                  isLoading = false; // 完成加载后隐藏加载指示器
                });
              },
            ),
          ),
          if (isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
