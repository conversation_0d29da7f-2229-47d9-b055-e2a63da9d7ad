import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import '../util/util.dart';

/// 草稿数据模型
/// 用于存储单个草稿的信息，包括产品名称、图片等
class Draft {
  /// 产品ID，可选
  String? id;
  
  /// 产品名称
  String? name;
  
  /// 封面图片文件名
  String? coverFile;
  
  /// 产品图片路径列表
  late List<String> imageFiles;
  
  /// 草稿创建时间
  late DateTime createdAt;
  
  /// 目标语言
  String? targetLang;
  
  /// 是否被选中
  bool selected = false;

  /// 可接受的文件类型
  static const List<String> acceptedFileTypes = ['jpg', 'jpeg', 'png'];
  double uploadProgress = 0.0;  // 上传进度
  String? error;               // 错误信息
  int processingStatus = Draft.STATUS_NONE; // 处理状态

  /// 创建草稿对象
  Draft({
    this.id,
    this.name,
    this.coverFile,
    this.imageFiles = const [],
    this.targetLang,
    this.selected = false,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// 设置选中状态
  void setSelected(bool value) {
    selected = value;
  }

  /// 处理状态
  static const int STATUS_NONE = 0;        // 未处理
  static const int STATUS_PENDING = 1;     // 未处理
  static const int STATUS_PROCESSING = 2;  // 处理中 
  static const int STATUS_UPLOADING = 3;   // 上传中
  static const int STATUS_TRANSLATING = 4; // 翻译中
  static const int STATUS_COMPLETED = 5;   // 完成
  static const int STATUS_FAILED = -1;      // 失败

  /// 将草稿对象转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'coverFile': coverFile,
      'imageFiles': imageFiles,
      'createdAt': createdAt.toIso8601String(),
      'targetLang': targetLang,
    };
  }

  /// 从JSON数据创建草稿对象
  Draft.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    coverFile = json['coverFile'];
    imageFiles = List<String>.from(json['imageFiles'] ?? []);
    createdAt = parseCreatedAt(json['createdAt']);
    targetLang = json['targetLang'];
    selected = false; 
  }

  /// 更新处理状态
  void updateStatus(int status, {double? progress, String? error}) {
    processingStatus = status;
    if (progress != null) uploadProgress = progress;
    if (error != null) this.error = error;
  }

  static DateTime parseCreatedAt(String? dateString) {
    try {
      return DateTime.parse(dateString ?? DateTime.now().toIso8601String());
    } catch (e) {
      return DateTime.now();
    }
  }
  
  bool get isProcessing =>
      processingStatus == STATUS_PENDING ||
      processingStatus == STATUS_PROCESSING ||
      processingStatus == STATUS_UPLOADING ||
      processingStatus == STATUS_TRANSLATING;
      
  // // 获取特定图片的处理状态
  // int getImageStatus(String imagePath) {
  //   return imageProcessingStatus[imagePath] ?? STATUS_PENDING;
  // }
  
  // // 更新特定图片的状态
  // void updateImageStatus(String imagePath, ProcessingStatus status) {
  //   imageProcessingStatus[imagePath] = status;
  // }

  /// 获取状态对应的文字描述
  static String getStatusText(int status) {
    switch (status) {
      case STATUS_PENDING:
        return 'Pending';
      case STATUS_UPLOADING:
        return 'Uploading';
      case STATUS_PROCESSING:
        return 'Processing';
      case STATUS_TRANSLATING:
        return 'Translating';
      case STATUS_COMPLETED:
        return 'Completed';
      default:
        return '';
    }
  }
}

/// 草稿列表模型
/// 负责草稿数据的存储和管理，包括：
/// 1. 草稿数据的持久化
/// 2. 草稿的文件存储路径管理
/// 3. 草稿列表的基础操作
class DraftModel {
  /// 草稿列表
  List<Draft> _items = [];

  /// 草稿文件存储的文件夹名
  static const String _storageFolder = 'drafts';
  
  /// 草稿数据文件的基础名称
  static const String _draftFileName = 'drafts.json';
  
  static const String _defaultSubFolderPrefix = 'draft';
  /// 默认文件后缀
  static const String _defaultSubFolderSuffix = 'default';

  /// 当前使用的子文件夹
  static String _subFolder = _defaultSubFolderPrefix + "_" + _defaultSubFolderSuffix;
  
  /// 草稿文件的根路径
  late String _storagePath;

  /// getters
  String get storagePath => _storagePath;
  List<Draft> get items => _items;
  
  // DraftModel() {
  //   _initializeStoragePath();
  // }

  // Future<void> _initializeStoragePath() async {
  //   final appDir = await getApplicationDocumentsDirectory();
  //   _storagePath = '${appDir.path}/$_storageFolder/$_subFolder';
  //   await Directory(_storagePath).create(recursive: true);
  // }

  /// 初始化并加载数据
  /// [suffix] 可选的文件后缀，用于区分不同用户的文件，建议使用uid
  Future<void> initialize({String? suffix}) async {
    // 检查suffix中是否包含非法字符（只允许字母、数字、下划线和横线）
    String cleanSuffix = _defaultSubFolderSuffix;
    if (suffix != null) {
      if (suffix.contains(RegExp(r'[^a-zA-Z0-9_-]'))) {
        throw FormatException('Suffix can only contain letters, numbers, underscores and hyphens');
      }
      cleanSuffix = suffix;
    }

    // 设置存储文件位置
    _subFolder = _defaultSubFolderPrefix + "_" + cleanSuffix;
    _storagePath = '${Util.appPath}/$_storageFolder/$_subFolder';
    
    await Directory(_storagePath).create(recursive: true);
    // debugPrint('storagePath: $_storagePath');
    await load();
  }

  /// 从存储中加载草稿数据
  Future<void> load() async {
    try {
      final filePath = '$_storagePath/$_draftFileName';
      final file = File(filePath);
      if (await file.exists()) {
        final String content = await file.readAsString();
        fromJson(jsonDecode(content));
      }
    } catch (e) {
      debugPrint('Error loading drafts: $e');
      rethrow;
    }
  }

  /// 保存草稿数据到存储
  Future<void> save() async {
    try {
      final filePath = '$_storagePath/$_draftFileName';
      final tempPath = '$filePath.tmp';
      final file = File(tempPath);
      
      // 先写入临时文件
      final content = jsonEncode(toJson());
      await file.writeAsString(content);
      
      // 确保写入成功后，再替换原文件
      final finalFile = File(filePath);
      if (await finalFile.exists()) {
        await finalFile.delete();
      }
      await file.rename(filePath);
      debugPrint('Drafts saved at: $filePath');
      
    } catch (e) {
      debugPrint('Error saving drafts: $e');
      rethrow;
    }
  }
  
  /// 添加草稿到列表
  void add(Draft draft) => _items.add(draft);
  
  /// 移除指定位置的草稿
  void removeAt(int index) {
    if (_items.length > index) {
      _items.removeAt(index);
    }
  }
  
  /// 清空所有草稿
  void clear() => _items.clear();
  
  /// 获取指定位置的草稿
  Draft? getAt(int index) => index < _items.length ? _items[index] : null;

  /// 将草稿列表转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'items': _items.map((draft) => draft.toJson()).toList(),
    };
  }

  /// 从JSON数据恢复草稿列表
  void fromJson(Map<String, dynamic> json) {
    final List<dynamic> jsonItems = json['items'] ?? [];
    _items.clear();
    for (var item in jsonItems) {
      _items.add(Draft.fromJson(item));
    }
  }

  /// 制作封面图
  /// 将原图压缩到200宽
  /// 返回存储完成的封面图文件名 不包含路径
  Future<String> createCoverImage(String originalImagePath, {String? coverImagePath}) async {
    // 如果没有提供 coverImagePath，使用默认命名
    coverImagePath ??= path.setExtension(originalImagePath, '_cover.jpg');

    // 读取原始图像
    final originalFile = File(originalImagePath);
    final originalBytes = await originalFile.readAsBytes();
    final originalImage = img.decodeImage(originalBytes);

    if (originalImage != null) {
      // 调整图像大小
      // final resizedImage = img.copyResize(originalImage, width: 200);
      final resizedImage = img.copyResize(originalImage,
        width: 200,
        // height: coverHeight,
        interpolation: img.Interpolation.average
      );

      // 将调整后的图像保存为新文件
      final coverFile = File(coverImagePath);
      await coverFile.writeAsBytes(img.encodeJpg(resizedImage, quality: 85));
    } else {
      throw Exception('Failed to decode image');
    }
    return path.basename(coverImagePath);
  }
}
