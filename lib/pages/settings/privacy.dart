import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart';
import '../../widgets/webviewbase.dart';

// 隐私协议页面 Privacy Policy
class Privacy extends StatefulWidget {
  const Privacy({super.key});

  @override
  State<Privacy> createState() => _PrivacyState();
}

class _PrivacyState extends State<Privacy> {
  bool isLoading = true; // 控制加载状态

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.backgroundColor,
      appBar: AppBar(
        // backgroundColor: ThemeManager.currentTheme.backgroundColor,
        automaticallyImplyLeading: false, // 禁用默认的返回按钮
        title: Row(
          children: [
            IconButton(
              onPressed: () {
                Navigator.pop(context); 
              },
              icon: Image(
                image: AssetImage(ThemeManager.getImagePath('btn_back')), width: 40, height: 40,
              ),
            ),
            const SizedBox(width: 10), // 按钮和标题之间的间距
            Text(
              "Privacy Policy",
              style: TextStyle(fontSize: 20, color: ThemeManager.currentTheme.textColor, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20.0, right: 20.0),
            child: WebViewBase(
              "https://littlegrass.cc/app/imtrans/privacy.html",
              onPageStarted: () {
                setState(() {
                  isLoading = true; // 页面开始加载时显示loading
                });
              },
              onPageFinished: () {
                setState(() {
                  isLoading = false; // 页面加载完成时隐藏loading
                });
              },
            ),
          ),
          if (isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
