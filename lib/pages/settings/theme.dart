import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import '../../util/theme_manager.dart';


class ThemePage extends StatefulWidget {
  const ThemePage({super.key});

  @override
  State<ThemePage> createState() => _ThemePageState();
}

class _ThemePageState extends State<ThemePage> {
  late bool _isDarkMode;
  late bool _isSystemMode;
  
  @override
  void initState() {
    super.initState();
    _isSystemMode = ThemeManager.themeMode == ThemeMode.system;
    _isDarkMode = ThemeManager.isDarkMode;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Theme(
      data: Theme.of(context).copyWith(
        switchTheme: SwitchThemeData(
          trackOutlineColor: WidgetStateProperty.all(Colors.transparent),
        ),
      ),
      child: Scaffold(
        backgroundColor: ThemeManager.currentTheme.backgroundColor,
        appBar: AppBar(
          backgroundColor: ThemeManager.currentTheme.backgroundColor,
          automaticallyImplyLeading: false,
          centerTitle: true,
          toolbarHeight: 60,
          titleSpacing: 0,
          title: Container(
            height: 60,
            width: double.infinity,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  l10n.darkMode,
                  style: TextStyle(
                    color: ThemeManager.currentTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Positioned(
                  left: 10,
                  top: 0,
                  bottom: 0,
                  child: IconButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: Image.asset(
                      ThemeManager.getImagePath('btn_back'),
                      width: 40,
                      height: 40,
                    ),
                  ),
                ),
              ]),
          ),
        ),
        
        body: ChangeNotifierProvider.value(
          value: ThemeManager(),
          child: Consumer<ThemeManager>(
            builder: (context, themeManager, child) {
              // 更新状态以反映当前主题模式
              _isSystemMode = ThemeManager.themeMode == ThemeMode.system;
              _isDarkMode = ThemeManager.isDarkMode;
              
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 添加说明文本
                    Padding(
                      padding: const EdgeInsets.only(left: 26, right: 16, top: 16, bottom: 10),
                      child: Text(
                        l10n.darkModeDescription,
                        style: TextStyle(
                          color: const Color(0xFF797979),
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                    
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        decoration: BoxDecoration( 
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            // 跟随系统开关 
                            Container(
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: ThemeManager.currentTheme.borderAreaBgColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      l10n.followSystem,
                                      style: TextStyle(
                                        color: ThemeManager.currentTheme.textColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  Switch(
                                    value: _isSystemMode,
                                    onChanged: (value) {
                                      setState(() {
                                        _isSystemMode = value;
                                        if (value) {
                                          // 设置为跟随系统
                                          ThemeManager.setThemeMode(ThemeMode.system);
                                        } else {
                                          // 设置为当前暗/亮模式
                                          ThemeManager.setThemeMode(
                                            _isDarkMode ? ThemeMode.dark : ThemeMode.light
                                          );
                                        }
                                      });
                                    },
                                    activeColor: Colors.white,
                                    activeTrackColor: ThemeManager.currentTheme.logoColor,
                                    inactiveThumbColor: Colors.white,
                                    inactiveTrackColor: Colors.grey.shade300,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                            // 夜间模式开关
                            Container(
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              decoration: BoxDecoration(
                                color: ThemeManager.currentTheme.borderAreaBgColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      l10n.darkMode,
                                      style: TextStyle(
                                        color: _isSystemMode ? Colors.grey : ThemeManager.currentTheme.textColor,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  Switch(
                                    value: _isDarkMode,
                                    onChanged: _isSystemMode ? null : (value) {
                                      setState(() {
                                        _isDarkMode = value;
                                        ThemeManager.setThemeMode(
                                          value ? ThemeMode.dark : ThemeMode.light
                                        );
                                      });
                                    },
                                    activeColor: Colors.white,
                                    activeTrackColor: _isSystemMode ? Colors.grey.shade300 : ThemeManager.currentTheme.logoColor,
                                    inactiveThumbColor: Colors.white,
                                    inactiveTrackColor: Colors.grey.shade300,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}