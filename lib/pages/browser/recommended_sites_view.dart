import 'package:flutter/material.dart';
import 'package:imtrans/services/manga_sites.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/services/browser_history_service.dart';
import 'package:imtrans/pages/browser/bookmarks_page.dart' as bookmarks_page;
import 'package:imtrans/services/bookmark_service.dart';

/// 推荐站点列表视图
/// 点击某个站点后通过回调将其 URL 传递给上层
class RecommendedSitesView extends StatefulWidget {
  final void Function(String url) onTap;

  const RecommendedSitesView({
    Key? key,
    required this.onTap,
  }) : super(key: key);

  @override
  State<RecommendedSitesView> createState() => _RecommendedSitesViewState();
}

class _RecommendedSitesViewState extends State<RecommendedSitesView> {
  final GlobalKey<AnimatedListState> _historyListKey = GlobalKey<AnimatedListState>();
  List<BrowserHistoryItem> _historyItems = [];

  @override
  void initState() {
    super.initState();
    _loadHistoryItems();
  }

  /// 加载历史记录项目
  Future<void> _loadHistoryItems() async {
    final items = await BrowserHistoryService.getHistory();
    if (mounted) {
      setState(() {
        _historyItems = items;
      });
    }
  }

  /// 导航到书签管理页面
  Future<void> _navigateToBookmarksPage(BuildContext context) async {
    final selectedUrl = await Navigator.of(context).push<String>(
      MaterialPageRoute(
        builder: (context) => const bookmarks_page.BookmarksPage(),
      ),
    );
    
    // 如果用户选择了书签，调用回调函数加载URL
    if (selectedUrl != null && selectedUrl.isNotEmpty) {
      debugPrint('从书签页面返回URL: $selectedUrl');
      widget.onTap(selectedUrl);
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<MangaSite>>(
      future: MangaSitesRepository.getAllSites(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            color: ThemeManager.currentTheme.backgroundColor,
            child: const Center(child: CircularProgressIndicator()),
          );
        }
        
        // 如果加载失败
        if (snapshot.hasError) {
          return Container(
            color: ThemeManager.currentTheme.backgroundColor,
            child: Center(
              child: Text(
                '',
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeManager.currentTheme.textColor,
                ),
              ),
            ),
          );
        }

        final List<MangaSite> sites = snapshot.data ?? [];
        // if (sites.isEmpty) {
        //   return Center(
        //     child: Text(
        //       '暂无推荐站点',
        //       style: const TextStyle(fontSize: 14),
        //     ),
        //   );
        // }

        return Container(
          color: ThemeManager.currentTheme.backgroundColor,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(26),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 推荐网站标题
                Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: Text(
                    AppLocalizations.of(context)!.recommendedWebsites,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: ThemeManager.currentTheme.textColor,
                    ),
                  ),
                ),

                // 推荐网站网格
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4, // 固定每行4个网站项目
                    mainAxisSpacing: 20,
                    crossAxisSpacing: 20,
                    childAspectRatio: 0.85, // 调整比例以适应两行文字
                  ),
                  itemCount: sites.length,
                  itemBuilder: (context, index) {
                    final MangaSite site = sites[index];
                    return GestureDetector(
                      onTap: () => widget.onTap(site.url),
                      child: _buildSiteItem(site),
                    );
                  },
                ),

                // 我的收藏部分
                const SizedBox(height: 40),
                _buildBookmarksSection(context),

                // 浏览历史部分
                const SizedBox(height: 40),
                _buildHistorySection(context),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建单个网站项目
  Widget _buildSiteItem(MangaSite site) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 48x48 外圆圈容器
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: ThemeManager.currentTheme.recommendedSitesBackgroundColor,
          ),
          child: Center(
            child: _buildInnerCircle(site),
          ),
        ),
        const SizedBox(height: 8),
        // 网站名称
        Text(
          site.name,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10,
            color: ThemeManager.currentTheme.textColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建内圆圈（28x28）
  Widget _buildInnerCircle(MangaSite site) {
    final hasIcon = site.icon != null && site.icon!.isNotEmpty;
    if (hasIcon) {
      return Container(
        width: 28,
        height: 28,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
        ),
        child: ClipOval(
          child: _buildFaviconWidget(site),
        ),
      );
    } else {
      // Use BookmarkService for consistent fallback icon
      return BookmarkService.instance.buildBookmarkIcon(
        site.name,
        site.url,
        null, // No favicon for fallback
        28,
        faviconCached: true, // Not a cached bookmark
      );
    }
  }

  /// 构建favicon图标
  Widget _buildFaviconWidget(MangaSite site) {
    if (site.icon!.startsWith('http')) {
      return Image.network(
        site.icon!,
        width: 28,
        height: 28,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => BookmarkService.instance.buildBookmarkIcon(
          site.name,
          site.url,
          null,
          28,
          faviconCached: true,
        ),
      );
    } else {
      return Image.asset(
        site.icon!,
        width: 28,
        height: 28,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => BookmarkService.instance.buildBookmarkIcon(
          site.name,
          site.url,
          null,
          28,
          faviconCached: true,
        ),
      );
    }
  }



  /// 构建我的收藏部分
  Widget _buildBookmarksSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 我的收藏标题和管理按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppLocalizations.of(context)!.myBookmarks,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ThemeManager.currentTheme.textColor,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToBookmarksPage(context),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text(
                AppLocalizations.of(context)!.manageBookmarks,
                style: TextStyle(
                  fontSize: 14,
                  color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.7),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),

        // 收藏列表
        FutureBuilder<List<BookmarkItem>>(
          future: BookmarkService.instance.getRecentBookmarks(limit: 8),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final bookmarks = snapshot.data ?? [];

            if (bookmarks.isEmpty) {
              return Container(
                height: 100,
                alignment: Alignment.center,
                child: Text(
                  AppLocalizations.of(context)!.noBookmarks,
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.6),
                  ),
                ),
              );
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisSpacing: 10,
                crossAxisSpacing: 2,
                childAspectRatio: 0.85, // 调整比例以适应两行文字
              ),
              itemCount: bookmarks.length,
              itemBuilder: (context, index) {
                final bookmark = bookmarks[index];
                return GestureDetector(
                  onTap: () => widget.onTap(bookmark.url),
                  onLongPress: () => _showDeleteBookmarkDialog(context, bookmark),
                  child: _buildBookmarkItem(bookmark),
                );
              },
            );
          },
        ),
      ],
    );
  }



  /// 构建单个书签项目
  Widget _buildBookmarkItem(BookmarkItem bookmark) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 48x48 外圆圈容器
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: ThemeManager.currentTheme.recommendedSitesBackgroundColor,
          ),
          child: Center(
            child: BookmarkService.instance.buildBookmarkIcon(
              bookmark.title,
              bookmark.url,
              bookmark.favicon,
              28,
              faviconCached: bookmark.faviconCached,
            ),
          ),
        ),
        const SizedBox(height: 8),
        // 书签标题
        Text(
          bookmark.title,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10,
            color: ThemeManager.currentTheme.textColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 显示删除书签对话框
  void _showDeleteBookmarkDialog(BuildContext context, BookmarkItem bookmark) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.deleteBookmark),
          content: Text(AppLocalizations.of(context)!.deleteBookmarkConfirm(bookmark.title)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context)!.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteBookmark(bookmark);
              },
              child: Text(AppLocalizations.of(context)!.delete),
            ),
          ],
        );
      },
    );
  }

  /// 删除书签
  Future<void> _deleteBookmark(BookmarkItem bookmark) async {
    try {
      await BookmarkService.instance.deleteBookmark(bookmark.url);

      // 触发重建以更新UI
      setState(() {});

      debugPrint('书签已删除: ${bookmark.title}');
    } catch (e) {
      debugPrint('删除书签失败: $e');
    }
  }

  /// 构建浏览历史部分
  Widget _buildHistorySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 浏览历史标题
        Text(
          AppLocalizations.of(context)!.browsingHistory,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeManager.currentTheme.textColor,
          ),
        ),
        const SizedBox(height: 20),

        // 历史记录列表
        _historyItems.isEmpty
            ? Container(
                height: 100,
                alignment: Alignment.center,
                child: Text(
                  AppLocalizations.of(context)!.noBrowsingHistory,
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.6),
                  ),
                ),
              )
            : AnimatedList(
                key: _historyListKey,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                initialItemCount: _historyItems.length,
                itemBuilder: (context, index, animation) {
                  if (index >= _historyItems.length) {
                    return const SizedBox.shrink();
                  }
                  return _buildAnimatedHistoryItem(_historyItems[index], animation);
                },
              ),
      ],
    );
  }

  /// 构建带动画的历史记录项目
  Widget _buildAnimatedHistoryItem(BrowserHistoryItem historyItem, Animation<double> animation) {
    return SlideTransition(
      position: animation.drive(
        Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).chain(CurveTween(curve: Curves.easeOut)),
      ),
      child: FadeTransition(
        opacity: animation,
        child: Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: _buildHistoryItem(historyItem),
        ),
      ),
    );
  }

  /// 构建单个历史记录项目
  Widget _buildHistoryItem(BrowserHistoryItem historyItem) {
    return GestureDetector(
      onTap: () => widget.onTap(historyItem.url),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ThemeManager.currentTheme.recommendedSitesBackgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // 网站图标
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ThemeManager.currentTheme.recommendedSitesBackgroundColor,
              ),
              child: Center(
                child: BookmarkService.instance.buildBookmarkIcon(
                  historyItem.title,
                  historyItem.url,
                  historyItem.favicon,
                  24,
                  faviconCached: historyItem.faviconCached,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // 网站信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 网站标题
                  Text(
                    historyItem.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ThemeManager.currentTheme.textColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  // 网站URL
                  Text(
                    historyItem.url,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 11,
                      color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.5),
                    ),
                  ),
                  const SizedBox(height: 4),
                  // 访问时间
                  Text(
                    _formatTimestamp(context, historyItem.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),

            // 删除按钮
            GestureDetector(
              onTap: () => _deleteHistoryItem(historyItem),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.delete_outline,
                  size: 20,
                  color: ThemeManager.currentTheme.textColor.withValues(alpha: 0.6),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 格式化时间戳
  String _formatTimestamp(BuildContext context, DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return AppLocalizations.of(context)!.daysAgo(difference.inDays);
    } else if (difference.inHours > 0) {
      return AppLocalizations.of(context)!.hoursAgo(difference.inHours);
    } else if (difference.inMinutes > 0) {
      return AppLocalizations.of(context)!.minutesAgo(difference.inMinutes);
    } else {
      return AppLocalizations.of(context)!.justNow;
    }
  }

  /// 删除历史记录（带动画）
  Future<void> _deleteHistoryItem(BrowserHistoryItem historyItem) async {
    try {
      // 找到要删除的项目索引
      final index = _historyItems.indexWhere((item) => item.url == historyItem.url);
      if (index == -1) return;

      // 从本地列表中移除
      final removedItem = _historyItems.removeAt(index);

      // 执行动画移除
      _historyListKey.currentState?.removeItem(
        index,
        (context, animation) => SlideTransition(
          position: animation.drive(
            Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).chain(CurveTween(curve: Curves.easeOut)),
          ),
          child: FadeTransition(
            opacity: animation,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: _buildHistoryItem(removedItem),
            ),
          ),
        ),
        duration: const Duration(milliseconds: 300),
      );

      // 从持久化存储中删除
      await BrowserHistoryService.deleteHistoryItem(historyItem);

      debugPrint('历史记录已删除: ${historyItem.title}');
    } catch (e) {
      debugPrint('删除历史记录失败: $e');
      // 如果删除失败，重新加载历史记录
      _loadHistoryItems();
    }
  }


}