import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'base_extractor.dart';

/// 通用网站图片提取器
/// 处理一般网站的图片提取和下载
class GenericExtractor extends BaseImageExtractor {
  GenericExtractor({
    required InAppWebViewController controller,
    required String currentUrl,
  }) : super(
    controller: controller,
    currentUrl: currentUrl,
  );
  
  @override
  Future<List<String>> extractImageUrls() async {
    const String jsGetImages = '''
      function getAllImages() {
        const images = [];
        // 获取所有图片元素
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          // 检查图片是否有效
          if (img.src && img.src.trim() !== '' && 
              !img.src.startsWith('data:') && 
              img.width > 100 && img.height > 100) {
            images.push(img.src);
          }
          // 检查data-src属性（懒加载图片）
          if (img.dataset && img.dataset.src && 
              img.dataset.src.trim() !== '' && 
              !img.dataset.src.startsWith('data:')) {
            images.push(img.dataset.src);
          }
        });
        
        // 获取背景图片
        const elementsWithBg = document.querySelectorAll('*');
        elementsWithBg.forEach(el => {
          const style = window.getComputedStyle(el);
          const bgImage = style.backgroundImage;
          if (bgImage && bgImage !== 'none') {
            const match = bgImage.match(/url\(['"\]?([^'"\)]+)['"\]?\)/);
            if (match && match[1] && !match[1].startsWith('data:')) {
              images.push(match[1]);
            }
          }
        });
        
        return JSON.stringify([...new Set(images)]);
      }
      getAllImages();
    ''';
    try {
      final dynamic result = await controller.evaluateJavascript(source: jsGetImages);
      if (result == null) return [];
      // 解析JSON结果
      final List<dynamic> srcList = jsonToList(result);
      final List<String> urls = srcList.map((e) => e.toString()).toList();
      return urls;
    } catch (e) {
      debugPrint('提取图片URL出错: $e');
      return [];
    }
  }
  
  /// 将JSON字符串转换为List
  List<dynamic> jsonToList(String jsonStr) {
    try {
      // 移除可能的引号包裹
      String processedStr = jsonStr;
      if (processedStr.startsWith('"') && processedStr.endsWith('"')) {
        processedStr = processedStr.substring(1, processedStr.length - 1);
        // 处理转义字符
        processedStr = processedStr.replaceAll('\\"', '"');
      }
      
      try {
        // 尝试直接解析JSON
        final decoded = json.decode(processedStr);
        if (decoded is List) {
          return decoded;
        }
      } catch (e) {
        // 如果直接解析失败，使用备用方法
        debugPrint('直接JSON解析失败，使用备用方法: $e');
      }
      
      return List<dynamic>.from(Uri.decodeComponent(processedStr).codeUnits)
          .map((e) => String.fromCharCode(e))
          .join('')
          .trim()
          .split(',');
    } catch (e) {
      debugPrint('JSON解析错误: $e');
      return [];
    }
  }
}