import 'package:flutter/material.dart';

class TransparentRoute extends PageRoute {
  final WidgetBuilder builder;
  final String name;
  TransparentRoute(
    {
      required this.builder,
      required this.name
    }
  ) : super(
    settings: RouteSettings(name: name),
  );

  @override
  Widget buildTransitions(
      BuildContext context,
      Animation<double> animation,
      Animation<double> secondaryAnimation,
      Widget child,
  ) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
    return Title(
      title: "",
      color: Theme.of(context).primaryColor.withAlpha(255),
      child: builder(context),
    );
  }

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 0);

  @override
  bool get opaque => false;
}
