import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

/// Toast position enumeration
enum ToastPosition {
  top,
  center,
  bottom,
}

/// Toast duration enumeration
enum ToastDuration {
  short,
  long,
}

/// Configuration class for toast appearance
class ToastConfig {
  final Color backgroundColor;
  final Color textColor;
  final double fontSize;
  final ToastDuration duration;
  final double borderRadius;
  final EdgeInsets padding;

  const ToastConfig({
    this.backgroundColor = const Color(0xDD000000), // Colors.black54 equivalent
    this.textColor = Colors.white,
    this.fontSize = 16.0,
    this.duration = ToastDuration.short,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
  });

  ToastConfig copyWith({
    Color? backgroundColor,
    Color? textColor,
    double? fontSize,
    ToastDuration? duration,
    double? borderRadius,
    EdgeInsets? padding,
  }) {
    return ToastConfig(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      fontSize: fontSize ?? this.fontSize,
      duration: duration ?? this.duration,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
    );
  }
}

/// Centralized Toast Widget for consistent toast display across the app
class ToastWidget {
  static ToastWidget? _instance;
  static ToastWidget get instance => _instance ??= ToastWidget._();
  
  ToastWidget._();

  // Default configuration
  static const ToastConfig _defaultConfig = ToastConfig();
  
  // Track active toasts to prevent spam
  static final Set<String> _activeToasts = <String>{};
  static Timer? _cleanupTimer;

  /// Show a toast message with configurable position and styling
  /// 
  /// Parameters:
  /// * [message] - The text to display in the toast
  /// * [position] - Where to display the toast (top, center, bottom)
  /// * [config] - Optional configuration for appearance and duration
  /// * [context] - Optional BuildContext for custom toast implementation
  static Future<void> show(
    String message, {
    ToastPosition position = ToastPosition.center,
    ToastConfig? config,
    BuildContext? context,
  }) async {
    // Prevent duplicate toasts
    final toastKey = '$message-${position.name}';
    if (_activeToasts.contains(toastKey)) {
      return;
    }

    _activeToasts.add(toastKey);
    
    // Clean up after toast duration
    _scheduleCleanup(toastKey, config?.duration ?? _defaultConfig.duration);

    final effectiveConfig = config ?? _defaultConfig;

    try {
      // Use fluttertoast for consistent cross-platform behavior
      await Fluttertoast.showToast(
        msg: message,
        toastLength: _mapDurationToToastLength(effectiveConfig.duration),
        gravity: _mapPositionToGravity(position),
        timeInSecForIosWeb: _mapDurationToSeconds(effectiveConfig.duration),
        backgroundColor: effectiveConfig.backgroundColor,
        textColor: effectiveConfig.textColor,
        fontSize: effectiveConfig.fontSize,
      );
    } catch (e) {
      debugPrint('ToastWidget: Error showing toast: $e');
      // Fallback to debug print if toast fails
      debugPrint('Toast: $message');
    }
  }

  /// Show a success toast with green styling
  static Future<void> showSuccess(
    String message, {
    ToastPosition position = ToastPosition.center,
    ToastConfig? config,
  }) async {
    final successConfig = (config ?? _defaultConfig).copyWith(
      backgroundColor: const Color(0xDD4CAF50), // Green
      textColor: Colors.white,
    );
    
    await show(message, position: position, config: successConfig);
  }

  /// Show an error toast with red styling
  static Future<void> showError(
    String message, {
    ToastPosition position = ToastPosition.center,
    ToastConfig? config,
  }) async {
    final errorConfig = (config ?? _defaultConfig).copyWith(
      backgroundColor: const Color(0xDDF44336), // Red
      textColor: Colors.white,
    );
    
    await show(message, position: position, config: errorConfig);
  }

  /// Show a warning toast with orange styling
  static Future<void> showWarning(
    String message, {
    ToastPosition position = ToastPosition.center,
    ToastConfig? config,
  }) async {
    final warningConfig = (config ?? _defaultConfig).copyWith(
      backgroundColor: const Color(0xDDFF9800), // Orange
      textColor: Colors.white,
    );
    
    await show(message, position: position, config: warningConfig);
  }

  /// Show an info toast with blue styling
  static Future<void> showInfo(
    String message, {
    ToastPosition position = ToastPosition.center,
    ToastConfig? config,
  }) async {
    final infoConfig = (config ?? _defaultConfig).copyWith(
      backgroundColor: const Color(0xDD2196F3), // Blue
      textColor: Colors.white,
    );
    
    await show(message, position: position, config: infoConfig);
  }

  static Future<void> showTop(
    String message, {
      ToastConfig? config,
  }) async {
    await show(message, position: ToastPosition.top, config: config);
  }

  static Future<void> showBottom(
    String message, {
      ToastConfig? config,
  }) async {
    await show(message, position: ToastPosition.bottom, config: config);
  }

  /// Backward compatibility method that matches the original Util.showToast signature
  static Future<void> showToast(
    String message, {
    ToastGravity? gravity = ToastGravity.CENTER,
    ToastConfig? config,
  }) async {
    final position = _mapGravityToPosition(gravity ?? ToastGravity.CENTER);
    await show(message, position: position, config: config);
  }

  /// Clear all active toast tracking (useful for testing or cleanup)
  static void clearActiveToasts() {
    _activeToasts.clear();
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
  }

  // Private helper methods

  static void _scheduleCleanup(String toastKey, ToastDuration duration) {
    final cleanupDelay = Duration(
      milliseconds: _mapDurationToSeconds(duration) * 1000 + 500, // Add 500ms buffer
    );
    
    Timer(cleanupDelay, () {
      _activeToasts.remove(toastKey);
    });
  }

  static Toast _mapDurationToToastLength(ToastDuration duration) {
    switch (duration) {
      case ToastDuration.short:
        return Toast.LENGTH_SHORT;
      case ToastDuration.long:
        return Toast.LENGTH_LONG;
    }
  }

  static int _mapDurationToSeconds(ToastDuration duration) {
    switch (duration) {
      case ToastDuration.short:
        return 2;
      case ToastDuration.long:
        return 4;
    }
  }

  static ToastGravity _mapPositionToGravity(ToastPosition position) {
    switch (position) {
      case ToastPosition.top:
        return ToastGravity.TOP;
      case ToastPosition.center:
        return ToastGravity.CENTER;
      case ToastPosition.bottom:
        return ToastGravity.BOTTOM;
    }
  }

  static ToastPosition _mapGravityToPosition(ToastGravity gravity) {
    switch (gravity) {
      case ToastGravity.TOP:
        return ToastPosition.top;
      case ToastGravity.CENTER:
        return ToastPosition.center;
      case ToastGravity.BOTTOM:
        return ToastPosition.bottom;
      default:
        return ToastPosition.center;
    }
  }

  /// Static method to show a test toast for debugging
  static void showTestToast(BuildContext context) {
    show('Test Toast Message', position: ToastPosition.center);
  }

  /// Static method to show all toast types for testing
  static void showAllToastTypes(BuildContext context) {
    Future.delayed(const Duration(milliseconds: 0), () {
      show('Default Toast', position: ToastPosition.bottom);
    });
    
    Future.delayed(const Duration(milliseconds: 500), () {
      showSuccess('Success Toast', position: ToastPosition.bottom);
    });
    
    Future.delayed(const Duration(milliseconds: 1000), () {
      showError('Error Toast', position: ToastPosition.bottom);
    });
    
    Future.delayed(const Duration(milliseconds: 1500), () {
      showWarning('Warning Toast', position: ToastPosition.bottom);
    });
    
    Future.delayed(const Duration(milliseconds: 2000), () {
      showInfo('Info Toast', position: ToastPosition.bottom);
    });
  }

  /// Test different positions
  static void testPositions(BuildContext context) {
    Future.delayed(const Duration(milliseconds: 0), () {
      show('Top Toast', position: ToastPosition.top);
    });
    
    Future.delayed(const Duration(milliseconds: 1000), () {
      show('Center Toast', position: ToastPosition.center);
    });
    
    Future.delayed(const Duration(milliseconds: 2000), () {
      show('Bottom Toast', position: ToastPosition.bottom);
    });
  }
}
