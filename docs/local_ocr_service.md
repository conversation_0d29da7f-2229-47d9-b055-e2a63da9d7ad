# Local OCR Service Documentation

## Overview

The `LocalOcrService` is a singleton service that provides optical character recognition (OCR) functionality using the `vision_text_recognition` package. It can extract text from images with bounding box information and confidence scores.

## Features

- **Singleton Pattern**: Ensures only one instance of the service exists
- **Multiple Input Formats**: Supports both image bytes and file paths
- **Bounding Box Information**: Returns text with precise positioning data
- **Confidence Scores**: Provides confidence levels for recognized text
- **Error Handling**: Comprehensive error handling with custom exceptions
- **JSON Serialization**: Support for serializing/deserializing OCR results

## Installation

The service uses the `vision_text_recognition` package which is already included in the project's `pubspec.yaml`:

```yaml
dependencies:
  vision_text_recognition: ^1.0.2
```

## Usage

### Basic Usage

```dart
import 'package:imtrans/services/local_ocr_service.dart';

// Get the singleton instance
final ocrService = LocalOcrService();

// Initialize the service (required before use)
await ocrService.initialize();

// Extract text from image bytes
final Uint8List imageBytes = ...; // Your image data
final List<OcrTextElement> textElements = await ocrService.extractTextFromBytes(imageBytes);

// Extract text from image file path
final String imagePath = '/path/to/image.jpg';
final List<OcrTextElement> textElements = await ocrService.extractTextFromPath(imagePath);

// Clean up when done
ocrService.dispose();
```

### Working with Results

```dart
for (final element in textElements) {
  print('Text: ${element.text}');
  print('Confidence: ${element.confidence}');
  print('Bounding Box: ${element.boundingBox}');
  
  // Access bounding box properties
  final box = element.boundingBox;
  print('Position: (${box.left}, ${box.top}) to (${box.right}, ${box.bottom})');
  print('Size: ${box.width} x ${box.height}');
  print('Center: ${box.center}');
  
  // Convert to Flutter Rect
  final Rect rect = box.toRect();
}
```

### JSON Serialization

```dart
// Convert to JSON
final Map<String, dynamic> json = textElement.toJson();

// Create from JSON
final OcrTextElement element = OcrTextElement.fromJson(json);
```

## API Reference

### LocalOcrService

#### Methods

##### `initialize()`
```dart
Future<void> initialize()
```
Initializes the OCR service. Must be called before using any OCR functionality.

**Throws**: `OcrException` if initialization fails.

##### `extractTextFromBytes(Uint8List imageBytes)`
```dart
Future<List<OcrTextElement>> extractTextFromBytes(Uint8List imageBytes)
```
Extracts text from image bytes.

**Parameters**:
- `imageBytes`: The image data as bytes

**Returns**: List of recognized text elements with positioning information

**Throws**: `OcrException` if service not initialized or extraction fails

##### `extractTextFromPath(String imagePath)`
```dart
Future<List<OcrTextElement>> extractTextFromPath(String imagePath)
```
Extracts text from image file path.

**Parameters**:
- `imagePath`: Path to the image file

**Returns**: List of recognized text elements with positioning information

**Throws**: `OcrException` if service not initialized or extraction fails

##### `dispose()`
```dart
void dispose()
```
Disposes the service and cleans up resources.

#### Properties

##### `isInitialized`
```dart
bool get isInitialized
```
Returns whether the service has been initialized.

### OcrTextElement

Represents a text element recognized by OCR.

#### Properties

- `String text`: The recognized text
- `OcrBoundingBox boundingBox`: The bounding box of the text
- `double confidence`: Confidence score (0.0 to 1.0)

#### Methods

- `Map<String, dynamic> toJson()`: Convert to JSON
- `OcrTextElement.fromJson(Map<String, dynamic> json)`: Create from JSON
- `String toString()`: String representation

### OcrBoundingBox

Represents a bounding box for OCR text.

#### Properties

- `double left`: Left coordinate
- `double top`: Top coordinate  
- `double right`: Right coordinate
- `double bottom`: Bottom coordinate
- `double width`: Width of the box (computed)
- `double height`: Height of the box (computed)
- `Offset center`: Center point of the box (computed)

#### Methods

- `Rect toRect()`: Convert to Flutter Rect
- `Map<String, dynamic> toJson()`: Convert to JSON
- `OcrBoundingBox.fromJson(Map<String, dynamic> json)`: Create from JSON
- `String toString()`: String representation

### OcrException

Custom exception for OCR operations.

#### Properties

- `String message`: Error message
- `dynamic originalError`: Original error (optional)

#### Methods

- `String toString()`: String representation including original error if present

## Error Handling

The service uses custom `OcrException` for error handling:

```dart
try {
  final results = await ocrService.extractTextFromBytes(imageBytes);
} catch (e) {
  if (e is OcrException) {
    print('OCR Error: ${e.message}');
    if (e.originalError != null) {
      print('Original Error: ${e.originalError}');
    }
  }
}
```

## Common Error Scenarios

1. **Service Not Initialized**: Call `initialize()` before using OCR functions
2. **Invalid Image Data**: Ensure image bytes are valid image format
3. **File Not Found**: Verify file path exists and is accessible
4. **Unsupported Format**: Use supported image formats (JPEG, PNG, etc.)

## Performance Considerations

- OCR processing can be CPU intensive
- Consider processing images on a background isolate for large batches
- Dispose the service when no longer needed to free resources
- The service is a singleton, so initialization only needs to happen once

## Integration with Browser Translation

The service is integrated with the browser's local translation feature:

```dart
// In MultiBrowserPage
final textElements = await _ocrService.extractTextFromBytes(imageBytes);
for (final element in textElements) {
  final translatedText = await _translationService.translateText(
    element.text,
    TranslateLanguage.japanese,
    TranslateLanguage.english,
  );
  // Overlay translated text at element.boundingBox position
}
```

## Testing

Comprehensive unit tests are available in `test/services/local_ocr_service_test.dart`. Run tests with:

```bash
flutter test test/services/local_ocr_service_test.dart
```
