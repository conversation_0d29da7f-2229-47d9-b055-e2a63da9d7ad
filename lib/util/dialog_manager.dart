import 'dart:async';
import 'package:flutter/material.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/widgets/breakall.dart';
import 'package:imtrans/widgets/transparentoute.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';

/// 对话框类型枚举
enum DialogType {
  confirm,    // 确认对话框
  input,      // 输入对话框
  custom      // 自定义对话框
}

/// 对话框管理器，用于显示各种对话框
/// 基本用法：
/// // 获取单例实例
/// final dialogManager = DialogManager.instance;
/// 
/// // 设置主上下文（通常在应用初始化时设置）
/// dialogManager.setMainContext(context);
/// 
/// // 显示对话框
/// dialogManager.showDialog(...);
/// ```
class DialogManager {
  // 单例实现
  static final DialogManager _instance = DialogManager._internal();
  factory DialogManager() => _instance;
  DialogManager._internal();

  /// 单例访问器，用于获取DialogManager的唯一实例
  /// 
  /// 使用示例：
  /// ```dart
  /// final dialogManager = DialogManager.instance;
  /// ```
  static DialogManager get instance => _instance;

  /// 存储当前显示的对话框，键为对话框名称，值为对应的路由
  final Map<String, dynamic> _viewIds = {};
  
  /// 主上下文，用于显示对话框
  /// 通常在应用初始化时通过setMainContext方法设置
  BuildContext? _mainContext;

  /// 设置主上下文，必须在显示对话框前调用此方法
  /// 
  /// 通常在应用的主页面初始化时调用，例如在StatefulWidget的initState方法中：
  /// ```dart
  /// @override
  /// void initState() {
  ///   super.initState();
  ///   DialogManager.instance.setMainContext(context);
  /// }
  /// ```
  /// 
  /// 参数：
  /// * [context] - 用于显示对话框的BuildContext，通常是应用的主上下文
  void setMainContext(BuildContext? context) {
    _mainContext = context;
  }

  /// 获取当前设置的主上下文
  /// 
  /// 返回值：
  /// * 当前设置的BuildContext，如果未设置则返回null
  BuildContext? get mainContext => _mainContext;

  /// 检查指定名称的页面是否存在
  /// 
  /// 参数：
  /// * [pageName] - 要检查的页面名称
  /// 
  /// 返回值：
  /// * true - 页面存在
  /// * false - 页面不存在
  bool pageIsExist(String pageName) {
    return _viewIds[pageName] != null;
  }

  /// 显示确认对话框
  /// 
  /// 参数：
  /// * [title] - 对话框标题
  /// * [message] - 对话框消息内容
  /// * [onConfirm] - 确认按钮回调
  /// * [confirmText] - 确认按钮文本，默认为本地化的"OK"
  Future<bool> showConfirmDialog({
    String? title,
    required String message,
    VoidCallback? onConfirm,
    String? confirmText,
    // VoidCallback? onCancel,
    // String cancelText = "Cancel"
  }) async {
    final localizations = AppLocalizations.of(_mainContext!)!;
    return await showDialog(
      title: title ?? "",
      message: message,
      onConfirm: () {
        closeDialog(null, name: "DialogView");
        onConfirm?.call();
      },
      confirmText: confirmText ?? localizations.ok,
      // cancel: () {},
      // cancelTitle: cancelText,
    );
  }

  /// 显示输入对话框
  /// 
  /// 参数：
  /// * [title] - 对话框标题
  /// * [message] - 对话框消息内容
  /// * [onConfirm] - 确认按钮回调，接收输入文本
  /// * [confirmText] - 确认按钮文本，默认为本地化的"Confirm"
  /// * [initialInputText] - 输入框初始文本，可选
  /// * [cancelText] - 取消按钮文本，默认为本地化的"Cancel"
  Future<String?> showInputDialog({
    required String title,
    required String message,
    required Function(String) onConfirm,
    String? confirmText,
    String? initialInputText,
    String? cancelText
  }) async {
    final localizations = AppLocalizations.of(_mainContext!)!;
    return await showDialog(
      title: title,
      message: message,
      onConfirm: onConfirm,
      confirmText: confirmText ?? localizations.confirm,
      onCancel: () {},
      cancelText: cancelText ?? localizations.cancel,
      inputText: initialInputText,
    );
  }

  Future<bool> showDeleteDialog({
    required String message,
    required VoidCallback onConfirm,
    // VoidCallback? onCancel,
  }) async {
    final localizations = AppLocalizations.of(_mainContext!)!;
    return await showDialog(
      title: localizations.delete,
      message: message,
      onConfirm: onConfirm,
      confirmText: localizations.delete,
      onCancel: () {},
      // cancelTitle: cancelText,
    );
  }

  /// 显示一个自定义对话框，支持标题、内容、确认按钮、取消按钮和输入框功能。
  /// 
  /// 该方法会返回一个Future，可以通过await等待用户操作完成并获取结果。
  /// 
  /// 参数说明：
  /// * [title] - 对话框的标题
  /// * [content] - 对话框的内容文本
  /// * [submit] - 必需参数，确认按钮的回调函数
  ///   - 当有输入框时(inputText != null)，回调函数会接收用户输入的文本作为参数
  ///   - 当没有输入框时，回调函数不接收参数
  /// * [submitTitle] - 必需参数，确认按钮的文本
  /// * [cancel] - 可选参数，取消按钮的回调函数，如果为null则不显示取消按钮
  /// * [cancelTitle] - 可选参数，取消按钮的文本
  /// * [inputText] - 可选参数，如果提供则显示带有初始文本的输入框
  /// 
  /// 返回值：
  /// * 当有输入框时：
  ///   - 用户点击确认按钮：返回用户输入的文本
  ///   - 用户点击取消按钮：返回null
  /// * 当没有输入框时：
  ///   - 用户点击确认按钮：返回true
  ///   - 用户点击取消按钮：返回false
  /// 
  /// 使用示例：
  /// ```dart
  /// // 显示普通确认对话框
  /// bool result = await DialogManager.instance.showDialog(
  ///   '确认删除',
  ///   '确定要删除这个项目吗？',
  ///   submit: () {
  ///     // 用户点击确认后的操作
  ///   },
  ///   submitTitle: '确认',
  ///   cancel: () {
  ///     // 用户点击取消后的操作
  ///   },
  ///   cancelTitle: '取消',
  /// );
  /// 
  /// // 显示带输入框的对话框
  /// String? name = await DialogManager.instance.showDialog(
  ///   '重命名',
  ///   '请输入新名称：',
  ///   submit: (String text) {
  ///     // 用户输入文本并点击确认后的操作
  ///   },
  ///   submitTitle: '确认',
  ///   cancel: () {
  ///     // 用户点击取消后的操作
  ///   },
  ///   cancelTitle: '取消',
  ///   inputText: '默认名称', // 输入框的初始文本
  /// );
  /// 
  Future<dynamic> showDialog({
      required String title,
      required String message,
      required Function onConfirm,
      required String confirmText,
      Function? onCancel,
      String? cancelText,
      String? inputText}) {
    Completer<dynamic> completer = Completer<dynamic>();

    // 先检查是否已存在弹窗，如果存在则先关闭
    if (_viewIds["DialogView"] != null) {
      closeDialog(null, name: "DialogView");
    }
    // 延迟一帧再显示新弹窗
    Future.microtask(() {
      navigatorTransparentPush(_DialogView(
        title,
        message,
        submit: inputText != null
            ? (String text) {
                closeDialog(null, name: "DialogView");
                onConfirm(text);
                if (!completer.isCompleted) {
                  completer.complete(text);
                }
              }
            : () {
                closeDialog(null, name: "DialogView");
                onConfirm();
                if (!completer.isCompleted) {
                  completer.complete(true);
                }
              },
        submitTitle: confirmText,
        cancel: onCancel != null ? () {
          closeDialog(null, name: "DialogView");
          onCancel();
          if (!completer.isCompleted) {
            completer.complete(inputText != null ? null : false);
          }
        } : null,
        cancelTitle: cancelText,
        inputText: inputText,
      ));
    });

    return completer.future;
  }

  /// 使用透明路由推送视图，显示自定义视图组件
  /// 
  /// 该方法使用透明背景的路由来显示视图，适合显示对话框、弹出菜单等覆盖在当前页面上的UI组件。
  /// 方法会自动管理视图的生命周期，包括添加到_viewIds和在路由关闭时移除。
  /// 
  /// 参数：
  /// * [view] - 要显示的Widget视图
  /// 
  /// 使用示例：
  /// ```dart
  /// // 显示自定义视图
  /// DialogManager.instance.navigatorTransparentPush(
  ///   MyCustomView(param1: value1, param2: value2)
  /// );
  /// ```
  Future<void> navigatorTransparentPush(Widget view) async {
    if (_mainContext == null || !_mainContext!.mounted) return;
    
    String name = view.runtimeType.toString();
    // 如果存在旧的路由，清理掉
    if (_viewIds[name] != null) {
      _viewIds.remove(name);
    }
    
    TransparentRoute router = TransparentRoute(
      name: name,
      builder: (context) => view,
    );
    
    _viewIds[name] = router;
    await Navigator.push(_mainContext!, router).then((_) {
      // 路由退出时清理 viewIds
      _viewIds.remove(name);
    });
  }

  /// 关闭视图，用于关闭当前显示的对话框或其他视图
  /// 
  /// 可以通过传入视图实例或视图名称来关闭特定视图。
  /// 
  /// 参数：
  /// * [view] - 要关闭的视图实例，如果提供此参数，则会自动获取其类型名称
  /// * [name] - 要关闭的视图名称，当view参数为null时使用
  /// * [showAni] - 是否显示关闭动画
  ///   - true（默认）：使用Navigator.pop()关闭，会有动画效果
  ///   - false：使用Navigator.removeRoute()关闭，没有动画效果
  /// 
  /// 使用示例：
  /// ```dart
  /// // 通过视图名称关闭
  /// DialogManager.instance.closeView(null, name: "DialogView");
  /// 
  /// // 通过视图实例关闭
  /// DialogManager.instance.closeView(myView);
  /// 
  /// // 关闭视图但不显示动画
  /// DialogManager.instance.closeView(null, name: "DialogView", showAni: false);
  /// ```
  void closeDialog(dynamic view, {String? name, bool? showAni = true}) {
    if (_mainContext == null || !_mainContext!.mounted) return;
    
    if (view != null) {
      name = view.runtimeType.toString();
    }
    
    if (name != null && _viewIds[name] != null) {
      if (showAni == true) {
        try {
          Navigator.pop(_mainContext!);
        } catch (e) {
          debugPrint("Pop error: $e");
        }
      } else {
        Navigator.removeRoute(_mainContext!, _viewIds[name]);
      }
      _viewIds.remove(name);
    }
  }
}

/// 对话框视图组件
class _DialogView extends StatefulWidget {
  final String title;
  final String? content;
  final Function submit;
  final String submitTitle;
  final Function? cancel;
  final String? cancelTitle;
  final String? inputText;

  const _DialogView(
    this.title,
    this.content, {
    required this.submit,
    required this.submitTitle,
    this.cancel,
    this.cancelTitle,
    this.inputText,
  });

  @override
  State<_DialogView> createState() => _DialogViewState();
}

class _DialogViewState extends State<_DialogView> {
  final TextEditingController _inputController = TextEditingController();

  @override
  void initState() {
    if (widget.inputText != null) {
      _inputController.text = widget.inputText!;
    }
    super.initState();
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  Widget _createButton(String title, Color bgColor, VoidCallback onPressed) {
    return Container(
      width: 118,
      height: 38,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(10)
      ),
      child: TextButton(
        onPressed: () {
          debugPrint("Button pressed: $title");
          onPressed();
        },
        style: const ButtonStyle(
          padding: WidgetStatePropertyAll(EdgeInsets.all(0))
        ),
        child: Text(
          title,
          style: const TextStyle(
            color: Color(0xff1b1c1e),
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  void _handleConfirm() {
    debugPrint("_handleConfirm called");
    // 先关闭对话框
    if (mounted) {
      debugPrint("Navigator pop called in _handleConfirm");
      Navigator.of(context).pop();
    } else {
      debugPrint("Component not mounted in _handleConfirm");
      return;
    }
    
    // 然后再执行回调
    if (widget.inputText != null) {
      widget.submit(_inputController.text);
    } else {
      widget.submit();
    }
  }

  void _handleCancel() {
    debugPrint("_handleCancel called");
    // 先关闭对话框
    if (mounted) {
      debugPrint("Navigator pop called in _handleCancel");
      Navigator.of(context).pop();
    } else {
      debugPrint("Component not mounted in _handleCancel");
      return;
    }
    
    // 然后再执行回调
    if (widget.cancel != null) {
      debugPrint("Executing cancel callback");
      widget.cancel!();
    } else {
      debugPrint("No cancel callback provided");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: .6),
      child: Center(
        child: SingleChildScrollView(
          child: Container(
            width: 300,
            constraints: const BoxConstraints(
              minHeight: 186
            ),
            padding: const EdgeInsets.fromLTRB(18, 10, 18, 18),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: ThemeManager.currentTheme.backgroundColor,
              borderRadius: const BorderRadius.all(Radius.circular(10)),
            ),
            child: Center(
              child: Column(
                children: [
                  // 增加顶部间距
                  const SizedBox(height: 8),
                  Text(
                    widget.title,
                    style: TextStyle(
                      color: ThemeManager.currentTheme.textColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 18
                    ),
                  ),
                  const SizedBox(height: 15),
                  Container(
                    constraints: BoxConstraints(
                      minHeight: 55
                    ),
                    // height: 60,
                    alignment: Alignment.center,
                    padding: const EdgeInsets.only(left: 10, right: 10),
                    decoration: BoxDecoration(
                      color: widget.inputText == null ? ThemeManager.currentTheme.backgroundColor : Color(0xfff0f0f0), // 文本框背景
                      borderRadius: BorderRadius.circular(4)
                    ),
                    child: widget.inputText == null
                      ? Text.rich(
                          BreakAllTextSpan(
                            widget.content!,
                            TextStyle(
                              color: ThemeManager.currentTheme.lightTextColor,
                              fontSize: 13
                            )
                          ),
                        )
                      : 
                      TextField(
                          controller: _inputController,
                          decoration: const InputDecoration(
                            counterText: '',
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(vertical: 15),
                          ),
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: Color(0xff1b1c1e)
                          ),
                        ),
                  ),
                  const SizedBox(height: 20),
                  // 修改这里的按钮布局，增加左右边距
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 2),
                    child: Row(
                      mainAxisAlignment: widget.cancel != null
                        ? MainAxisAlignment.spaceBetween
                        : MainAxisAlignment.center,
                      children: [
                        if (widget.cancel != null)
                          _createButton(
                            widget.cancelTitle ?? AppLocalizations.of(context)!.cancel,
                            const Color(0xFFf0f0f0),
                            _handleCancel
                          ),
                        _createButton(
                          widget.submitTitle,
                          ThemeManager.currentTheme.logoColor,
                          _handleConfirm
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}