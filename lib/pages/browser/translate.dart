import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:imtrans/controllers/product_controller.dart';
import 'package:imtrans/models/product_model.dart';
import 'package:imtrans/util/theme_manager.dart';
import 'package:imtrans/widgets/text_overlay.dart';

class BrowserTranslatePreview extends StatefulWidget {
  final List<Uint8List> images;
  const BrowserTranslatePreview({Key? key, required this.images}) : super(key: key);

  @override
  State<BrowserTranslatePreview> createState() => _BrowserTranslatePreviewState();
}

class _BrowserTranslatePreviewState extends State<BrowserTranslatePreview> {
  // bool _isBulkTranslating = false;
  bool _showOrigin = false; // 添加显示原图的状态变量
  List<Product?> _translatedProducts = []; 
  // 与 widget.images 对应，翻译成功后存储 Product 对象(只含单张图即可)

  @override
  void initState() {
    super.initState();
    _translatedProducts = List.filled(widget.images.length, null, growable: false);
  }

  /// 对单张图片执行翻译
  Future<void> _translateOne(int index) async {
    if (_translatedProducts[index] != null) {
      // 已经翻译过，或者用户想重复翻译，可根据需求自行处理
      return;
    }
    try {
      final imageData = widget.images[index];
      // 拿到默认语言
      // 这里先硬编码成英语
      final targetLang = Product.DEFAULT_TARGET_LANG;

      final productController = ProductController();
      String? productId = await productController.create([imageData], targetLang);
      if (productId == null) {
        throw Exception("Create product failed");
      }
      final p = await productController.waitForTranslation(productId);
      if (!mounted) return;
      setState(() {
        _translatedProducts[index] = p;
      });
    } catch (e) {
      debugPrint("Translation error: $e");
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Translation failed: $e"))
      );
    }
  }

  /// 一次性翻译全部
  Future<void> _translateAll() async {
    setState(() {
      // _isBulkTranslating = true;
    });
    for (int i = 0; i < widget.images.length; i++) {
      await _translateOne(i);
    }
    setState(() {
      // _isBulkTranslating = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeManager.currentTheme.backgroundColor, 
      appBar: AppBar(
        // title: Text("Web Images Preview", style: TextStyle(color: global.labelBlack)),
        backgroundColor: ThemeManager.currentTheme.bottomBarBackgroundColor,
        iconTheme: IconThemeData(color: ThemeManager.currentTheme.textColor),
        // 移除默认的返回按钮
        automaticallyImplyLeading: false,
        // 添加自定义的返回按钮，与浏览器页面保持一致
        leading: IconButton(
          icon: Image.asset(ThemeManager.getImagePath('btn_back'), width: 40, height: 40),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          // 切换原图/翻译按钮
          IconButton(
            onPressed: () {
              setState(() {
                _showOrigin = !_showOrigin;
              });
            },
            icon: Image.asset(
              _showOrigin ? ThemeManager.getImagePath('img_open') : ThemeManager.getImagePath('img_close'), 
              width: 40, 
              height: 40
            ),
          ),
          // 一键翻译
          // if (!_isBulkTranslating)
          //   IconButton(
          //     onPressed: _translateAll,
          //     icon: Image.asset(global.image_translate, width: 24, height: 24), // 使用全局翻译图标
          //   ),
          // if (_isBulkTranslating)
          //   const Padding(
          //     padding: EdgeInsets.symmetric(horizontal: 18.0),
          //     child: Center(child: CircularProgressIndicator()),
          //   ),
        ],
      ),
      body: ListView.builder(
        itemCount: widget.images.length,
        itemBuilder: (_, index) {
          return Column(
            children: [
              GestureDetector(
                onTap: () => _translateOne(index),
                child: Container(
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  color: Colors.black12,
                  child: AspectRatio(
                    aspectRatio: 1.0,
                    child: _buildPreview(index),
                  ),
                ),
              ),
              const Divider(height: 1, color: Colors.grey),
            ],
          );
        },
      ),
    );
  }

  /// 如果已翻译，则以 TextOverlay 形式展示翻译结果，否则显示原图
  Widget _buildPreview(int index) {
    final p = _translatedProducts[index];
    
    // 显示原始图片的情况：
    // 1. 未翻译
    // 2. 已翻译但用户选择显示原图
    if (p == null || _showOrigin) {
      // 未翻译或显示原图
      return Image.memory(
        widget.images[index], 
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          debugPrint("图像显示错误: $error");
          return const Center(
            child: Icon(
              Icons.broken_image,
              size: 64,
              color: Colors.grey,
            ),
          );
        },
      );
    } else {
      // 已翻译且显示翻译结果 -> 显示翻译覆盖
      final imageData = widget.images[index];
      // 只考虑第一张图
      if (p.images.isEmpty) {
        return Image.memory(imageData, fit: BoxFit.contain);
      }

      final translateResult = p.images[0].translateResult ?? [];
      final imageUrl = p.images[0].url ?? "";
      Map<String, dynamic> caches = {
        "imageData": imageData,
      };

      // 直接复用 TextOverlay 实现
      return TextOverlay(
        imageUrl,
        translateResult,
        caches: caches,
        visible: true,
      );
    }
  }
}
