// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyByKjQsHKrKr4NZty_3FRVwxIehlPFTGHo',
    appId: '1:307763371993:ios:07e7835cee162c339003c8',
    messagingSenderId: '307763371993',
    projectId: 'ai-manga-7765e',
    storageBucket: 'ai-manga-7765e.firebasestorage.app',
    iosClientId: '307763371993-nvcmmp9kfln2gagbh3s3qu7i8m1kvvqu.apps.googleusercontent.com',
    iosBundleId: 'cc.littlegrass.mangaai',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyByKjQsHKrKr4NZty_3FRVwxIehlPFTGHo',
    appId: '1:307763371993:android:ANDROID_APP_ID_PLACEHOLDER',
    messagingSenderId: '307763371993',
    projectId: 'ai-manga-7765e',
    storageBucket: 'ai-manga-7765e.firebasestorage.app',
    androidClientId: '307763371993-nvcmmp9kfln2gagbh3s3qu7i8m1kvvqu.apps.googleusercontent.com',
  );
}
