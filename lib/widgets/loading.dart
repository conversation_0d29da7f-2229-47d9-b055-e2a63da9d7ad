import 'package:flutter/material.dart';
import 'package:imtrans/l10n/generated/app_localizations.dart';
import 'package:imtrans/util/theme_manager.dart';

class Loading extends StatefulWidget {
  final VoidCallback? onTimeoutCancel;
  
  const Loading({Key? key, this.onTimeoutCancel}) : super(key: key);
  
  @override
  State<Loading> createState() => LoadingState(); 
}

class LoadingState extends State<Loading> {
  bool _showCancelButton = false;
  
  void showTimeoutButton() {
    if (mounted) {
      setState(() {
        _showCancelButton = true;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return PopScope(
      // onWillPop: () async => false, // 禁止返回键关闭
      child: Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        insetPadding: EdgeInsets.zero, // 移除默认内边距
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: ThemeManager.currentTheme.textColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  // const SizedBox(height: 15),
                  if (_showCancelButton) ...[
                    const SizedBox(height: 15),
                    Text(AppLocalizations.of(context)!.operationTakingLong, 
                      style: const TextStyle(fontSize: 12, color: Colors.grey)),
                    const SizedBox(height: 10),
                    TextButton(
                      onPressed: widget.onTimeoutCancel,
                      child: Text(AppLocalizations.of(context)!.cancel),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
